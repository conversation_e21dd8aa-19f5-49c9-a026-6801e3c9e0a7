<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDeprTaskDetailWriteMapper">
    <insert id="insertDepraskDetail">
        INSERT INTO ecs_reim_depr_task_detail (
        task_id,
        dept_code,
        dept_name,
        asset_type_code,
        asset_type_name,
        source_code,
        source_name,
        open_year,
        open_date,
        amt,
        crter,
        create_time
        ) VALUES (
        #{taskId, jdbcType=INTEGER},
        #{deptCode, jdbcType=VARCHAR},
        #{deptName, jdbcType=VARCHAR},
        #{assetTypeCode, jdbcType=VARCHAR},
        #{assetTypeName, jdbcType=VARCHAR},
        #{sourceCode, jdbcType=VARCHAR},
        #{sourceName, jdbcType=VARCHAR},
        #{openYear, jdbcType=VARCHAR},
        #{openDate, jdbcType=VARCHAR},
        #{amt, jdbcType=DECIMAL},
        #{crter, jdbcType=VARCHAR},
        #{createTime, jdbcType=VARCHAR}
        )
    </insert>
</mapper>
