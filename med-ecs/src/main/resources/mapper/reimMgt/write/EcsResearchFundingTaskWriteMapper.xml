<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.write.EcsResearchFundingTaskWriteMapper">

    <insert id="insertResearchFundingTaskDetail">
        INSERT INTO ecs_research_funding_task_detail (task_id,
                                                   reim_abstract,
                                                   org_id,
                                                   reim_type,
                                                   reim_amt,
                                                   att)
        VALUES (#{taskId,jdbcType=INTEGER},
                #{reimAbstract,jdbcType=VARCHAR},
                #{orgId,jdbcType=VARCHAR},
                #{reimType,jdbcType=VARCHAR},
                #{reimAmt,jdbcType=DECIMAL},
                #{att,jdbcType=VARCHAR})
    </insert>
</mapper>
