<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimFileRecordWriteMapper">

    <insert id="insertFileRecord">
        INSERT INTO ecs_reim_file_record(
                                         att,
                                         att_name,
                                         type,
                                         att_code
        ) VALUES (
                  #{att,jdbcType=VARCHAR},
                  #{attName,jdbcType=VARCHAR},
                  #{type,jdbcType=VARCHAR},
                  #{attCode,jdbcType=VARCHAR}
                         )
    </insert>

</mapper>
