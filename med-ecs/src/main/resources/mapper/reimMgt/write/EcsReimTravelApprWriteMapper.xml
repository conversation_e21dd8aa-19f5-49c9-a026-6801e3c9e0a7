<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimTravelApprWriteMapper">

    <update id="resetTravelApprAuditNode">
        UPDATE ecs_audit_rcdfm
        SET chk_time = NULL,
            chk_remarks = NULL,
            chk_state = '0',
            chk_sign_path = NULL,
            chk_att_path = NULL,
            act_chker = NULL
        WHERE
            ID = #{apprId,jdbcType=INTEGER}
    </update>

    <update id="resetTravelApprAuditRes">
        UPDATE ecs_audit_res
        SET audit_res = '3'
        WHERE bchno = #{auditBchno,jdbcType=VARCHAR}
    </update>
</mapper>
