<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.write.EcsInvoRcdWriteMapper">

    <!-- 通过id更新状态 -->
    <update id="updateStateByIds">
        UPDATE ecs_invo_rcd
        <set>
            state = #{type,jdbcType=VARCHAR},
            <if test="business != null">
                invo_used_by = #{business,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <insert id="insertInvoChkRcd">
        INSERT INTO ecs_invo_chk_rcd (
            seller_unit_or_individual,
            invoice_type,
            saler_taxpayer_number,
            saler_address_or_phone,
            saler_bank_account,
            purchaser_name,
            check_code,
            cyjgxx,
            note,
            saler_name,
            invoice_money,
            invalid_mark,
            invoice_number,
            all_tax,
            all_valorem_tax,
            invoice_date,
            saler_bank_and_number,
            purchaser_taxpayer_number,
            invo_rcd_id,
            after_tax_code,
            blue_invoice_code,
            blue_invoice_no,
            brand_version,
            business_unit,
            business_unit_tax_no,
            busmess_unit_address,
            busmess_unit_bank_and_account,
            busmess_unit_phone,
            car_price,
            car_type,
            car_type_and_number,
            carframe_code,
            carrier_name,
            carrier_tax_no,
            code,
            consignor_name,
            consignor_tax_no,
            drawee_name,
            drawee_tax_no,
            engine_code,
            id_card,
            import_license,
            inspection_amount,
            inspection_number,
            invoice_code,
            lemon_market,
            lemon_market_address,
            lemon_market_bank_and_account,
            lemon_market_phone,
            lemon_market_tax_no,
            license_code,
            license_plate,
            limit_amount,
            machine_code,
            producing_area,
            purchaser_address_or_phone,
            purchaser_bank_and_number,
            purchaser_phone,
            purchaser_unit_or_individual,
            purchaser_unit_or_individual_address,
            purchaser_unitcode_or_idno,
            receive_name,
            receive_tax_no,
            registration_no,
            saler_address,
            saler_bank_name,
            saler_phone,
            seller_phone,
            seller_unit_code_or_idno,
            seller_unit_or_individual_address,
            tax_disk_number,
            tax_rate,
            tax_unit_code,
            tax_unit_name,
            through_address,
            traffic_fee_flag,
            transferred_vehicle_office,
            transport_goods_info,
            unit,
            vehicle_tonnage,
            zero_tax_rate_flag
        )
        VALUES (
            #{sellerUnitOrIndividual,jdbcType=VARCHAR},
            #{invoiceType,jdbcType=VARCHAR},
            #{salerTaxpayerNumber,jdbcType=VARCHAR},
            #{salerAddressOrPhone,jdbcType=VARCHAR},
            #{salerBankAccount,jdbcType=VARCHAR},
            #{purchaserName,jdbcType=VARCHAR},
            #{checkCode,jdbcType=VARCHAR},
                #{cyjgxx,jdbcType=VARCHAR},
                #{note,jdbcType=VARCHAR},
                #{salerName,jdbcType=VARCHAR},
                #{invoiceMoney,jdbcType=VARCHAR},
                #{invalidMark,jdbcType=VARCHAR},
                #{invoiceNumber,jdbcType=VARCHAR},
                #{allTax,jdbcType=VARCHAR},
                #{allValoremTax,jdbcType=VARCHAR},
                #{invoiceDate,jdbcType=VARCHAR},
                #{salerBankAndNumber,jdbcType=VARCHAR},
                #{purchaserTaxpayerNumber,jdbcType=VARCHAR},
                #{invoRcdId,jdbcType=INTEGER},
                #{afterTaxCode,jdbcType=VARCHAR},
                #{blueInvoiceCode,jdbcType=VARCHAR},
                #{blueInvoiceNo,jdbcType=VARCHAR},
                #{brandVersion,jdbcType=VARCHAR},
                #{businessUnit,jdbcType=VARCHAR},
                #{businessUnitTaxNo,jdbcType=VARCHAR},
                #{busmessUnitAddress,jdbcType=VARCHAR},
                #{busmessUnitBankAndAccount,jdbcType=VARCHAR},
                #{busmessUnitPhone,jdbcType=VARCHAR},
                #{carPrice,jdbcType=VARCHAR},
                #{carType,jdbcType=VARCHAR},
                #{carTypeAndNumber,jdbcType=VARCHAR},
                #{carframeCode,jdbcType=VARCHAR},
                #{carrierName,jdbcType=VARCHAR},
                #{carrierTaxNo,jdbcType=VARCHAR},
                #{code,jdbcType=VARCHAR},
                #{consignorName,jdbcType=VARCHAR},
                #{consignorTaxNo,jdbcType=VARCHAR},
                #{draweeName,jdbcType=VARCHAR},
                #{draweeTaxNo,jdbcType=VARCHAR},
                #{engineCode,jdbcType=VARCHAR},
                #{idCard,jdbcType=VARCHAR},
                #{importLicense,jdbcType=VARCHAR},
                #{inspectionAmount,jdbcType=VARCHAR},
                #{inspectionNumber,jdbcType=VARCHAR},
                #{invoiceCode,jdbcType=VARCHAR},
                #{lemonMarket,jdbcType=VARCHAR},
                #{lemonMarketAddress,jdbcType=VARCHAR},
                #{lemonMarketBankAndAccount,jdbcType=VARCHAR},
                #{lemonMarketPhone,jdbcType=VARCHAR},
                #{lemonMarketTaxNo,jdbcType=VARCHAR},
                #{licenseCode,jdbcType=VARCHAR},
                #{licensePlate,jdbcType=VARCHAR},
                #{limitAmount,jdbcType=VARCHAR},
                #{machineCode,jdbcType=VARCHAR},
                #{producingArea,jdbcType=VARCHAR},
                #{purchaserAddressOrPhone,jdbcType=VARCHAR},
                #{purchaserBankAndNumber,jdbcType=VARCHAR},
                #{purchaserPhone,jdbcType=VARCHAR},
                #{purchaserUnitOrIndividual,jdbcType=VARCHAR},
                #{purchaserUnitOrIndividualAddress,jdbcType=VARCHAR},
                #{purchaserUnitcodeOrIdno,jdbcType=VARCHAR},
                #{receiveName,jdbcType=VARCHAR},
                #{receiveTaxNo,jdbcType=VARCHAR},
                #{registrationNo,jdbcType=VARCHAR},
                #{salerAddress,jdbcType=VARCHAR},
                #{salerBankName,jdbcType=VARCHAR},
                #{salerPhone,jdbcType=VARCHAR},
                #{sellerPhone,jdbcType=VARCHAR},
                #{sellerUnitCodeOrIdno,jdbcType=VARCHAR},
                #{sellerUnitOrIndividualAddress,jdbcType=VARCHAR},
                #{taxDiskNumber,jdbcType=VARCHAR},
                #{taxRate,jdbcType=VARCHAR},
                #{taxUnitCode,jdbcType=VARCHAR},
                #{taxUnitName,jdbcType=VARCHAR},
                #{throughAddress,jdbcType=VARCHAR},
                #{trafficFeeFlag,jdbcType=VARCHAR},
                #{transferredVehicleOffice,jdbcType=VARCHAR},
                #{transportGoodsInfo,jdbcType=VARCHAR},
                #{unit,jdbcType=VARCHAR},
                #{vehicleTonnage,jdbcType=VARCHAR},
                #{zeroTaxRateFlag,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertInvoChkRcdDetail">
        INSERT INTO ecs_invo_chk_rcd_detail (
            all_tax,
            standard,
            row_no,
            tax_classify_code,
            net_value,
            num,
            traffic_date_start,
            type,
            tax_detail_amount,
            tax_rate,
            tax_unit_price,
            unit,
            plate_no,
            detail_no,
            detail_amount,
            goods_name,
            traffic_date_end,
            expense_item,
            invo_rcd_id
        )
        VALUES (
            #{allTax,jdbcType=VARCHAR},
                #{standard,jdbcType=VARCHAR},
                #{rowNo,jdbcType=VARCHAR},
                #{taxClassifyCode,jdbcType=VARCHAR},
                #{netValue,jdbcType=VARCHAR},
                #{num,jdbcType=VARCHAR},
                #{trafficDateStart,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR},
                #{taxDetailAmount,jdbcType=VARCHAR},
                #{taxRate,jdbcType=VARCHAR},
                #{taxUnitPrice,jdbcType=VARCHAR},
                #{unit,jdbcType=VARCHAR},
                #{plateNo,jdbcType=VARCHAR},
                #{detailNo,jdbcType=VARCHAR},
                #{detailAmount,jdbcType=VARCHAR},
                #{goodsName,jdbcType=VARCHAR},
                #{trafficDateEnd,jdbcType=VARCHAR},
                #{expenseItem,jdbcType=VARCHAR},
                #{invoRcdId,jdbcType=INTEGER}
        )
    </insert>




















</mapper>
