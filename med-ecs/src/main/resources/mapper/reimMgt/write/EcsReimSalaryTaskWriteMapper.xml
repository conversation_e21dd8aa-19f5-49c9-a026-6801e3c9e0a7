<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimSalaryTaskWriteMapper">
    <insert id="insertSalaryTaskDetail">
        INSERT INTO ecs_reim_salary_task_detail(task_id,
                                                org_id,
                                                reim_type,
                                                reim_amt,
                                                reim_desc,
                                                type,
                                                reim_name,
                                                emp_code,
                                                emp_type,
                                                emp_count)
        VALUES (#{taskId,jdbcType=INTEGER},
                #{orgId,jdbcType=VARCHAR},
                #{reimType,jdbcType=VARCHAR},
                #{reimAmt,jdbcType=DOUBLE},
                #{reimDesc,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR},
                #{reimName,jdbcType=VARCHAR},
                #{empCode,jdbcType=VARCHAR},
                #{empType,jdbcType=VARCHAR},
                #{empCount,jdbcType=INTEGER})
    </insert>
</mapper>
