<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDetailWriteMapper">

    <!-- 写入项目 -->
    <insert id="insertItemDetail">
        INSERT INTO ecs_reim_item_detail(
            reim_detail_id,dept_code,item,doc_num,days_or_kilor,std,amt,extra_amt,att,att_name,type,reim_abst,budget_code,invo_id,emp_code,reim_name,actig,rel_co,researcher_funding_apply_id
        )
        VALUES(
               #{reimDetailId,jdbcType=INTEGER},
               #{deptCode,jdbcType=VARCHAR},
               #{item,jdbcType=VARCHAR},
               #{docNum,jdbcType=INTEGER},
               #{daysOrKilor,jdbcType=DOUBLE},
               #{std,jdbcType=DOUBLE},
               #{amt,jdbcType=DOUBLE},
               #{extraAmt,jdbcType=DOUBLE},
               #{att,jdbcType=VARCHAR},
               #{attName,jdbcType=VARCHAR},
               #{type,jdbcType=VARCHAR},
               #{reimAbst,jdbcType=VARCHAR},
               #{budgetCode,jdbcType=VARCHAR},
               #{invoId,jdbcType=VARCHAR},
               #{empCode,jdbcType=VARCHAR},
               #{reimName,jdbcType=VARCHAR},
               #{actig,jdbcType=VARCHAR},
               #{relCo,jdbcType=VARCHAR},
               #{researcherFundingApplyId,jdbcType=INTEGER}
        )
    </insert>

    <!-- 更新合同项目信息 -->
    <update id="updateContractItems">
        UPDATE ecs_reim_item_detail
        SET
            type = #{type,jdbcType=VARCHAR},
            budget_code = #{budgetCode,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>
    <!-- 更新项目 -->
    <update id="updateItemDetail">
        UPDATE ecs_reim_item_detail
        SET
            days_or_kilor = #{daysOrKilor,jdbcType=DOUBLE},
            amt = #{amt,jdbcType=DOUBLE}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 写入补助项目 -->
    <insert id="insertSubsItemDetail">
        INSERT INTO ecs_reim_subs_item_detail(
            reim_detail_id,item,days_or_kilor,std,amt,att,att_name,invo_id
        )
        VALUES(
               #{reimDetailId,jdbcType=INTEGER},
               #{item,jdbcType=VARCHAR},
               #{daysOrKilor,jdbcType=DOUBLE},
               #{std,jdbcType=DOUBLE},
               #{amt,jdbcType=DOUBLE},
               #{att,jdbcType=VARCHAR},
               #{attName,jdbcType=VARCHAR},
               #{invoId,jdbcType=VARCHAR}
        )
    </insert>
    <!-- 更新补助项目 -->
    <update id="updateSubsItemDetail">
        UPDATE ecs_reim_subs_item_detail
        SET
            days_or_kilor = #{daysOrKilor,jdbcType=DOUBLE},
            amt = #{amt,jdbcType=DOUBLE}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>
    <!-- 更新随行人员金额 -->
    <update id="updatePsnDetail">
        UPDATE ecs_reim_psn_detail
        SET reim_amt =#{reimAmt,jdbcType=DOUBLE}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 写入报销人员 -->
    <insert id="insertPsnDetail">
        INSERT INTO ecs_reim_psn_detail(
            reim_detail_id,dept,trip_psn,reim_amt,type
        )
        VALUES(
               #{reimDetailId,jdbcType=INTEGER},
               #{dept,jdbcType=VARCHAR},
               #{tripPsn,jdbcType=VARCHAR},
               #{reimAmt,jdbcType=DOUBLE},
               #{type,jdbcType=VARCHAR}
       )
    </insert>

    <insert id="insertRelCoDetail">
        INSERT INTO ecs_reim_rel_co_detail (
           reim_detail_id, rel_co_code, rel_co_name, amt
        )
        VALUES (
                #{reimDetailId,jdbcType=INTEGER},
                #{relCoCode,jdbcType=VARCHAR},
                #{relCoName,jdbcType=VARCHAR},
                #{amt,jdbcType=DOUBLE}
        )
    </insert>

    <insert id="insertShareDetail">
        INSERT INTO ecs_reim_share_detail(
            reim_detail_id,
            share_type,
            dept_code,
            dept_name,
            amt,
            base,
            abs
        ) VALUES (
                  #{reimDetailId,jdbcType=INTEGER},
                  #{shareType,jdbcType=VARCHAR},
                  #{deptCode,jdbcType=VARCHAR},
                  #{deptName,jdbcType=VARCHAR},
                  #{amt,jdbcType=DOUBLE},
                  #{base,jdbcType=VARCHAR},
                  #{abs,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 写入辅项 -->
    <insert id="addReimAsst">
        INSERT INTO ecs_reim_asst_detail(
            reim_detail_id,
            pay_type_code,
            pay_type_name,
            actig_sub_code,
            actig_sub_name,
            actig_sys,
            dept_code,
            dept_name,
            rel_co_code,
            rel_co_name,
            fun_sub_code,
            fun_sub_name,
            econ_sub_code,
            econ_sub_name,
            proj_code,
            proj_name,
            cash_flow_code,
            cash_flow_name,
            actig_amt_type,
            actig_amt,
            crter,
            create_time,
            hospital_id,
            abst,
            sup_type,
            fund_type,
            fund_type_name
        )
        VALUES
        <foreach collection="reimAsstDetails" item="item" separator=",">
            (
             #{item.reimDetailId,jdbcType=INTEGER},
             #{item.payTypeCode,jdbcType=VARCHAR},
             #{item.payTypeName,jdbcType=VARCHAR},
             #{item.actigSubCode,jdbcType=VARCHAR},
             #{item.actigSubName,jdbcType=VARCHAR},
             #{item.actigSys,jdbcType=VARCHAR},
             #{item.deptCode,jdbcType=VARCHAR},
             #{item.deptName,jdbcType=VARCHAR},
             #{item.relCoCode,jdbcType=VARCHAR},
             #{item.relCoName,jdbcType=VARCHAR},
             #{item.funSubCode,jdbcType=VARCHAR},
             #{item.funSubName,jdbcType=VARCHAR},
             #{item.econSubCode,jdbcType=VARCHAR},
             #{item.econSubName,jdbcType=VARCHAR},
             #{item.projCode,jdbcType=VARCHAR},
             #{item.projName,jdbcType=VARCHAR},
             #{item.cashFlowCode,jdbcType=VARCHAR},
             #{item.cashFlowName,jdbcType=VARCHAR},
             #{item.actigAmtType,jdbcType=VARCHAR},
             #{item.actigAmt,jdbcType=VARCHAR},
             #{item.crter,jdbcType=VARCHAR},
             #{item.createTime,jdbcType=VARCHAR},
             #{item.hospitalId,jdbcType=VARCHAR},
             #{item.abst,jdbcType=VARCHAR},
             #{item.supType,jdbcType=VARCHAR},
            #{item.fundType,jdbcType=VARCHAR},
            #{item.fundTypeName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 删除随行人员 -->
    <delete id="deletePsnWithReimDetailId">
        DELETE FROM ecs_reim_psn_detail
        WHERE type = #{type,jdbcType=VARCHAR}
        AND reim_detail_id = #{reimId,jdbcType=INTEGER}
    </delete>

    <!-- 删除项目信息 -->
    <delete id="deleteReimItemWithReimId">
        DELETE FROM ecs_reim_item_detail
        WHERE reim_detail_id = #{reimId,jdbcType=INTEGER}
    </delete>

    <!-- 删除补助项目信息 -->
    <delete id="deleteReimSubItemWithReimId">
        DELETE FROM ecs_reim_subs_item_detail
        WHERE reim_detail_id = #{reimId,jdbcType=INTEGER}
    </delete>

    <update id="updateHrmResearcherFundingApplyStatus">
        update hrm_researcher_funding_apply
        set status = #{status} where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
                                   #{id}
        </foreach>
    </update>
</mapper>
