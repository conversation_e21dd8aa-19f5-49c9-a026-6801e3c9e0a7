<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsFundApplyReadMapper">

    <select id="queryList" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsFundApplyVo">
        select
            a.id as id,
            a.appyer as appyer,
            a.appyer_dept as appyerDept,
            a.appyer_time as appyerTime,
            a.appyer_rea as appyerRea,
            a.sum as sum,
            a.cap_sum as capSum,
            a.bank as bank,
            a.acctname as acctname,
            a.bankcode as bankcode,
            a.audit_bchno as auditBchno,
            a.crter as crter,
            a.hospital_id as hospitalId,
            case when position(#{chker,jdbcType=VARCHAR} in b.chker) > 0 then '1' else '0' end as auditFlag
        from ecs_fund_apply a
        left join (
            select *
            from (
                select bchno,chker,chk_seq,min(chk_seq)over(partition by bchno) as min_seq
                from ecs_audit_rcdfm
                where chk_time is null
            ) a
            where min_seq = chk_seq
        ) b
        on a.audit_bchno = b.bchno
    </select>

</mapper>
