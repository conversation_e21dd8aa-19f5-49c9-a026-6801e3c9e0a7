<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDeprTaskReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.vo.EcsReimDeprTaskVo" id="reimDeprTaskMap">
        <result property="id" column="id"/>
        <result property="launchDept" column="launch_dept"/>
        <result property="launchDate" column="launch_date"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="upder" column="upder"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.vo.EcsReimDeprTaskVo">
        select
            a.id as id,
            a.launch_dept as launchDept,
            a.launch_date as launchDate,
            a.reim_flag as reimFlag,
            c.org_name as launchDeptName,
            a.crter as crter,
            substring(a.create_time,1,10) as createTime,
            a.upder as upder,
            a.amt as amt,
            a.att_code as attCode,
            substring(a.update_time,1,10) as updateTime,
            b.emp_name as crterName
        from ecs_reim_depr_task a
        left join hrm_employee_info b on a.crter = b.emp_code
        left join hrm_org c on c.org_id = a.launch_dept
        <where>
            <if test="id != null">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="launchDept != null and launchDept != ''">
                and a.launch_dept = #{launchDept,jdbcType=VARCHAR}
            </if>
            <if test="launchDate != null and launchDate != ''">
                and a.launch_date = #{launchDate,jdbcType=VARCHAR}
            </if>
            <if test="reimFlag != null and reimFlag != ''">
                and a.reim_flag = #{reimFlag,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
