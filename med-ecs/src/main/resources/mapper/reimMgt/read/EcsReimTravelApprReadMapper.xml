<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimTravelApprReadMapper">

    <select id="queryList" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimTravelApprVo">
        select a.*
        from(
            select
                a.id as id,
                a.appyer_time as appyerTime,
                a.appyer as appyer,
                a.appr_dept as apprDept,
                a.evection_rea as evectionRea,
                a.evection_begn_time as evectionBegnTime,
                a.evection_end_time as evectionEndTime,
                a.evection_addr as evectionAddr,
                a.evection_detl_addr as evectionDetlAddr,
                a.detour_or_not as detourOrNot,
                a.kil as kil,
                a.trnp as trnp,
                a.trnp_num as trnpNum,
                a.food as food,
                a.stay as stay,
                a.prse as prse,
                a.plan_amt as planAmt,
                a.plan_amt2 as planAmt2,
                a.plan_amt3 as planAmt3,
                a.hospital_id as hospitalId,
                a."type" as type,
                a.att as att,
                a.att_name as attName,
                a.audit_bchno as auditBchno,
                a.page_image as pageImage,
                a.reim_flag as reimFlag,
                a.bus_met as busMet,
                a.appr_dept_type as apprDeptType,
                a.travel_range as travelRange,
                a.status as status,
                a.chker_flow as chkerFlow,
                a.self_drive_rea as selfDriveRea,
                c.emp_name as appyerName,
                b.engage_rank AS engageRank,
                case when position(#{chker,jdbcType=VARCHAR} in d.chker) > 0 then '1' else '0' end as auditFlag, <!-- 当前是否审核 -->
                case when position(#{chker,jdbcType=VARCHAR} in f.chker) > 0 then '1' else '0' end as tbAudit, <!-- 是否已经审核过 -->
                e.audit_res as auditState
            from ecs_reim_travel_appr a
            left join hrm_employee_info c
            on a.appyer = c.emp_code
            LEFT JOIN (
                    select * from (
                    select emp_id,id,engage_rank,max(id) over(partition by emp_id) max_id from hrm_engage_level
                    ) a
                    where max_id = id
            ) b ON C.ID = b.emp_id
            left join (
                select *
                from (
                    select bchno,chker,chk_seq,min(chk_seq)over(partition by bchno) as min_seq
                    from ecs_audit_rcdfm
                    where chk_time is null
                ) a
                where min_seq = chk_seq
            ) d
            on a.audit_bchno = d.bchno
            left join ecs_audit_res e
            on a.audit_bchno = e.bchno
            left join (
                select bchno, string_agg(chker, ',') as chker
                from ecs_audit_rcdfm
                where chk_time IS NOT NULL
                group by bchno
            ) f
            on a.audit_bchno = f.bchno
            <where>
                <if test="appyer != null and appyer != ''">
                    and a.appyer = #{appyer,jdbcType=VARCHAR}
                </if>
                <if test="apprDeptType != null and apprDeptType !=''">
                    and a.appr_dept_type = #{apprDeptType,jdbcType=VARCHAR}
                </if>
                <if test="audit != null and audit == true">
                    and exists (
                        select *
                        from (
                            select bchno, string_agg(chker, ',') as chkers
                            from ecs_audit_rcdfm b
                            where b.bchno = a.audit_bchno
                            group by bchno
                        ) a
                        where position(#{chker,jdbcType=VARCHAR} in chkers) > 0
                    )
                </if>
                <if test="auditBchno != null and auditBchno != ''">
                    and a.audit_bchno = #{auditBchno,jdbcType=VARCHAR}
                </if>
                <if test="type != null and type !=''">
                    and a.type = #{type,jdbcType=VARCHAR}
                </if>
                <if test="reimFlag != null and reimFlag != ''">
                    and a.reim_flag = #{reimFlag,jdbcType=VARCHAR}
                    <choose>
                        <!--dash;&gt; 如果是查询未报销，获取审核通过的申请记录 -->
                        <when test='reimFlag == "0"'>
                            and exists (
                            select *
                            from(
                            select string_agg(distinct b.chk_state, ',') as f
                            from ecs_audit_rcdfm b
                            where a.audit_bchno = b.bchno
                            group by a.id
                            ) c
                            where f = '1' <!-- 全部申请通过 -->
                            )
                        </when>
                    </choose>
                </if>
                <if test="id != null">
                    and a.id = #{id,jdbcType=INTEGER}
                </if>
            </where>
            order by a.appyer_time DESC
        ) a
        <where>
            a.auditBchno is not null
            <if test="auditFlag != null and auditFlag != ''">
                <choose>
                    <!-- 已审核 -->
                    <when test='auditFlag == "0"'>
                        and a.auditFlag = #{auditFlag,jdbcType=VARCHAR}
                        and a.tbAudit = '1'
                    </when>

                    <!-- 未审核 -->
                    <when test='auditFlag == "1"'>
                        and a.auditFlag = #{auditFlag,jdbcType=VARCHAR}
                    </when>

                    <!-- 待审核 -->
                    <when test='auditFlag == "2"'>
                        and a.tbAudit = '1'
                        and a.auditFlag = '0'
                    </when>
                </choose>
            </if>
            <if test="auditState != null and auditState.size() > 0">
                and a.auditState IN
                <foreach collection="auditState" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
            <if test="id !=null ">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryListNew" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimTravelApprVo">
        select a.* from (
        select
        a.id as id,
        a.appyer_time as appyerTime,
        a.appyer as appyer,
        a.appr_dept as apprDept,
        a.evection_rea as evectionRea,
        a.evection_begn_time as evectionBegnTime,
        a.evection_end_time as evectionEndTime,
        a.evection_addr as evectionAddr,
        a.evection_detl_addr as evectionDetlAddr,
        a.detour_or_not as detourOrNot,
        a.kil as kil,
        a.trnp as trnp,
        a.trnp_num as trnpNum,
        a.food as food,
        a.stay as stay,
        a.prse as prse,
        a.plan_amt as planAmt,
        a.plan_amt2 as planAmt2,
        a.plan_amt3 as planAmt3,
        a.hospital_id as hospitalId,
        a."type" as type,
        a.att as att,
        a.att_name as attName,
        a.audit_bchno as auditBchno,
        a.page_image as pageImage,
        a.reim_flag as reimFlag,
        a.bus_met as busMet,
        a.appr_dept_type as apprDeptType,
        a.travel_range as travelRange,
        a.status as status,
        a.chker_flow as chkerFlow,
        a.self_drive_rea as selfDriveRea,
        c.emp_name as appyerName,
        a.process_instance_id as processInstanceId,
        b.engage_rank AS engageRank,
        case when position(#{chker,jdbcType=VARCHAR} in d.chker) > 0 then '1' else '0' end as auditFlag, <!-- 当前是否审核 -->
        case when position(#{chker,jdbcType=VARCHAR} in f.chker) > 0 then '1' else '0' end as tbAudit, <!-- 是否已经审核过 -->
        G.text_ as auditState
        from ecs_reim_travel_appr a
        left join hrm_employee_info c
        on a.appyer = c.emp_code
        LEFT JOIN (
        select * from (
        select emp_id,id,engage_rank,max(id) over(partition by emp_id) max_id from hrm_engage_level
        ) a
        where max_id = id
        ) b ON C.ID = b.emp_id
        LEFT JOIN act_hi_varinst G <!--审核实例状态  1 审核中 2 审核成功 3 审核失败-->
        ON A.process_instance_id = G.proc_inst_id_
        AND G.NAME_ = 'PROCESS_STATUS'
        LEFT JOIN ( SELECT proc_inst_id_,assignee_ as chker FROM act_hi_taskinst h <!--任务审核历史-->
        WHERE h.rev_ = '1' AND h.end_time_ IS NULL ) d ON d.proc_inst_id_ = A.process_instance_id
        LEFT JOIN ( SELECT proc_inst_id_, string_agg ( assignee_, ',' ) as chker
        FROM act_hi_taskinst j
        WHERE j.end_time_ IS NOT NULL GROUP BY proc_inst_id_ ) f
        ON f.proc_inst_id_ = A.process_instance_id
        <where>
            <if test="appyer != null and appyer != ''">
                and a.appyer = #{appyer,jdbcType=VARCHAR}
            </if>
            <if test="apprDeptType != null and apprDeptType !=''">
                and a.appr_dept_type = #{apprDeptType,jdbcType=VARCHAR}
            </if>
            <if test="audit != null and audit == true">
                and exists (
                select * from (
                select b.proc_inst_id_,string_agg(assignee_,',') as chkers from act_hi_taskinst b
                where b.proc_inst_id_ = a.process_instance_id
                group by b.proc_inst_id_
                ) a
                where position(#{chker,jdbcType=VARCHAR} in chkers) > 0
                )
            </if>
            <!--<if test="auditBchno != null and auditBchno != ''">
                and a.audit_bchno = #{auditBchno,jdbcType=VARCHAR}
            </if>-->
            <if test="type != null and type !=''">
                and a.type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="reimFlag != null and reimFlag != ''">
                and a.reim_flag = #{reimFlag,jdbcType=VARCHAR}
                <choose>
                    <!-- 如果是查询未报销，获取审核通过的申请记录 -->
                    <when test='reimFlag == "0"'>
                        and g.text_ = '2'
                    </when>
                </choose>
            </if>
        </where>
        order by a.appyer_time DESC
        ) a
        <where>
            a.auditBchno is null
            <if test="auditFlag != null and auditFlag != ''">
                <choose>
                    <!-- 已审核 -->
                    <when test='auditFlag == "0"'>
                        and a.auditFlag = '0'
                        and a.tbAudit = '1'
                    </when>

                    <!-- 未审核 -->
                    <when test='auditFlag == "1"'>
                        and a.auditFlag = '1'
                    </when>

                    <!-- 待审核 -->
                    <when test='auditFlag == "2"'>
                        and a.tbAudit = '1'
                        and a.auditFlag = '0'
                    </when>
                </choose>
            </if>
            <if test="auditState != null and auditState.size() > 0">
                and ((a.status is not null and a.status in
                <foreach collection="auditState" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
                ) or (a.status is null and a.auditState IN
                <foreach collection="auditState" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>))
            </if>
            <if test="id !=null ">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="ids !=null  and ids.size() > 0">
                   and a.id in
                <foreach collection="ids" item="idd" open="(" close=")" separator=",">
                    #{idd}
                </foreach>
            </if>
            <if test="processInstanceId !=null ">
                and a.processInstanceId = #{processInstanceId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="getFirstAudit" resultType="com.jp.med.common.entity.audit.AuditDetail">
        SELECT a.id,
               a.bchno,
               a.chker,
               a.chk_dept as chkDept,
               a.chk_time as chkTime,
               a.chk_remarks as chkRemarks,
               a.chk_sign as chkSign,
               a.chk_att as chkAtt,
               a.chk_seq as chkSeq,
               a.chk_state as chkState,
               a.chk_sign_path as chkSignPath,
               a.chk_att_path as chkAttPath,
               a.dscr,
               a.message_id as messageId,
               a.message_sup as messageSup,
               a.message_payload as messagePayload
        FROM ecs_audit_rcdfm a
        LEFT JOIN ecs_reim_travel_appr b
        ON a.bchno = b.audit_bchno
        WHERE a.chk_seq = 1
        AND b."id" = #{id,jdbcType=INTEGER}
    </select>

    <select id="queryNextAuditedNode" resultType="com.jp.med.common.entity.audit.AuditDetail">
        SELECT
            A.ID,
            A.bchno,
            A.chker,
            A.chk_dept AS chkDept,
            A.chk_time AS chkTime,
            A.chk_remarks AS chkRemarks,
            A.chk_sign AS chkSign,
            A.chk_att AS chkAtt,
            A.chk_seq AS chkSeq,
            A.chk_state AS chkState,
            A.chk_sign_path AS chkSignPath,
            A.chk_att_path AS chkAttPath,
            A.dscr,
            A.message_id AS messageId,
            A.message_sup AS messageSup,
            A.message_payload AS messagePayload
        FROM
            ecs_audit_rcdfm A
        WHERE
            A.bchno = #{auditBchno,jdbcType=VARCHAR}
          AND A.chk_seq >= ( SELECT MAX ( chk_seq ) FROM ecs_audit_rcdfm WHERE bchno = #{auditBchno,jdbcType=VARCHAR} AND chker = #{chker,jdbcType=VARCHAR} AND chk_time IS NOT NULL )
          AND A.chk_time IS NOT NULL
          ORDER BY A.chk_seq
    </select>

    <select id="queryReimWithAppr" resultType="java.lang.Integer">
        SELECT COUNT
                   ( 1 )
        FROM
            ecs_reim_detail A
        WHERE
            TYPE IN ( '1', '2' )
          AND travel_appr_id = ( SELECT ID FROM ecs_reim_travel_appr WHERE audit_bchno = #{auditBchno,jdbcType=VARCHAR} )
    </select>
</mapper>
