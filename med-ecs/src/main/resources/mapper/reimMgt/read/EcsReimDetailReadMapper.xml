<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDetailReadMapper">

    <select id="queryList" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo">
        select a.*
        from(
        select a.id                  as id,
               a.appyer              as appyer,
               a.evection_begn_time  as evectionBegnTime,
               a.evection_end_time   as evectionEndTime,
               a.appyer_time         as appyerTime,
               a.budg_ctrl           as budgCtrl,
               a.evection_addr       as evectionAddr,
               a.evection_rea        as evectionRea,
               a.self_drive_rea      as selfDriveRea,
               a.bank                as bank,
               a.acctname            as acctname,
               a.bankcode            as bankcode,
               a."sum"               as "sum",
               a.cap_sum             as capSum,
               a.audit_bchno         as auditBchno,
               a.bus<PERSON>s             as busstas,
               a.crter               as crter,
               a.hospital_id         as hospitalId,
               a."type"              as type,
               a.share_type          as shareType,
               a.bus_met             as busMet,
               a.chker_flow          as chkerFlow,
               a.share_amt           as shareAmt,
               a.share_date          as shareDate,
               a.att_code            as attCode,
               a.fund_type           as fundType,
               a.appyer_dept         as appyerDept,
               a.travel_appr_id      as travelApprId,
               a.project_id          as projectId,
               a.funding_id          as fundingId,
               a.pay_method          as payMethod,
                a.is_loan            as isLoan,
                a.loan_reim_id       as loanReimId,
                a.loan_amt           as loanAmt,
                a.pay_rcpt_id        as payRcptId,
                a.has_pz             as hasPz,
               a.page_image          as pageImage,
               a.att                 as att,
               a.att_name            as attName,
               a.invo_id             as invoId,
               a.opposite_name       as oppositeName,
               a.process_instance_id as processInstanceId,
        <if test="type != null and type != ''">
            <choose>
                <when test='type == "1" or type == "2"'>
                    m.appr_dept_type as apprDeptType,
                </when>
                <when test='type == "5"'>
                    m.ff_mth     as ffMth,
                    m.num        as num,
                    m.should_pay as shouldPay,
                    m.reduce_pay as reducePay,
                    m.real_pay   as realPay,
                    m.remark     as remark,
                </when>
                <when test='type == "6"'>
                    m.appyer            as conAppyer,
                    m.appyer_dept       as conAppyerDept,
                    m.ct_code           as ctCode,
                    m.ct_name           as ctName,
                    m.ct_unified_code   as ctUnifiedCode,
                    m.type_code         as typeCode,
                    m.contract_att      as contractAtt,
                    m.contract_att_name as contractAttName,
                    m.total_amt         as totalAmt,
                    m.stage             as stage,
                    m.contract_id       as contractId,
                    m.proportion        as proportion,
                    m.payment_time      as paymentTime,
                    m.payment_id        as paymentId,
                    m.payment_type      as paymentType,
                    m.need_reim_amt     as needReimAmt,
                </when>
                <when test='type == "8"'></when>
                <!-- 科研 -->
                <when test='type == "9"'>
                    m.appyer            as rfAppyer,
                    m.appyer_dept       as rfAppyerDept,
                    m.project_id        as projectId,
                    m.project_name      as projectName,
                    m.project_leader    as projectLeader,
                    m.project_level     as projectLevel,
                    m.topic_category    as topicCategory,
                    m.reim_amt          as reimAmt,
                    m.schedule_pay_time as schedulePayTime,
                </when>
            </choose>
        </if>
        <if test="apprDeptType != null and apprDeptType != '' and (type == null or type == '')">
            m.appr_dept_type as apprDeptType,
        </if>
        b.org_name as appyerDeptName,
        c.emp_name as appyerName,
        <!--查询申请信息 -->
        N.food                                                                             as food,
        N.stay                                                                             as stay,
        N.trnp                                                                             as trnp,
        case when position(#{chker,jdbcType=VARCHAR} in d.chker) > 0 then '1' else '0' end as auditFlag, <!-- 当前是否审核 -->
        case when position(#{chker,jdbcType=VARCHAR} in f.chker) > 0 then '1' else '0' end as tbAudit, <!-- 是否是待审核人员 -->
        e.audit_res as auditState
        from ecs_reim_detail a
            left join hrm_org b
                      on a.appyer_dept = b.org_id
            LEFT JOIN ecs_reim_travel_appr N ON A.travel_appr_id = N."id"
            left join hrm_employee_info c
                      on a.appyer = c.emp_code
            left join (select *
                       from (select bchno, chker, chk_seq, min(chk_seq) over (partition by bchno) as min_seq
                             from ecs_audit_rcdfm
                             where chk_time is null) a
                       where min_seq = chk_seq) d
                      on a.audit_bchno = d.bchno
            left join ecs_audit_res e
                      on a.audit_bchno = e.bchno
            left join (select bchno, string_agg(chker, ',') as chker
                       from ecs_audit_rcdfm
                       where chk_time is not null
                       group by bchno) f
                      on a.audit_bchno = f.bchno
        <if test="type != null and type != ''">
            <choose>
                <when test='type == "1" or type == "2"'>
                    LEFT JOIN ecs_reim_travel_appr m on a.travel_appr_id = m."id"
                </when>
                <when test='type == "5"'>
                    LEFT JOIN ecs_reim_salary_task m on a.travel_appr_id = m.id
                </when>
                <when test='type == "6"'>
                    LEFT JOIN ecs_reim_contract_task m on a.travel_appr_id = m.id
                </when>
                <when test='type == "8"'>

                </when>
                <when test='type == "9"'>
                    LEFT JOIN ecs_research_funding_task m on a.travel_appr_id = m.id
                </when>
            </choose>
        </if>
        <if test="apprDeptType != null and apprDeptType != '' and (type == null or type == '')">
            LEFT JOIN ecs_reim_travel_appr m on a.travel_appr_id = m."id"
        </if>
        <where>
            <if test="auditBchno != null and auditBchno != ''">
                and a.audit_bchno = #{auditBchno,jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="appyer != null and appyer != ''">
                and a.appyer = #{appyer,jdbcType=VARCHAR}
            </if>
            <if test="audit != null and audit == true">
                and exists (select *
                            from (select bchno, string_agg(chker, ',') as chkers
                                  from ecs_audit_rcdfm b
                                  where b.bchno = a.audit_bchno
                                  group by bchno) a
                            where position(#{chker,jdbcType=VARCHAR} in chkers) > 0)
            </if>
        </where>
        order by a.appyer_time desc
        ) a
        <where>
            a.auditBchno is not null
            <if test="auditFlag != null and auditFlag != ''">
                <choose>
                    <!-- 已审核 -->
                    <when test='auditFlag == "0"'>
                        and a.auditFlag = #{auditFlag,jdbcType=VARCHAR}
                        and a.tbAudit = '1'
                    </when>

                    <!-- 未审核 -->
                    <when test='auditFlag == "1"'>
                        and a.auditFlag = #{auditFlag,jdbcType=VARCHAR}
                    </when>

                    <!-- 待审核 -->
                    <when test='auditFlag == "2"'>
                        and a.tbAudit = '1'
                        and a.auditFlag = '0'
                    </when>
                </choose>
            </if>
            <if test="auditState != null and auditState.size() > 0">
                and a.auditState IN
                <foreach collection="auditState" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
            <if test="apprDeptType != null and apprDeptType != '' and (type == null or type == '')">
                and a.apprDeptType = #{apprDeptType,jdbcType=VARCHAR}
            </if>
            <if test="busstas != null and busstas != ''">
                and a.busstas = #{busstas,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryListNew" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo">
        select a.*
        from(
        select a.id                  as id,
               a.appyer              as appyer,
               a.evection_begn_time  as evectionBegnTime,
               a.evection_end_time   as evectionEndTime,
               a.appyer_time         as appyerTime,
               a.budg_ctrl           as budgCtrl,
               a.evection_addr       as evectionAddr,
               a.evection_rea        as evectionRea,
               a.self_drive_rea      as selfDriveRea,
               a.bank                as bank,
               a.acctname            as acctname,
               a.bankcode            as bankcode,
               a."sum"               as "sum",
               a.cap_sum             as capSum,
               a.audit_bchno         as auditBchno,
               a.busstas             as busstas,
               a.crter               as crter,
               a.hospital_id         as hospitalId,
               a."type"              as type,
               a.share_type          as shareType,
               a.bus_met             as busMet,
               a.chker_flow          as chkerFlow,
               a.share_amt           as shareAmt,
               a.share_date          as shareDate,
               a.att_code            as attCode,
               a.fund_type           as fundType,
               a.appyer_dept         as appyerDept,
               a.travel_appr_id      as travelApprId,
               a.project_id          as projectId,
               a.funding_id          as fundingId,
                a.pay_method          as payMethod,
                a.is_loan            as isLoan,
                a.loan_reim_id       as loanReimId,
                a.loan_amt           as loanAmt,
                a.pay_rcpt_id        as payRcptId,
                a.has_pz             as hasPz,
               a.page_image          as pageImage,
               a.att                 as att,
               a.att_name            as attName,
               a.invo_id             as invoId,
               a.opposite_name       as oppositeName,
               a.process_instance_id as processInstanceId,
        <if test="type != null and type != ''">
            <choose>
                <when test='type == "1" or type == "2"'>
                    m.appr_dept_type as apprDeptType,
                </when>
                <when test='type == "3" or type == "11"'>
                    m.itemKeyWord,
                </when>
                <when test='type == "5"'>
                    m.ff_mth     as ffMth,
                    m.num        as num,
                    m.should_pay as shouldPay,
                    m.reduce_pay as reducePay,
                    m.real_pay   as realPay,
                    m.remark     as remark,
                </when>
                <when test='type == "6"'>
                    m.appyer            as conAppyer,
                    m.appyer_dept       as conAppyerDept,
                    m.ct_code           as ctCode,
                    m.ct_name           as ctName,
                    m.ct_unified_code   as ctUnifiedCode,
                    m.type_code         as typeCode,
                    m.contract_att      as contractAtt,
                    m.contract_att_name as contractAttName,
                    m.total_amt         as totalAmt,
                    m.stage             as stage,
                    m.contract_id       as contractId,
                    m.proportion        as proportion,
                    m.payment_time      as paymentTime,
                    m.payment_id        as paymentId,
                    m.payment_type      as paymentType,
                    m.need_reim_amt     as needReimAmt,
                </when>
                <when test='type == "8"'></when>
            </choose>
        </if>
        <if test="apprDeptType != null and apprDeptType != '' and (type == null or type == '')">
            m.appr_dept_type as apprDeptType,
        </if>
        b.org_name as appyerDeptName,
        c.emp_name as appyerName,
        <!--查询申请信息 -->
        N.food                                                                             as food,
        N.stay                                                                             as stay,
        N.trnp                                                                             as trnp,
        case when position(#{chker,jdbcType=VARCHAR} in d.chker) > 0 then '1' else '0' end as auditFlag, <!-- 当前是否审核 -->
        case when position(#{chker,jdbcType=VARCHAR} in f.chker) > 0 then '1' else '0' end as tbAudit, <!-- 是否是待审核人员 -->
        <!--e.audit_res as auditState-->
        G.text_ as auditState
        from ecs_reim_detail a
            left join hrm_org b
                      on a.appyer_dept = b.org_id
            LEFT JOIN ecs_reim_travel_appr N ON A.travel_appr_id = N."id"
            left join hrm_employee_info c
                      on a.appyer = c.emp_code
            LEFT JOIN act_hi_varinst G <!--审核实例状态  1 审核中 2 审核成功 3 审核失败-->
        ON A.process_instance_id = G.proc_inst_id_
            AND G.NAME_ = 'PROCESS_STATUS'
            LEFT JOIN ( SELECT proc_inst_id_, assignee_ as chker
                        FROM act_hi_taskinst h <!--任务审核历史-->
        WHERE h.rev_ = '1'
          AND h.end_time_ IS NULL ) d ON d.proc_inst_id_ = A.process_instance_id
            LEFT JOIN (SELECT proc_inst_id_, string_agg(assignee_, ',') as chker
                       FROM act_hi_taskinst j
                       WHERE j.end_time_ IS NOT NULL
                       GROUP BY proc_inst_id_) f
                      ON f.proc_inst_id_ = A.process_instance_id
        <if test="type != null and type != ''">
            <choose>
                <when test='type == "1" or type == "2"'>
                    LEFT JOIN ecs_reim_travel_appr m on a.travel_appr_id = m."id"
                </when>
                <when test='type == "3" or type == "11"'>
                    LEFT JOIN (
                        select reim_detail_id,string_agg(reim_abst,',') as itemKeyWord from ecs_reim_item_detail where reim_abst is not null and reim_name is null  group by reim_detail_id
                    ) m on a.id = m.reim_detail_id
                </when>
                <when test='type == "5"'>
                    LEFT JOIN ecs_reim_salary_task m on a.travel_appr_id = m.id
                </when>
                <when test='type == "6"'>
                    LEFT JOIN ecs_reim_contract_task m on a.travel_appr_id = m.id
                </when>
                <when test='type == "8"'>

                </when>
            </choose>
        </if>
        <if test="apprDeptType != null and apprDeptType != '' and (type == null or type == '')">
            LEFT JOIN ecs_reim_travel_appr m on a.travel_appr_id = m."id"
        </if>
        <where>
            <!--<if test="auditBchno != null and auditBchno != ''">
                and a.audit_bchno = #{auditBchno,jdbcType=VARCHAR}
            </if>-->
            <if test="type != null and type != ''">
                and a.type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="busstas != null and busstas != ''">
                and a.busstas = #{busstas,jdbcType=VARCHAR}
            </if>
            <if test="appyer != null and appyer != ''">
                and a.appyer = #{appyer,jdbcType=VARCHAR}
            </if>
            <if test="hasPz != null and hasPz != ''">
                and a.has_pz = #{hasPz,jdbcType=VARCHAR}
            </if>
            <if test="audit != null and audit == true">
                and exists (select *
                            from (select b.proc_inst_id_, string_agg(assignee_, ',') as chkers
                                  from act_hi_taskinst b
                                  where b.proc_inst_id_ = a.process_instance_id
                                  group by b.proc_inst_id_) a
                            where position(#{chker,jdbcType=VARCHAR} in chkers) > 0)
            </if>
        </where>
        order by a.appyer_time desc
        ) a
        <where>
            a.auditBchno is null
            <if test="auditFlag != null and auditFlag != ''">
                <choose>
                    <!-- 已审核 -->
                    <when test='auditFlag == "0"'>
                        and a.auditFlag = '0'
                        and a.tbAudit = '1'
                    </when>

                    <!-- 未审核 -->
                    <when test='auditFlag == "1"'>
                        and a.auditFlag = '1'
                    </when>

                    <!-- 待审核 -->
                    <when test='auditFlag == "2"'>
                        and a.tbAudit = '1'
                        and a.auditFlag = '0'
                    </when>
                </choose>
            </if>
            <if test="auditState != null and auditState.size() > 0">
                and ((a.busstas is not null and a.busstas in
                <foreach collection="auditState" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
                ) or (a.busstas is null and a.auditState IN ('1', '2', '3')))
            </if>
            <if test="apprDeptType != null and apprDeptType != '' and (type == null or type == '')">
                and a.apprDeptType = #{apprDeptType,jdbcType=VARCHAR}
            </if>
            <if test="itemKeyWord != null and itemKeyWord != ''">
                and a.itemKeyWord like concat('%',#{itemKeyWord,jdbcType=VARCHAR},'%')
            </if>
            <if test="id != null">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="ids !=null  and ids.size() > 0">
                and a.id in
                <foreach collection="ids" item="idd" open="(" close=")" separator=",">
                    #{idd}
                </foreach>
            </if>
            <if test="processInstanceId !=null ">
                and a.processInstanceId = #{processInstanceId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <!-- 查询项目详情 -->
    <select id="queryItemDetail"
            resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsReimItemDetail">
        select a.id,
               a.reim_detail_id as reimDetailId,
               a.dept_code      as deptCode,
               c.org_name       as deptName,
               a.item,
               a.doc_num        as docNum,
               a.amt,
               a.extra_amt      as extraAmt,
               a.att,
               a.att_name       as attName,
               a.days_or_kilor  as daysOrKilor,
               a.std            as std,
               a.type,
               a.reim_abst      as reimAbst,
               a.budget_code    as budgetCode,
               a.invo_id        as invoId,
               a.emp_code       as empCode,
               a.reim_name      as reimName,
               a.actig as actig,
               a.rel_co as relCo,
               a.researcher_funding_apply_id as researcherFundingApplyId,
               b.sub_name       as subName,
               b.bgt_summary    as bgtSummary
        from ecs_reim_item_detail a
                 left join ecs_econ_fun_sub_cfg b
                           on a.type = b.sub_code and b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
                 left join hrm_org c
                           on a.dept_code = c.org_id
        <where>
            <if test="id != null">
                a.reim_detail_id = #{id,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <!-- 查询补助项目详情 -->
    <select id="querySubsItemDetail"
            resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsReimSubsItemDetail">
        select a.id,
               a.reim_detail_id as reimDetailId,
               a.item,
               a.days_or_kilor  as daysOrKilor,
               a.std,
               a.amt,
               a.att,
               a.att_name       as attName,
               a.invo_id        as invoId
        from ecs_reim_subs_item_detail a
        <where>
            <if test="id != null">
                a.reim_detail_id = #{id,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <!-- 查询报销人员详情 -->
    <select id="queryPsnDetail" resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsReimPsnDetail">
        select a.id,
               a.reim_detail_id,
               a.dept,
               a.trip_psn,
               a.reim_amt,
               a.type,
               b.org_name as deptName,
               c.emp_name as tripPsnName,
               c.sex
        from ecs_reim_psn_detail a
                 LEFT JOIN hrm_org b
                           on a.dept = b.org_id
                 left join hrm_employee_info c
                           on a.trip_psn = c.emp_code
        <where>
            <if test="id != null">
                AND a.reim_detail_id = #{id,jdbcType=INTEGER}
            </if>
            <if test="type != null and type != ''">
                AND a.type = #{type,jdbcType=INTEGER}
            </if>
            <if test="ecsReimTravelApprIds!=null and ecsReimTravelApprIds.size() != 0">
                AND a.reim_detail_id in
                <foreach collection="ecsReimTravelApprIds" item="idd" open="(" close=")" separator=",">
                    #{idd}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询科室已报销金额 -->
    <select id="queryDeptAmt" resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsReimPsnDetail">
        <choose>
            <when test='type == "1" or type == "2"'>
                WITH combined_data AS (
                    SELECT a.id, b.amt
                    FROM ecs_reim_detail a
                    LEFT JOIN ecs_reim_item_detail b
                        ON a.id = b.reim_detail_id
                        AND b.item &lt;&gt; '租车费'
                    WHERE a.busstas = '1'
                        and SUBSTR( A.appyer_time, 1, 4 ) = TO_CHAR( CURRENT_DATE, 'YYYY' )
                        AND a.type = '1'  -- 替换为实际参数

                    UNION ALL

                    SELECT a.id, b.amt
                    FROM ecs_reim_detail a
                    LEFT JOIN ecs_reim_subs_item_detail b
                        ON a.id = b.reim_detail_id
                    WHERE a.busstas = '1'
                        AND SUBSTR( A.appyer_time, 1, 4 ) = TO_CHAR( CURRENT_DATE, 'YYYY' )
                        AND a.type = '1'  -- 替换为实际参数
                ),
                aggregated_ids AS (
                    SELECT id, SUM(amt) AS amt
                    FROM combined_data
                    GROUP BY id
                ),
                joined_psn AS (
                    SELECT
                        ai.id,
                        ai.amt,
                        c.dept,
                        COUNT(c.dept) OVER (PARTITION BY ai.id) AS tt
                    FROM aggregated_ids ai
                    INNER JOIN ecs_reim_psn_detail c
                        ON ai.id = c.reim_detail_id
                        AND c.type = '2'
                    WHERE c.dept IS NOT NULL
                )
                SELECT
                    dept,
                    SUM(ROUND(amt / NULLIF(tt, 0)::NUMERIC, 6)) AS reimAmt,
                    'zjxrmyy' AS hospitalId
                FROM joined_psn
                GROUP BY dept
<!--                select f.dept,sum(f.reimAmt) as reimAmt,'zjxrmyy' as hospitalId from (-->
<!--                select e.dept, ROUND( ( e.amt / NULLIF ( e.tt, 0 ) ) * e.ttt, 6 ) AS reimAmt from (-->
<!--                select d.id,d.amt,d.dept,d.tt,count(d.dept) ttt from (-->

<!--                select n.id,n.amt,c.dept,count(n.id) over (partition by n.id) tt from (-->
<!--                select m.id,sum(m.amt) amt from (-->
<!--                select a.id,b.amt from ecs_reim_detail a-->
<!--                left join ecs_reim_item_detail b on a.id = b.reim_detail_id and b.item != '租车费'-->
<!--                where a.busstas = '1' and SUBSTR( A.appyer_time, 1, 4 ) = TO_CHAR( CURRENT_DATE, 'YYYY' ) and a.type = #{type,jdbcType=VARCHAR}-->

<!--                union all-->

<!--                select a.id,b.amt from ecs_reim_detail a-->
<!--                left join ecs_reim_subs_item_detail b on a.id = b.reim_detail_id-->
<!--                where a.busstas = '1' and SUBSTR( A.appyer_time, 1, 4 ) = TO_CHAR( CURRENT_DATE, 'YYYY' ) and a.type = #{type,jdbcType=VARCHAR}-->
<!--                ) m-->
<!--                group by m.id-->
<!--                order by m.id desc) n-->
<!--                left join ecs_reim_psn_detail c on n.id = c.reim_detail_id and c.type = '2') d-->
<!--                group by d.id,d.amt,d.dept,d.tt) e) f-->
<!--                group by dept-->

<!--                SELECT c.*-->
<!--                FROM (SELECT b.dept,-->
<!--                             SUM(b.reim_amt) AS reimAmt,-->
<!--                             a.hospital_id   AS hospitalId-->
<!--                      FROM ecs_reim_detail a-->
<!--                               LEFT JOIN-->
<!--                           ecs_reim_psn_detail b ON a.id = b.reim_detail_id-->
<!--                      WHERE SUBSTR(appyer_time, 1, 4) = TO_CHAR(CURRENT_DATE, 'YYYY')-->
<!--                        AND a."type" = #{type,jdbcType=VARCHAR}-->
<!--                        AND b."type" = '2'-->
<!--                        AND A.busstas = '1'-->
<!--                      GROUP BY b.dept, a.hospital_id) c-->
<!--                WHERE c.dept IS NOT NULL-->
            </when>
            <otherwise>
                SELECT c.*
                FROM (SELECT b.dept_code   AS dept,
                             SUM(b.amt)    AS reimAmt,
                             b."type"      AS type,
                             a.hospital_id AS hospitalId
                      FROM ecs_reim_detail a
                               LEFT JOIN
                           ecs_reim_item_detail b ON b.reim_detail_id = a.id
                      WHERE SUBSTR(a.appyer_time, 1, 4) = TO_CHAR(CURRENT_DATE, 'YYYY')
                        AND a."type" = #{type,jdbcType=VARCHAR}
                        AND A.busstas = '1'
                      GROUP BY b.dept_code, b."type", a.hospital_id) c
                WHERE c.dept IS NOT NULL
            </otherwise>
        </choose>
    </select>

    <select id="queryZCFDeptAmt" resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsReimPsnDetail">
        WITH filtered_data AS (
            SELECT
                A.ID,
                B.amt,
                C.dept,
                COUNT(A.ID) OVER (PARTITION BY A.ID) AS tt
            FROM
                ecs_reim_detail A
            INNER JOIN
                ecs_reim_item_detail B
                ON A.ID = B.reim_detail_id
                AND B.item = '租车费'
            INNER JOIN
                ecs_reim_psn_detail C
                ON A.ID = C.reim_detail_id
                AND C.type = '2'
            WHERE
                A.busstas = '1'
                and SUBSTR( A.appyer_time, 1, 4 ) = TO_CHAR( CURRENT_DATE, 'YYYY' )
                AND B.amt IS NOT NULL
                AND C.dept IS NOT NULL
        )
        SELECT
            dept,
            SUM(ROUND(amt / NULLIF(tt, 0), 6)) AS reimAmt
        FROM
            filtered_data
        GROUP BY
            dept
<!--        SELECT e.dept,SUM(e.reimAmt) FROM (-->
<!--              SELECT-->
<!--                  n.dept,-->
<!--                  ROUND( ( n.amt / NULLIF ( n.tt, 0 ) ) * n.ttt, 6 ) AS reimAmt-->
<!--              FROM-->
<!--                  (-->
<!--                      SELECT M.ID,-->
<!--                             M.amt,-->
<!--                             M.dept,-->
<!--                             M.tt,-->
<!--                             COUNT ( M.dept ) ttt-->
<!--                      FROM-->
<!--                          (-->
<!--                              SELECT A.ID,-->
<!--                                     b.amt,-->
<!--                                     C.dept,-->
<!--                                     COUNT ( A.ID ) OVER ( PARTITION BY A.ID ) tt-->
<!--                              FROM-->
<!--                                  ecs_reim_detail-->
<!--                                      A LEFT JOIN ecs_reim_item_detail B ON A.ID = B.reim_detail_id-->
<!--                                      AND b.item = '租车费'-->
<!--                                        LEFT JOIN ecs_reim_psn_detail C ON A.ID = C.reim_detail_id-->
<!--                                      AND C."type" = '2'-->
<!--                              WHERE-->
<!--                                  A.busstas = '1'-->
<!--                                AND SUBSTR( A.appyer_time, 1, 4 ) = TO_CHAR( CURRENT_DATE, 'YYYY' )-->
<!--                                AND b.amt IS NOT NULL-->
<!--                          ) M-->
<!--                      GROUP BY-->
<!--                          M.ID,-->
<!--                          M.amt,-->
<!--                          M.dept,-->
<!--                          M.tt-->
<!--                  ) n ) e group by e.dept-->
    </select>

    <!-- 查询辅项信息 -->
    <select id="queryReimAsstDetail"
            resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsReimAsstDetail">
        SELECT a.id,
               a.reim_detail_id,
               a.pay_type_code,
               a.pay_type_name,
               a.actig_sub_code,
               a.actig_sub_name,
               a.actig_sys,
               a.dept_code,
               a.dept_name,
               a.rel_co_code,
               a.rel_co_name,
               a.fun_sub_code,
               a.fun_sub_name,
               a.econ_sub_code,
               a.econ_sub_name,
               a.proj_code,
               a.proj_name,
               a.cash_flow_code,
               a.cash_flow_name,
               a.actig_amt_type,
               a.actig_amt,
               a.crter,
               a.create_time,
               a.hospital_id,
               a.abst,
               a.sup_type,
               a.vpzh
        FROM ecs_reim_asst_detail a
        where a.reim_detail_id = #{reimDetailId,jdbcType=VARCHAR}
          and a.sup_type = #{supType,jdbcType=VARCHAR}
        <if test="deptCode != null and deptCode != ''">
            and dept_code = #{deptCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getFirstAudit" resultType="com.jp.med.common.entity.audit.AuditDetail">
        SELECT a.id,
               a.bchno,
               a.chker,
               a.chk_dept        as chkDept,
               a.chk_time        as chkTime,
               a.chk_remarks     as chkRemarks,
               a.chk_sign        as chkSign,
               a.chk_att         as chkAtt,
               a.chk_seq         as chkSeq,
               a.chk_state       as chkState,
               a.chk_sign_path   as chkSignPath,
               a.chk_att_path    as chkAttPath,
               a.dscr,
               a.message_id      as messageId,
               a.message_sup     as messageSup,
               a.message_payload as messagePayload
        FROM ecs_audit_rcdfm a
                 LEFT JOIN ecs_reim_detail b
                           ON a.bchno = b.audit_bchno
        WHERE a.chk_seq = 1
          AND b."id" = #{id,jdbcType=INTEGER}
    </select>

    <select id="queryBusInvoIds" resultType="java.lang.String">
        SELECT A.invo_id
        FROM ecs_reim_item_detail A
        WHERE A.reim_detail_id = #{id,jdbcType=INTEGER}
          AND A.invo_id IS NOT NULL
        UNION ALL
        SELECT b.invo_id
        FROM ecs_reim_subs_item_detail b
        WHERE b.reim_detail_id = #{id,jdbcType=INTEGER}
          AND b.invo_id IS NOT NULL
    </select>

    <select id="queryItemDetailsInvoIds" resultType="java.lang.String">
        SELECT A.invo_id
        FROM ecs_reim_item_detail A
        WHERE A.reim_detail_id = #{id,jdbcType=INTEGER}
          AND A.invo_id IS NOT NULL
    </select>

    <select id="queryTravelPsnInfo" resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsReimApprManage">
        SELECT *
        FROM (SELECT b.dept                                                           AS deptCode,
                     e.org_name                                                       AS deptName,
                     b.trip_psn                                                       AS psnCode,
                     d.emp_name                                                       AS psnName,
                     d.sex                                                            AS sex,
                     d.age                                                            AS age,
                     f.engage_rank                                                    AS engageLevel,
                     h.administrative_rank                                            AS adminLevel,
                     A.evection_addr                                                  AS evectionAddr,
                     A.evection_rea                                                   AS evectionRea,
                     A.evection_begn_time                                             AS evectionBeginTime,
                     A.evection_end_time                                              AS evectionEndTime,
                     (A.evection_end_time :: DATE - A.evection_begn_time :: DATE) + 1 AS timePeriod,
                     A.ID                                                             AS apprId,
                    a.self_drive_rea as selfDriveRea,
                     --A.ID AS reimId,
                     M.ID                                                             AS reimId,
                     M.reim_amt                                                       AS reimAmt,
                     m.busstas                                                        as busstas,
                     --b.reim_amt AS reimAmt,
                     A.reim_flag                                                      AS reimFlag
              FROM ecs_reim_travel_appr A
                       JOIN ecs_audit_res C ON A.audit_bchno = C.bchno
                       JOIN ecs_reim_psn_detail b ON A.ID = b.reim_detail_id
                  AND b."type" = '1'
                       LEFT JOIN hrm_employee_info d ON b.trip_psn = d.emp_code
                       LEFT JOIN hrm_org e ON b.dept = e.org_id
                       LEFT JOIN (SELECT *
                                  FROM (SELECT emp_id, ID, engage_rank, MAX(ID) OVER ( PARTITION BY emp_id ) max_id
                                        FROM hrm_engage_level) A
                                  WHERE max_id = ID) f ON d.ID = f.emp_id --职称
                       LEFT JOIN (SELECT *
                                  FROM (SELECT A
                                                   .emp_id,
                                               A.ID,
                                               MAX(A.ID) OVER ( PARTITION BY A.emp_id ) max_id,
                                               b.code_lable AS                          administrative_rank
                                        FROM hrm_administrative_rank A
                                                 LEFT JOIN hrm_employee_dict b ON A.administrative_rank = b."id" :: TEXT) A
                                  WHERE max_id = ID) h ON d.ID = h.emp_id --行政职务
                       LEFT JOIN (SELECT A.ID,
                                         A.travel_appr_id,
                                         A.busstas,
                                         b.dept,
                                         b.trip_psn,
                                         b.reim_amt
                                  FROM ecs_reim_detail A,
                                       ecs_reim_psn_detail b
                                  WHERE A.ID = b.reim_detail_id
                                    AND A.TYPE = #{type,jdbcType=VARCHAR}
                                    AND b."type" = '2') M ON A.ID = M.travel_appr_id
                  AND b.dept = M.dept
                  AND b.trip_psn = M.trip_psn
              WHERE C.audit_res = '1'
                AND A.TYPE = #{type,jdbcType=VARCHAR}
                AND LEFT(A.appyer_time, 4) = #{year,jdbcType=VARCHAR}) n
        <where>
            <if test="apprId != null">
                AND n.apprId = #{apprId,jdbcType=INTEGER}
            </if>
            <if test="reimId != null">
                AND n.reimId = #{reimId,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY n.apprId
    </select>

    <select id="msgNote" resultType="com.jp.med.ecs.modules.reimMgt.vo.HomeMsgNoteVo">
        SELECT
            *
        FROM
            (
                SELECT A.ID,
                       A.appyer as empCode,
                       A.appyer_time as createTime,
                       b.emp_name as empName,
                       '职能申请' AS operation
                FROM
                    ecs_reim_travel_appr
                        A LEFT JOIN hrm_employee_info b ON A.appyer = b.emp_code
                WHERE
                    appr_dept_type = '1'
                    <if test='sysUser.username != null and sysUser.username !="admin"'>
                        AND a.appyer = #{sysUser.username}
                    </if>

                UNION ALL

                SELECT A.ID,
                       A.appyer as empCode,
                       A.appyer_time as createTime,
                       b.emp_name as empName,
                       '临床申请' AS operation
                FROM
                    ecs_reim_travel_appr
                        A LEFT JOIN hrm_employee_info b ON A.appyer = b.emp_code
                WHERE
                    appr_dept_type = '2'
                <if test='sysUser.username != null and sysUser.username !="admin"'>
                    AND a.appyer = #{sysUser.username}
                </if>

                UNION ALL

                SELECT A.ID,
                       A.appyer as empCode,
                       A.appyer_time as createTime,
                       b.emp_name as empName,
                       CASE
                           A.TYPE
                           WHEN '1' THEN
                               '职能差旅报销' ELSE'职能培训报销'
                           END AS operation
                FROM
                    ecs_reim_detail
                        A LEFT JOIN hrm_employee_info b ON A.appyer = b.emp_code,
                    ecs_reim_travel_appr C
                WHERE
                     ( A.TYPE = '1' OR A.TYPE = '2' )
                    <if test='sysUser.username != null and sysUser.username !="admin"'>
                        AND a.appyer = #{sysUser.username}
                    </if>
                  AND A.travel_appr_id = C.ID
                  AND C.appr_dept_type = '1'


                UNION ALL

                SELECT A.ID,
                       A.appyer as empCode,
                       A.appyer_time as createTime,
                       b.emp_name as empName,
                       CASE
                           A.TYPE
                           WHEN '1' THEN
                               '职能差旅报销' ELSE'职能培训报销'
                           END AS operation
                FROM
                    ecs_reim_detail
                        A LEFT JOIN hrm_employee_info b ON A.appyer = b.emp_code,
                    ecs_reim_travel_appr C
                WHERE
                     ( A.TYPE = '1' OR A.TYPE = '2' )
                    <if test='sysUser.username != null and sysUser.username !="admin"'>
                        AND a.appyer = #{sysUser.username}
                    </if>
                  AND A.travel_appr_id = C.ID
                  AND C.appr_dept_type = '1'

                UNION ALL

                SELECT A.ID,
                       A.appyer as empCode,
                       A.appyer_time as createTime,
                       b.emp_name as empName,
                       '其他费用报销' AS operation
                FROM
                    ecs_reim_detail A
                    LEFT JOIN hrm_employee_info b ON A.appyer = b.emp_code
                WHERE
                   A.TYPE = '3'
                <if test='sysUser.username != null and sysUser.username !="admin"'>
                    AND a.appyer = #{sysUser.username}
                </if>
            ) M
        ORDER BY
            M.createTime DESC
    </select>

    <select id="queryReimDetailOfMonth" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo">
        SELECT A.*,
               b.appr_dept_type
        FROM
            ecs_reim_detail
                A LEFT JOIN ecs_reim_travel_appr b ON A.travel_appr_id = b.ID
                AND A.TYPE IN ( '1', '2' )
        WHERE
            SUBSTRING ( A.appyer_time, 1, 7 ) = #{month,jdbcType=VARCHAR}

        UNION

        SELECT A.*,
               '' AS apprDeptType
        FROM
            ecs_reim_detail A
        WHERE
            SUBSTRING ( A.appyer_time, 1, 7 ) = #{month,jdbcType=VARCHAR}
          AND A.TYPE NOT IN ( '1', '2' )
    </select>

    <select id="queryReimItemDetails" resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsReimItemDetail">
        select a.id,
               a.reim_detail_id as reimDetailId,
               a.dept_code      as deptCode,
               c.org_name       as deptName,
               a.item,
               a.doc_num        as docNum,
               a.amt,
               a.extra_amt      as extraAmt,
               a.att,
               a.att_name       as attName,
               a.days_or_kilor  as daysOrKilor,
               a.std            as std,
               a.type,
               a.reim_abst      as reimAbst,
               a.budget_code    as budgetCode,
               a.invo_id        as invoId,
               a.emp_code       as empCode,
               a.reim_name      as reimName,
               b.sub_name       as subName,
               b.bgt_summary    as bgtSummary
        from ecs_reim_item_detail a
                 left join ecs_econ_fun_sub_cfg b
                           on a.type = b.sub_code and b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
                 left join hrm_org c
                           on a.dept_code = c.org_id
        <where>
            <choose>
                <!-- 集合非空时，生成 IN 子句 -->
                <when test="ids != null and !ids.isEmpty()">
                    a.reim_detail_id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=INTEGER}
                </foreach>
                </when>
                <!-- 集合为空时，生成 1=0 确保无结果 -->
                <otherwise>
                    1 =0
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>
