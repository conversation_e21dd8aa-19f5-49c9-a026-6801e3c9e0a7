<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsResearchFundingTaskReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskVo" id="researchFundingTaskMap">
        <result property="id" column="id"/>
        <result property="appyer" column="appyer"/>
        <result property="appyerDept" column="appyer_dept"/>
        <result property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="projectLeader" column="project_leader"/>
        <result property="projectLevel" column="project_level"/>
        <result property="topicCategory" column="topic_category"/>
        <result property="reimAmt" column="reim_amt"/>
        <result property="schedulePayTime" column="schedule_pay_time"/>
        <result property="reimFlag" column="reim_flag"/>
        <result property="reimId" column="reim_id"/>
        <result property="recordId" column="record_id"/>
        <result property="budget" column="budget"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskVo">
        select
            id as id,
            appyer as appyer,
            appyer_dept as appyerDept,
            project_id as projectId,
            project_name as projectName,
            project_leader as projectLeader,
            project_level as projectLevel,
            topic_category as topicCategory,
            reim_amt as reimAmt,
            schedule_pay_time as schedulePayTime,
            reim_flag as reimFlag,
            reim_id as reimId,
            record_id as recordId,
            budget as budget
        from ecs_research_funding_task
        <where>
            <if test="reimFlag != null and reimFlag != ''">
                and reim_flag = #{reimFlag,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                and id = #{id,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryResearchTaskDetail" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskDetailVo">
        SELECT A.ID,
               A.task_id,
               A.reim_abstract,
               A.org_id,
               A.reim_type,
               A.reim_amt,
               A.att,
               b.budget_code,
               b.bgt_summary as bgtSummary
        FROM
            ecs_research_funding_task_detail
                A LEFT JOIN ecs_econ_fun_sub_cfg b ON A.reim_type = b.sub_code
                AND b.active_flag = '1'
                AND b.sub_type = '2'
                and b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
        WHERE
            A.task_id = #{id,jdbcType=INTEGER}
    </select>

    <select id="queryResearchFundingBudget" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskVo">
        SELECT A
                   .project_id,
               A.project_name,
               COALESCE(b.alreadyUsedAmt,0) AS alreadyUsedAmt,
               a.budgetAmt
        FROM
            (
                SELECT A
                           .project_id,
                       a.project_name,
                       ROUND( AVG ( A.budget ) , 2 ) AS budgetAmt
                FROM
                    ecs_research_funding_task A
                GROUP BY
                    A.project_id,a.project_name
            ) a
                LEFT JOIN (
                SELECT
                    b.project_id,
                    b.project_name,
                    SUM ( b.reim_amt ) AS alreadyUsedAmt
                FROM
                    ecs_research_funding_task b
                WHERE
                    b.reim_flag = '1'
                GROUP BY
                    b.project_id,
                    b.project_name
            ) b ON A.project_id = b.project_id
    </select>
</mapper>
