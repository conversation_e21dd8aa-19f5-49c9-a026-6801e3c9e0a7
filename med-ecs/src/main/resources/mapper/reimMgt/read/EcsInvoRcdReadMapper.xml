<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsInvoRcdReadMapper">

    <sql id="fields">
        a.id as id,
        <!--a.invo_code as invoCode,
        a.invo_num as invoNum,
        a.invo_date as invoDate,-->
        a.chk_code as chkCode,
        a.att as att,
        a.att_name as attName,
        a.chk_state as chkState,
        a.chk_time as chkTime,
        a.idtf_err_msg as idtfErrMsg,
        a.create_time as createTime,
        a.hospital_id as hospitalId,
        a.state as state,
        a.chk_data as chkData,
        a.manual_amend as manualAmend,
        a.amend_user as amendUser,
        a.file_identifier as fileIdentifier,
        a.status as status,
        a.amend_applyer as amendApplyer,
        a.abs as abs,
        a.purchaser_name as purchaserName,
        a.purchaser_taxpayer_number as purchaserTaxpayerNumber,
        a.invoice_money as invoiceMoney,
        a.all_valorem_tax as allValoremTax,
        a.create_user as createUser,
        a.invo_user as invoUser,
        a.invo_from as invoFrom,
        a.invo_type as invoType,
        a.sub_invo_ids as subInvoIds,
        a.is_sub as isSub,
        a.invo_used_by as invoUsedBy
    </sql>

    <select id="queryList" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo">
        select
            <include refid="fields" />,
            <!--a.invo_code as invoCode,
            a.invo_num as invoNum,
            a.invo_date as invoDate,-->
            sub_data.sub_invo_codes as invoCode,
            sub_data.sub_invo_nums as invoNum,
            sub_data.sub_invo_dates as invoDate,
            b.emp_name as amendUserName,
            c.emp_name as amendApplyerName
        from ecs_invo_rcd a
        left join hrm_employee_info b
        on a.amend_user = b.emp_code
        left join hrm_employee_info c
        on a.amend_applyer = c.emp_code
        LEFT JOIN LATERAL ( -- 使用 LEFT JOIN 以防 a.sub_invo_ids 为空或找不到对应 id 时丢失 a 表的行
            SELECT
                string_agg(b.invo_code, ',') AS sub_invo_codes,
                string_agg(b.invo_num, ',') AS sub_invo_nums,
                string_agg(b.invo_date::text, ',') AS sub_invo_dates -- 同样需要转换日期类型
            FROM
                ecs_invo_rcd b
            WHERE
                -- 添加检查，避免 sub_invo_ids 为 NULL 或空字符串 '' 时 string_to_array 出错
                NULLIF(a.sub_invo_ids, '') IS NOT NULL
                AND b.id = ANY(string_to_array(a.sub_invo_ids, ',')::integer[])
        ) AS sub_data ON true -- LATERAL 的关联条件通常是 ON true，实际关联在 WHERE 子句中完成
        <where>
            <!-- 排除子发票 -->
            a.is_sub is null
            <if test="invoFrom != null and invoFrom != ''">
                and a.invo_from = #{invoFrom,jdbcType=VARCHAR}
            </if>
            <if test="status!=null and status !=''">
                <choose>
                    <when test='status == "1"'>
                        and a.status = #{status,jdbcType=VARCHAR}
                    </when>
                    <otherwise>
                        and a.status is not null
                        and a.status != '1'
                    </otherwise>
                </choose>
            </if>
            <if test='createUser !=null and createUser !="" and createUser !="admin"'>
                and a.create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="updDate != null and updDate != ''">
                and LEFT(a.create_time, 10) = #{updDate,jdbcType=VARCHAR}
            </if>
            <if test="state != null and state != ''">
                <choose>
                    <when test='state == "1"'>
                        and a.state = '1'
                    </when>
                    <when test='state == "2"'>
                        and a.state = '2'
                    </when>
                    <when test='state == "3"'>
                        and a.state = '4'
                    </when>
                    <when test='state == "4"'>
                        and (a.state = '5' or a.state = '6' or a.state = '7' or a.state = '8')
                    </when>
                    <when test='state == "5"'>
                        and a.state = '9'
                    </when>
                    <otherwise>
                        and a.state = '3'
                    </otherwise>
                </choose>
                <!--and a.state = #{state,jdbcType=VARCHAR}   只分为可报销和其他状态-->
            </if>
            <if test="attName != null and attName != ''">
                and a.att_name like concat('%',#{attName,jdbcType=VARCHAR},'%')
            </if>
            <if test="invoNum != null and invoNum != ''">
                and exists (
                    select 1 from ecs_invo_rcd m
                    where m.id in (
                                SELECT unnest(string_to_array(a.sub_invo_ids, ','))::int
                            )
                and (m.invo_num like concat('%',#{invoNum,jdbcType=VARCHAR},'%')
                or m.invo_num like concat('%',#{invoNum,jdbcType=VARCHAR},'%'))
                )
                <!--and (a.invo_num like concat('%',#{invoNum,jdbcType=VARCHAR},'%')
                or a.invo_code like concat('%',#{invoNum,jdbcType=VARCHAR},'%'))-->
            </if>
        </where>
        order by a.create_time desc
    </select>

    <!-- 查询已经存在的记录 -->
    <select id="queryExistsRcd" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo">
        select
            a.invo_code as invoCode,
            a.invo_num as invoNum,
            a.invo_date as invoDate,
            <include refid="fields" />
        from ecs_invo_rcd a
        where exists (
            select *
            from(
                <foreach collection="list" item="rcd" separator=" union all ">
                    select #{rcd.invoCode,jdbcType=VARCHAR} as invoCode,
                           #{rcd.invoNum,jdbcType=VARCHAR} as invoNum,
                           #{rcd.invoDate,jdbcType=VARCHAR} as invoDate,
                           #{rcd.chkCode,jdbcType=VARCHAR} as chkCode
                </foreach>
            ) b
            where a.invo_code = b.invoCode
            and a.invo_num = b.invoNum
            and a.invo_date = b.invoDate
            and a.chk_code = b.chkCode
        )
    </select>

    <select id="queryExistsRcdByHash" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo">
        select
        a.invo_code as invoCode,
        a.invo_num as invoNum,
        a.invo_date as invoDate,
        <include refid="fields"/>
        from ecs_invo_rcd a
        where a.file_identifier in (
            <foreach collection="list" item="item" separator=",">
                #{item.fileIdentifier,jdbcType=VARCHAR}
            </foreach>
        )
    </select>

    <!-- 查询是否已经报销 -->
    <select id="queryAlreadyReim" resultType="java.lang.Integer">
        select count(1)
        from ecs_invo_rcd
        where id in (
            <foreach collection="list" item="id" separator=",">
                #{id,jdbcType=INTEGER}
            </foreach>
        )
        and state != '1'
    </select>

    <!-- 查询发票校验记录 -->
    <select id="queryChkRcdByInvoId" resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsInvoChkRcdEntity">
        SELECT
            id,
            all_tax,
            all_valorem_tax,
            check_code,
            cyjgxx,
            invalid_mark,
            invoice_code,
            invoice_date,
            invoice_money,
            invoice_number,
            invoice_type,
            machine_code,
            note,
            purchaser_name,
            saler_address_or_phone,
            saler_bank_account,
            saler_bank_and_number,
            saler_name,
            saler_taxpayer_number,
            purchaser_taxpayer_number,
            seller_unit_or_individual,
            invo_rcd_id
        FROM ecs_invo_chk_rcd
        WHERE invo_rcd_id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 查询发票校验记录明细 -->
    <select id="queryChkRcdDetailByInvoId" resultType="com.jp.med.ecs.modules.reimMgt.entity.EcsInvoChkRcdDetailEntity">
        SELECT
            id,
            all_tax,
            standard,
            row_no,
            tax_classify_code,
            net_value,
            num,
            traffic_date_start,
            type,
            tax_detail_amount,
            tax_rate,
            tax_unit_price,
            unit,
            plate_no,
            detail_no,
            detail_amount,
            goods_name,
            traffic_date_end,
            expense_item,
            invo_rcd_id
        FROM ecs_invo_chk_rcd_detail
        WHERE invo_rcd_id = #{id,jdbcType=INTEGER}
    </select>

    <select id="queryAlreadyAmendRcd" resultType="java.lang.Integer">
        SELECT COUNT ( 1 )
        FROM
            ecs_invo_rcd A
        WHERE
        A.manual_amend = '1'
        and A.id IN (
            <foreach collection="list" item="id" separator=",">
                #{id,jdbcType=VARCHAR}
            </foreach>
        )
    </select>

    <select id="queryEcsInvoRcdByFileIdentifier" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo">
        SELECT
            a.invo_code as invoCode,
            a.invo_num as invoNum,
            a.invo_date as invoDate,
            <include refid="fields"/>
        FROM
            ecs_invo_rcd a
        WHERE
            a.file_identifier = #{fileIdentifier,jdbcType=VARCHAR}
    </select>
</mapper>
