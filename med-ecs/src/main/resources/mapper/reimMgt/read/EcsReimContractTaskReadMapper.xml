<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimContractTaskReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskVo"
        id="reimContractTaskMap">
        <result property="id" column="id" />
        <result property="appyer" column="appyer" />
        <result property="appyerDept" column="appyer_dept" />
        <result property="ctCode" column="ct_code" />
        <result property="typeCode" column="type_code" />
        <result property="contractAtt" column="contract_att" />
        <result property="contractAttName" column="contract_att_name" />
        <result property="totalAmt" column="total_amt" />
        <result property="stage" column="stage" />
        <result property="contractId" column="contract_id" />
        <result property="proportion" column="proportion" />
        <result property="paymentTime" column="payment_time" />
        <result property="reimFlag" column="reim_flag" />
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskVo">
        select a.id as id, a.appyer as appyer, a.appyer_dept as appyerDept, a.ct_code as ctCode,
        a.type_code as typeCode, a.contract_att as contractAtt, a.contract_att_name as
        contractAttName, a.total_amt as totalAmt, a.stage as stage, a.contract_id as contractId,
        a.proportion as proportion, a.payment_time as paymentTime, a.reim_flag as reimFlag,
        a.payment_id as paymentId, a.reim_id as reimId, b.org_name as appyerDeptName, a.bank as
        bank, a.acctname as acctname, a.bankcode as bankcode, a.opposite_name as oppositeName
        ,a.ct_name as ctName ,a.ct_unified_code as ctUnifiedCode ,a.need_reim_amt as needReimAmt
        ,a.payment_type as paymentType from ecs_reim_contract_task a left join hrm_org b on
        a.appyer_dept = b.org_id left join hrm_employee_info c on a.appyer = c.emp_code <where>
            <if test="reimFlag != null and reimFlag != ''"> and a.reim_flag =
        #{reimFlag,jdbcType=VARCHAR} </if>
            <if test="ctName != null and ctName != ''"> and a.ct_name
        LIKE CONCAT('%',#{ctName,jdbcType=VARCHAR},'%') </if>
            <if
                test="ctCode != null and ctCode != ''"> and a.ct_code LIKE
        CONCAT('%',#{ctCode,jdbcType=VARCHAR},'%') </if>
            <if
                test="ctUnifiedCode != null and ctUnifiedCode != ''"> and a.ct_unified_code LIKE
        CONCAT('%',#{ctUnifiedCode,jdbcType=VARCHAR},'%') </if>
            <if
                test="typeCode != null and typeCode != ''"> and a.type_code =
        #{typeCode,jdbcType=VARCHAR} </if>
            <if test="oppositeName != null and oppositeName != ''">
        and a.opposite_name LIKE CONCAT('%',#{oppositeName,jdbcType=VARCHAR},'%') </if>
            <if
                test="appyer != null and appyer != ''"> and a.appyer = #{appyer,jdbcType=VARCHAR} </if>
        </where>
        order by a.opposite_name asc, a.payment_id asc </select>

    <select id="queryContractTaskDetail"
        resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskDetailVo"> SELECT A.ID,
        A.task_id AS taskId, A.reim_abst AS reimAbst, A.org_id AS orgId, A.reim_type AS reimType,
        A.reim_amt AS reimAmt, A.att, b.budget_code AS budgetCode,b.bgt_summary as bgtSummary FROM
        ecs_reim_contract_task_detail A LEFT JOIN ecs_econ_fun_sub_cfg b ON A.reim_type = b.sub_code
        and b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT AND b.active_flag = '1' AND b.sub_type =
        '2' WHERE A.task_id = #{id,jdbcType=INTEGER} </select>
</mapper>