<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimFileRecordReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.reimMgt.vo.EcsReimFileRecordVo" id="reimFileRecordMap">
        <result property="id" column="id"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
        <result property="type" column="type"/>
        <result property="attCode" column="att_code"/>
        <result property="flag" column="flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimFileRecordVo">
        select
            id as id,
            att as att,
            att_name as attName,
            type as type,
            att_code as attCode,
            flag as flag
        from ecs_reim_file_record
    </select>

    <select id="queryByAttCode" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimFileRecordVo">
        select
            id as id,
            att as att,
            att_name as attName,
            type as type,
            att_code as attCode,
            flag as flag
        from ecs_reim_file_record
        where att_code = #{attCode,jdbcType=VARCHAR}
        and flag = '0'
    </select>

</mapper>
