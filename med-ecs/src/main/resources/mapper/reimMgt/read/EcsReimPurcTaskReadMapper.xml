<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimPurcTaskReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.vo.EcsReimPurcTaskVo" id="reimPurcTaskMap">
        <result property="id" column="id"/>
        <result property="itemNo" column="item_no"/>
        <result property="itemName" column="item_name"/>
        <result property="reimOrgCode" column="reim_org_code"/>
        <result property="exeEmpCode" column="exe_emp_code"/>
        <result property="exeDate" column="exe_date"/>
        <result property="cfmOrgCode" column="cfm_org_code"/>
        <result property="cfmEmpCode" column="cfm_emp_code"/>
        <result property="cfmDate" column="cfm_date"/>
        <result property="sumamt" column="sumamt"/>
        <result property="reimFlag" column="reim_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.vo.EcsReimPurcTaskVo">
        select a.id                                                  as id,
               a.item_no                                             as itemNo,
               'purc' || a.id                                   as key,
               a.item_name                                           as itemName,
               a.reim_org_code                                       as reimOrgCode,
               a.reim_task_type                                       as reimTaskType,
               a.supplier_id                                       as supplierId,
               a.supplier_name                                       as supplierName,
               org1.org_name                                         as reimOrgName,
               a.exe_emp_code                                        as exeEmpCode,
               e1.emp_name                                           as exeEmpName,
               to_date(a.exe_date, 'yyyy-MM-dd HH24:mi:ss')::VARCHAR as exeDate,
               a.cfm_org_code                                        as cfmOrgCode,
               org2.org_name                                         as cfmOrgName,
               a.cfm_emp_code                                        as cfmEmpCode,
               e2.emp_name                                           as cfmEmpName,
               a.cfm_date                                            as cfmDate,
               a.sumamt                                              as sumamt,
               a.reim_flag                                           as reimFlag,
               a.reim_id                                             as reimId,
               b.att_code                                            as attCode,
               b.audit_bchno                                         as auditBchno
        from ecs_reim_purc_task a
                 left join ecs_reim_detail b on a.reim_id = b.id
                 left join hrm_org org1 on a.reim_org_code = org1.org_id
                 left join hrm_org org2 on a.cfm_org_code = org2.org_id
                 left join hrm_employee_info e1 on a.exe_emp_code = e1.emp_code
                 left join hrm_employee_info e2 on a.cfm_emp_code = e2.emp_code
        <where>
            <if test="reimFlag != null and reimFlag != ''">
                and reim_flag = #{reimFlag,jdbcType=VARCHAR}
            </if>
            <if test="itemName != null and itemName != ''">
                and exists (select 1
                            from ecs_reim_purc_task_detail m
                            where m.task_id = a.id
                              and POSITION(m.reim_desc IN #{itemName,jdbcType=VARCHAR}) > 0)
            </if>
            <if test="reimTaskType != null and reimTaskType != ''">
                and a.reim_task_type = #{reimTaskType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryPurcTaskDetail" resultType="com.jp.med.common.vo.EcsReimPurcTaskDetailVo">
        SELECT A.ID,
               A.task_id     AS taskId,
              A.purc_detail_id as purcDetailId,
               A.item_no     as itemNo,
               A.org_id      AS orgId,
               A.reim_type   AS reimType,
               A.reim_id     as reimId,
               A.reim_flag   as reimFlag,
               A.reim_amt    AS reimAmt,
               A.reim_desc   AS reimDesc,
               A.att,
               A.att_name    AS attName,
               B.budget_code AS budgetCode,
               B.bgt_summary AS bgtSummary
        FROM ecs_reim_purc_task_detail A
                 LEFT JOIN ecs_econ_fun_sub_cfg b ON A.reim_type = b.sub_code and b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
            AND b.active_flag = '1'
            AND b.sub_type = '2'
        WHERE
        A.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryPurcTaskDetailByReimId" resultType="com.jp.med.common.vo.EcsReimPurcTaskDetailVo">
        SELECT A.ID,
               A.task_id     AS taskId,
               A.purc_detail_id as purcDetailId,
               A.item_no     as itemNo,
               A.org_id      AS orgId,
               A.reim_type   AS reimType,
               A.reim_id     as reimId,
               A.reim_flag   as reimFlag,
               A.reim_amt    AS reimAmt,
               A.reim_desc   AS reimDesc,
               A.att,
               A.att_name    AS attName,
               A.unt as unt,
               A.cnt as cnt,
               A.price as price,
               B.budget_code AS budgetCode,
               B.bgt_summary as bgtSummary
        FROM ecs_reim_purc_task_detail A
                 LEFT JOIN ecs_econ_fun_sub_cfg b ON A.reim_type = b.sub_code and b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
            AND b.active_flag = '1'
            AND b.sub_type = '2'
        WHERE A.reim_id = #{reimId,jdbcType=INTEGER}
    </select>

    <select id="queryPurcTaskDetails" resultType="com.jp.med.common.vo.EcsReimPurcTaskDetailVo">
        SELECT A.ID,
               A.task_id            AS taskId,
               A.purc_detail_id     as purcDetailId,
               A.item_no            as itemNo,
               A.org_id             AS orgId,
               org.org_name         as cfmOrgName,
               A.reim_type          AS reimType,
               A.reim_id            as reimId,
               A.reim_flag          as reimFlag,
               A.unt,
               round(A.cnt, 2)      as cnt,
               round(A.price, 2)    as price,
               round(A.reim_amt, 2) AS reimAmt,
               A.reim_desc          AS reimDesc,
               A.att,
               A.att_name           AS attName,
               B.budget_code        AS budgetCode,
               B.bgt_summary        as bgtSummary
        FROM ecs_reim_purc_task_detail A
                 LEFT JOIN ecs_econ_fun_sub_cfg b
                           ON A.reim_type = b.sub_code AND b.active_flag = '1' AND b.sub_type = '2' and
                              b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
                 left join hrm_org org on org.org_id = a.org_id
        WHERE
        A.task_id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        <if test="dto.itemName != null and dto.itemName != ''">
            and POSITION(A.reim_desc IN #{dto.itemName,jdbcType=VARCHAR}) > 0
        </if>
    </select>

    <select id="selectListByDetailIds" resultType="com.jp.med.common.vo.EcsReimPurcTaskDetailVo">
        select *
        from ecs_reim_purc_task_detail a
        where a.item_no in (
        SELECT distinct item_no
        FROM ecs_reim_purc_task_detail WHERE id IN
        <foreach collection="purcDetailIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
    </select>

    <select id="queryPurcTaskByReimId" resultType="com.jp.med.common.vo.EcsReimPurcTaskVo">
        SELECT b.reim_id,A.*
        FROM
            ecs_reim_purc_task A,
            ( SELECT item_no,reim_id FROM ecs_reim_purc_task_detail A
              WHERE
                <if test="ids != null and ids.size() > 0">
                    A.reim_id IN
                    <foreach collection="ids" item="id" open="(" separator="," close=")">
                        #{id,jdbcType=INTEGER}
                    </foreach>
                </if>
                <if test="id != null">
                    AND A.reim_id = #{id,jdbcType=INTEGER}
                </if>
              GROUP BY item_no,reim_id ) b
        WHERE
            A.item_no = b.item_no
    </select>
</mapper>
