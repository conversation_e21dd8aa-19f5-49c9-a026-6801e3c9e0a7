<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.drugMgt.mapper.read.EcsSatmatReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatVo" id="satmatMap">
        <result property="id" column="id"/>
        <result property="stoinNum" column="stoin_num"/>
        <result property="stoinDate" column="stoin_date"/>
        <result property="satmatCode" column="satmat_code"/>
        <result property="satmatName" column="satmat_name"/>
        <result property="specs" column="specs"/>
        <result property="pattern" column="pattern"/>
        <result property="unit" column="unit"/>
        <result property="batchNum" column="batch_num"/>
        <result property="prodDate" column="prod_date"/>
        <result property="validDate" column="valid_date"/>
        <result property="satmatNum" column="satmat_num"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="sumamt" column="sumamt"/>
        <result property="factory" column="factory"/>
        <result property="spler" column="spler"/>
        <result property="consignor" column="consignor"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatVo">
        select
            a.id as id,
            a.stoin_num as stoinNum,
            substring(a.stoin_date,1,10) as stoinDate,
            a.satmat_code as satmatCode,
            a.satmat_name as satmatName,
            a.specs as specs,
            a.pattern as pattern,
            a.unit as unit,
            a.batch_num as batchNum,
            a.prod_date as prodDate,
            a.valid_date as validDate,
            a.satmat_num as satmatNum,
            a.unit_price as unitPrice,
            a.sumamt as sumamt,
            a.factory as factory,
            a.consignor as spler,
            a.sync_date as syncDate,
            a.hospital_id as hospitalId,
            a.satmat_reim_detail_id as satmatReimDetailId,
            a.att as att,
            a.att_name as attName,
            a.reim_flag as reimFlag
        from ecs_satmat a
        <where>
            <if test="issue != null and issue != ''">
                and substring(a.stoin_date,1,7) = #{issue,jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != ''">
                and a.reim_flag = #{type,jdbcType=VARCHAR}
            </if>
            <if test="satmatReimDetailId != null">
                and a.satmat_reim_detail_id = #{satmatReimDetailId,jdbcType=INTEGER}
            </if>
            <if test="stoinNum != null and stoinNum != ''">
                and a.stoin_num like CONCAT('%',#{stoinNum,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>

    <select id="listSatMats" resultType="com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatDto">
        <!--select * from (
        select a.单号     as stoinNum,
        to_char(a.入库时间,'yyyy-mm-dd HH24:mi:ss') as stoinDate,
        a.编码     as satmatCode,
        a.名称     as satmatName,
        a.规格     as specs,
        a.型号     as pattern,
        a.单位     as unit,
        a.批号     as batchNum,
        to_char(a.生产日期,'yyyy-mm-dd') as prodDate,
        to_char(a.有效期,'yyyy-mm-dd') as validDate,
        a.数量     as satmatNum,
        a.单价     as unitPrice,
        a.金额     as sumamt,
        a.厂家     as factory,
        a.供应商   as spler,
        case when a.货主 = '中江县人民医院直供' THEN A.供应商 ELSE A.货主 END as consignor,
        ROW_NUMBER() OVER (ORDER BY a.单号) AS rn
        from hcspd.v_spd_in_erp a
        WHERE A.类型 = '外采'
        AND A.单号 > #{floor,jdbcType=INTEGER}
        and A.入库时间 >= to_date('2024-01-01', 'yyyy-mm-dd')
        and not exists (select 1
        from hcspd.v_spd_in_erp b
        where b.类型 = '采购退货'
        and a.单号 = b.单号)
        ORDER BY A.单号)
        where rn between #{startRow,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}-->
        select *
        from (select a.单号 as stoinNum,
        to_char(a.入库时间, 'yyyy-mm-dd HH24:mi:ss') as stoinDate,
        a.编码 as satmatCode,
        a.名称 as satmatName,
        a.规格 as specs,
        a.型号 as pattern,
        a.单位 as unit,
        a.批号 as batchNum,
        to_char(a.生产日期, 'yyyy-mm-dd') as prodDate,
        to_char(a.有效期, 'yyyy-mm-dd') as validDate,
        a.数量 as satmatNum,
        a.单价 as unitPrice,
        a.金额 as sumamt,
        a.厂家 as factory,
        a.供应商 as spler,
        case
        when a.货主 = '中江县人民医院直供' THEN
        A.供应商
        ELSE
        A.货主
        END as consignor,
        ROW_NUMBER() OVER(ORDER BY a.单号) AS rn
        from hcspd.v_spd_in_erp a
        left join (select b.单号
        from hcspd.v_spd_in_erp b
        where b.类型 = '采购退货'
        and b.入库时间 >= to_date('2024-01-01', 'yyyy-mm-dd')
        and b.单号 > #{floor,jdbcType=INTEGER}) m
        on a.单号 = m.单号
        WHERE A.类型 = '外采'
        AND A.单号 > #{floor,jdbcType=INTEGER}
        and A.入库时间 >= to_date('2024-01-01', 'yyyy-mm-dd')
        and m.单号 IS NULL
        ORDER BY A.单号)
        where rn between #{startRow,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
    </select>

    <select id="queryFloor" resultType="java.lang.Integer">
        select COALESCE(max(stoin_num),0) FROM ecs_satmat
    </select>

    <select id="monthNum" resultType="java.util.Map">
        select issue,
        count(1) as num
        from(
        select substring(stoin_date,1,7) as issue
        from ecs_satmat a
        <where>
            <if test="issue != null and issue != ''">
                and substring(a.stoin_date,1,7) = #{issue,jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != ''">
                and a.reim_flag = #{type,jdbcType=VARCHAR}
            </if>
            <if test="year != null and year != ''">
                and substring(a.stoin_date,1,4) = #{year,jdbcType=VARCHAR}
            </if>
        </where>
        ) b
        where length(issue) = 7
        group by issue
    </select>
</mapper>
