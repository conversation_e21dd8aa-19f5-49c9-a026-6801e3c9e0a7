<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.drugMgt.mapper.read.EcsSatmatReimDetailReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatReimDetailVo" id="satmatReimDetailMap">
        <result property="id" column="id"/>
        <result property="spler" column="spler"/>
        <result property="sum" column="sum"/>
        <result property="capSum" column="cap_sum"/>
        <result property="payIstr" column="pay_istr"/>
        <result property="auditBchno" column="audit_bchno"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="issue" column="issue"/>
        <result property="parentAuditBchno" column="parent_audit_bchno"/>
    </resultMap>

    <sql id="fields">
        a.id as id,
        a.spler as spler,
        a.sum as sum,
        a.cap_sum as capSum,
        a.pay_istr as payIstr,
        a.audit_bchno as auditBchno,
        a.crter as crter,
        a.create_time as createTime,
        a.hospital_id as hospitalId,
        a.issue as issue,
        a.parent_audit_bchno as parentAuditBchno
    </sql>

    <select id="queryList" resultType="com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatReimDetailVo">
        select <include refid="fields" />,
        b.emp_name as crterName,
        c.audit_res as auditState
        from ecs_satmat_reim_detail a
        left join hrm_employee_info b
        on a.crter = b.emp_code
        left join ecs_audit_res c
        on a.audit_bchno = c.bchno
        <where>
            <if test="auditBchno != null and auditBchno != ''">
                and a.audit_bchno = #{auditBchno,jdbcType=VARCHAR}
            </if>
            <if test="issue != null and issue != ''">
                and a.issue = #{issue,jdbcType=VARCHAR}
            </if>

            <if test="stoinNum != null and stoinNum != ''">
                and exists(
                select *
                from ecs_satmat b
                where a.id = b.satmat_reim_detail_id
                and b.stoin_num = #{stoinNum,jdbcType=VARCHAR}
                )
            </if>
        </where>
    </select>

    <!-- 查询审核数据 -->
    <select id="queryAuditData" resultType="com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatReimDetailVo">
        select <include refid="fields" />,
            c.audit_res as auditState
        from ecs_satmat_reim_detail a
        left join ecs_audit_res c
        on a.audit_bchno = c.bchno
        where a.audit_bchno in
        <foreach collection="childrenDetails" item="item" open="(" close=")" separator=",">
            #{item.bchno}
        </foreach>
        <if test='auditState != null and auditState != "" and auditState == "3"'>
            and not exists(
            select *
            from ecs_audit_rcdfm b
            where a.audit_bchno = b.bchno
            and b.chk_time is not null
            and b.chk_state = '0'
            )
        </if>
    </select>

    <!-- 查询审核归集数据 -->
    <select id="queryAuditCollData" resultType="com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatReimDetailVo">
        select b.*,
        case when position(#{chker,jdbcType=VARCHAR} in c.chker) > 0 then '1' else '0' end as auditFlag
        from(
        select parent_audit_bchno as auditBchno,
        string_agg(spler, ',') as spler,
        sum(sum) as sum
        from ecs_satmat_reim_detail a
        group by parent_audit_bchno
        ) b
        left join (
        select *
        from (
        select bchno,chker,chk_seq,min(chk_seq)over(partition by bchno) as min_seq
        from ecs_audit_rcdfm
        where chk_time is null
        ) a
        where min_seq = chk_seq
        ) c
        on b.auditBchno = c.bchno
    </select>
</mapper>
