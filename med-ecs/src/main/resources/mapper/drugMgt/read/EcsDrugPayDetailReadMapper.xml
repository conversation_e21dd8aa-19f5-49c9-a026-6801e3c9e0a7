<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugPayDetailReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo" id="drugPayDetailMap">
        <result property="id" column="id"/>
        <result property="attCode" column="att_code"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo">
        select
            id as id,
            att_code as attCode,
            crter as crter,
            create_time as createTime
        from ecs_drug_pay_detail
    </select>

</mapper>
