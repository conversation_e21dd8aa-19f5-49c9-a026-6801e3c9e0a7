<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.drugMgt.mapper.read.EcsStoinReadMapper">

    <!-- 更新审核状态 -->
    <update id="updateReimState">
        UPDATE ecs_stoin
        SET reim_flag = '1',
            drug_reim_detail_id = #{id,jdbcType=INTEGER}
        WHERE id IN
        <foreach collection="stoinIds" item="num" open="(" close=")" separator=",">
            #{num,jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="queryList" resultType="com.jp.med.common.vo.ecs.drug.EcsStoinVo">
        SELECT
        a.id AS id,
        a.stoin_num AS stoinNum,
        SUBSTRING(a.stoin_date, 1, 10) AS stoinDate,
        detail.totlcnt AS totlcnt,  <!-- 替换子查询为JOIN字段 -->
        a.rtal_amt AS rtalAmt,
        a.purcpric_amt AS purcpricAmt,
        a.invono AS invono,
        a.spler AS spler,
        a.reim_flag AS reimFlag,
        a.sync_date AS syncDate,
        a.hospital_id AS hospitalId,
        a.att,
        a.att_name AS attName,
        a.invo_id AS invoId,
        a.xh AS xh,
        a.stoin_type AS stoinType,
        a.is_back AS isBack,
        a.drug_reim_detail_id,
        b.spler AS reimSpler,
        b.issue AS reimIssue
    FROM
        ecs_stoin a
    LEFT JOIN
        ecs_drug_reim_detai b ON a.drug_reim_detail_id = b.id
        <!-- 新增预聚合的LEFT JOIN -->
        LEFT JOIN (
        SELECT
            zd_xh,
            is_back,
            SUM(drug_num) AS totlcnt
        FROM
            ecs_stoin_detail
        GROUP BY
            zd_xh,
            is_back
    ) detail ON a.xh = detail.zd_xh AND a.is_back = detail.is_back
    <where>
        <include refid="conditions" />
        <if test="drugReimDetailId != null">
            AND a.drug_reim_detail_id = #{drugReimDetailId,jdbcType=INTEGER}
        </if>
        <if test="stoinNum != null and stoinNum != ''">
            AND a.stoin_num LIKE CONCAT('%',#{stoinNum,jdbcType=VARCHAR},'%')
        </if>
        <if test="stoinDate != null and stoinDate != ''">
            <!-- 避免函数计算字段，建议数据库存储为时间戳 -->
            AND to_timestamp(a.stoin_date, 'YYYY-MM-DD HH24:MI:SS') &gt;= (#{stoinDate,jdbcType=VARCHAR}::DATE)
        </if>
        <if test="stoinEndDate != null and stoinEndDate != ''">
            AND to_timestamp(a.stoin_date, 'YYYY-MM-DD HH24:MI:SS') &lt; (#{stoinEndDate,jdbcType=VARCHAR}::DATE + 1)
        </if>
        <if test="createDate != null and createDate != ''">
            AND to_timestamp(a.sync_date, 'YYYY-MM-DD HH24:MI:SS') &gt;= (#{createDate,jdbcType=VARCHAR}::DATE)
        </if>
        <if test="createEndDate != null and createEndDate != ''">
            AND to_timestamp(a.sync_date, 'YYYY-MM-DD HH24:MI:SS') &lt; (#{createEndDate,jdbcType=VARCHAR}::DATE + 1)
        </if>
        <!-- 动态传入 stoin_type 值（如 '1099','1094'） -->
        <if test="stoinTypeStr != null and stoinTypeStr.size() != 0">
            AND a.stoin_type IN
            <foreach collection="stoinTypeStr" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="drugReimDetailIds != null and drugReimDetailIds.size() != 0">
            AND a.drug_reim_detail_id IN
            <foreach collection="drugReimDetailIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
    </where>
    ORDER BY a.spler  <!-- 确保spler字段有索引 -->
        <!--select
            a.id as id,
            a.stoin_num as stoinNum,
            substring(a.stoin_date,1,10) AS stoinDate,
            (select sum(b.drug_num) from ecs_stoin_detail b where b.zd_xh = a.xh and b.is_back = a.is_back) AS totlcnt,
            a.rtal_amt as rtalAmt,
            a.purcpric_amt as purcpricAmt,
            a.invono as invono,
            a.spler as spler,
            a.reim_flag as reimFlag,
            a.sync_date as syncDate,
            a.hospital_id as hospitalId,
            a.att,
            a.att_name as attName,
            a.invo_id as invoId,
            a.xh as xh,
            a.stoin_type as stoinType,
            a.is_back as isBack,
            a.drug_reim_detail_id,
            b.spler as reimSpler,
            b.issue as reimIssue
        from ecs_stoin a
        left join ecs_drug_reim_detai b on a.drug_reim_detail_id = b.id
        <where>
            <include refid="conditions" />
            <if test="drugReimDetailId != null">
                and a.drug_reim_detail_id = #{drugReimDetailId,jdbcType=INTEGER}
            </if>
            <if test="stoinNum != null and stoinNum != ''">
                and a.stoin_num like CONCAT('%',#{stoinNum,jdbcType=VARCHAR},'%')
            </if>
            <if test="stoinDate != null and stoinDate != ''">
                and to_timestamp(a.stoin_date, 'YYYY-MM-DD HH24:MI:SS') &gt;= (#{stoinDate,jdbcType=VARCHAR}::DATE)
            </if>
            <if test="stoinEndDate != null and stoinEndDate != ''">
                and to_timestamp(a.stoin_date, 'YYYY-MM-DD HH24:MI:SS') &lt; (#{stoinEndDate,jdbcType=VARCHAR}::DATE + 1)
            </if>
            <if test="createDate != null and createDate != ''">
                and to_timestamp(a.sync_date, 'YYYY-MM-DD HH24:MI:SS') &gt;= (#{createDate,jdbcType=VARCHAR}::DATE)
            </if>
            <if test="createEndDate != null and createEndDate != ''">
                and to_timestamp(a.sync_date, 'YYYY-MM-DD HH24:MI:SS') &lt; (#{createEndDate,jdbcType=VARCHAR}::DATE + 1)
            </if>
            <if test="stoinTypeStr != null and stoinTypeStr.size() != 0">
                and a.stoin_type in
                <foreach collection="stoinTypeStr" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="drugReimDetailIds != null and drugReimDetailIds.size() != 0">
                and a.drug_reim_detail_id in
                <foreach collection="drugReimDetailIds" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        order by a.spler-->
    </select>

    <!-- 查询月份数量 -->
    <select id="monthNum" resultType="java.util.Map">
        select issue,
               count(1) as num
        from(
            select substring(stoin_date,1,7) as issue
            from ecs_stoin a
            <!--select * from (
            select substring(stoin_date,1,7) as issue
            from ecs_stoin a

            union all

            select substring(back_date,1,7) as issue
            from ecs_stoin_back a) a-->
            <where>
                <include refid="conditions" />
                <if test="year != null and year != ''">
                    and substring(a.stoin_date,1,4) = #{year,jdbcType=VARCHAR}
                </if>
            </where>
        ) b
        where length(issue) = 7
        group by issue
    </select>

    <!-- 药品数据同步-查询最大序号 -->
    <select id="queryMaxXh" resultType="java.lang.Integer">
        SELECT COALESCE(max(xh),0) FROM "ecs_stoin" where is_back = #{isBack,jdbcType=VARCHAR}
    </select>

    <sql id="conditions">
        <if test="issue != null and issue != ''">
            and substring(a.stoin_date,1,7) = #{issue,jdbcType=VARCHAR}
        </if>
        <if test="type != null and type != ''">
            and a.reim_flag = #{type,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="selectNum" resultType="java.lang.Integer">
        select count(1) from hcspd.v_spd_in_erp
    </select>

    <select id="list" resultType="com.jp.med.common.dto.ecs.drug.EcsStoinDto">
        SELECT
        xh,
        RTRIM(rkdh) AS stoinNum,
        rkrq AS stoinDate,
        0 AS totlcnt,
        lsje AS rtalAmt,
        jjje AS purcpricAmt,
        ghdwmc AS spler,
        jxje AS jxje,
        'zjxrmyy' AS hospitalId,
        RTRIM(ksdm) as stoinType,
        '0' AS isBack
        FROM
        YK_YPRKZD
        WHERE
        xh > #{floor,jdbcType=INTEGER}
        and SUBSTRING(rkrq, 1, 4) >= '2024'
        and jzbz != '0'              <!-- 未记账的不同步 0：未记账 1：记账 2：台账记账-->
        ORDER BY xh
        offset #{offset,jdbcType=INTEGER} ROWS
        FETCH NEXT #{batchSize,jdbcType=INTEGER} ROWS ONLY
    </select>

    <select id="listStoinDetails" resultType="com.jp.med.ecs.modules.drugMgt.dto.EcsStoinDetailDto">
        SELECT
            A.zd_xh as zdXh,		--入库序号
            A.ypmc as drugName,			--[药品名称]
            A.ph as batchNum,					--[批号]
            RTRIM(A.ypdw) as unit,			--[单位]
            CONVERT(DECIMAL(10,2), A.rksl/A.ykxs) as drugNum, --[数量]
            A.ypjj as purcPrice,			--[购入价]
            A.jjje as purcpricAmt,			--购入金额
            A.ylsj as rtalPrice,			--[零售价]
            A.lsje as rtalpricAmt,			--[零售金额]
            '0' AS isBack                   --[是否退货]
        FROM YK_YPRKMX A
        WHERE zd_xh IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="listStoinBack" resultType="com.jp.med.common.dto.ecs.drug.EcsStoinDto">
        SELECT
            xh,
            RTRIM( djh ) AS stoinNum,
            lrrq AS stoinDate,
            0 AS totlcnt,
            ypje_ls AS rtalAmt,
            thje AS purcpricAmt,
            ghdwmc AS spler,
            jxje AS jxje,
            'zjxrmyy' AS hospitalId,
            RTRIM(ksdm) as stoinType,
            '1' AS isBack
        FROM
            YK_YPTHZD
        WHERE
            xh > #{floor,jdbcType=INTEGER}
          AND SUBSTRING ( lrrq, 1, 4 ) >= '2024'
        ORDER BY xh
        offset #{offset,jdbcType=INTEGER} ROWS
        FETCH NEXT #{batchSize,jdbcType=INTEGER} ROWS ONLY
    </select>

    <select id="listStoinBackDetails" resultType="com.jp.med.ecs.modules.drugMgt.dto.EcsStoinDetailDto">
        SELECT
            A.zd_xh as zdXh,		--入库序号
            A.ypmc as drugName,			--[药品名称]
            A.ph as batchNum,					--[批号]
            RTRIM(A.ypdw) as unit,			--[单位]
            CONVERT(DECIMAL(10,2), A.thsl/A.ykxs) as drugNum, --[数量]
            A.ypjj as purcPrice,			--[购入价]
            A.jjje as purcpricAmt,			--购入金额
            A.thdj as rtalPrice,			--[零售价]
            A.thje as rtalpricAmt,			--[零售金额]
            '1' AS isBack                   --[是否退货]
        FROM YK_YPTHMX A
        WHERE zd_xh IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="queryStoinDetails" resultType="com.jp.med.ecs.modules.drugMgt.vo.EcsStoinDetailVo">
        SELECT A.ID as id,
               A.zd_xh as zdXh,
               A.drug_name as drugName,
               A.batch_num as batchNum,
               A.unit,
               A.drug_num as drugNum,
               A.purc_price as purcPrice,
               A.purcpric_amt as purcpricAmt,
               A.rtal_price as rtalPrice,
               A.rtalpric_amt as rtalpricAmt,
               A.is_back as isBack
        FROM
            ecs_stoin_detail A
        WHERE
            A.zd_xh = #{xh,jdbcType=INTEGER}
            and a.is_back =#{isBack,jdbcType=VARCHAR}
    </select>
</mapper>
