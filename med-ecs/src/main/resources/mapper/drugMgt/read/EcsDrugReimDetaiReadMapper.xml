<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugReimDetaiReadMapper">


    <sql id="fields">
        a.id as id,
        a.spler as spler,
        a.sum as sum,
        a.cap_sum as capSum,
        a.pay_istr as payIstr,
        a.audit_bchno as auditBchno,
        a.crter as crter,
        a.craete_time as craeteTime,
        a.hospital_id as hospitalId,
        a.issue as issue,
        a.parent_audit_bchno as parentAuditBchno,
        a.status as status,
        a.drug_pay_id as drugPayId,
        a.drug_pay_type as drugPayType,
        a.pay_method as payMethod,
        a.pay_istr2 as payIstr2,
        a.process_instance_id as processInstanceId
    </sql>

    <select id="queryList" resultType="com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo">
        select <include refid="fields" />,
                d.att,
                d.attName,
               b.emp_name as crterName,
               c.audit_res as auditState
        from ecs_drug_reim_detai a
        left join hrm_employee_info b
        on a.crter = b.emp_code
        left join ecs_audit_res c
        on a.audit_bchno = c.bchno
        left join (select a.id,STRING_AGG(b.att,',') as att,STRING_AGG(b.att_name,',') as attName
        from ecs_drug_pay_detail a,ecs_reim_file_record b
        where a.att_code = b.att_code and b."type" = '2' group by a.id) d on a.drug_pay_id = d.id
        <where>
            <if test="id != null">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="ids != null and ids.size() != 0">
                and a.id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="auditBchno != null and auditBchno != ''">
                and a.audit_bchno = #{auditBchno,jdbcType=VARCHAR}
            </if>
            <if test="issue != null and issue != ''">
                and a.issue = #{issue,jdbcType=VARCHAR}
            </if>
            <if test="parentAuditBchno !=null and parentAuditBchno !=''">
                and a.parent_audit_bchno = #{parentAuditBchno,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null and startDate != ''">
                and substring(a.craete_time, 1, 10) &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="spler != null and spler != ''">
                and a.spler like concat('%',#{spler,jdbcType=VARCHAR},'%')
            </if>
            <!-- 是否集采 -->
            <if test="cpFlag != null and cpFlag != ''">
                <choose>
                    <when test='cpFlag == "1"'>
                        and a.spler like '%集采%'
                    </when>
                    <otherwise>
                        and a.spler not like '%集采%'
                    </otherwise>
                </choose>
            </if>
            <if test="endDate != null and endDate != ''">
                and substring(a.craete_time, 1, 10) &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
            <if test="stoinNum != null and stoinNum != ''">
                and exists(
                    select *
                    from ecs_stoin b
                    where a.id = b.drug_reim_detail_id
                    and b.stoin_num = #{stoinNum,jdbcType=VARCHAR}
                )
            </if>
            <if test="stoinTypeStr != null and stoinTypeStr.size() != 0">
                and exists(
                    select *
                    from ecs_stoin b
                    where a.id = b.drug_reim_detail_id
                    and b.stoin_type in
                    <foreach collection="stoinTypeStr" item="item" separator="," open="(" close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                )
            </if>
        </where>
        order by a.update_time, a.craete_time
    </select>

    <!-- 查询审核数据 -->
    <select id="queryAuditData" resultType="com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo">
        select <include refid="fields" />,
	           c.audit_res as auditState
        from ecs_drug_reim_detai a
        left join ecs_audit_res c
        on a.audit_bchno = c.bchno
        where a.audit_bchno in
        <foreach collection="childrenDetails" item="item" open="(" close=")" separator=",">
            #{item.bchno}
        </foreach>
        <if test='auditState != null and auditState != "" and auditState == "3"'>
            and not exists(
                select *
                from ecs_audit_rcdfm b
                where a.audit_bchno = b.bchno
                and b.chk_time is not null
                and b.chk_state = '0'
            )
        </if>
    </select>

    <!-- 查询审核归集数据 -->
    <select id="queryAuditCollData"
            resultType="com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo">
        <!--select b.*,
               case when position(#{chker,jdbcType=VARCHAR} in c.chker) > 0 then '1' else '0' end as auditFlag
        from(
            &lt;!&ndash;select parent_audit_bchno as auditBchno,
                   string_agg(spler, ',') as spler,
                   sum(sum) as sum
            from ecs_drug_reim_detai a
            group by parent_audit_bchno&ndash;&gt;
            select parent_audit_bchno as auditBchno,
                string_agg(spler, ',') as spler,
                sum(sum) as sum,
                string_agg(b.emp_name, ',') as crter,
                string_agg(a.craete_time, ',') as craeteTime
            from ecs_drug_reim_detai a
            left join hrm_employee_info b on a.crter = b.emp_code
            group by parent_audit_bchno
        ) b
        left join (
            select *
            from (
                select bchno,chker,chk_seq,min(chk_seq)over(partition by bchno) as min_seq
                from ecs_audit_rcdfm m
                where chk_time is null and exists (select 1 from ecs_audit_res n where m.bchno = n.bchno and n.audit_res = '3')
            ) a
            where min_seq = chk_seq
        ) c
        on b.auditBchno = c.bchno-->
        <!--SELECT M.auditBchno,
            M.spler,
            M.SUM,
            ( string_to_array( M.crter, ',' ) ) [ 1 ] AS crter,
            ( string_to_array( M.craeteTime, ',' ) ) [ 1 ] AS craeteTime,
            M.auditFlag
        FROM
        (
            SELECT
                b.*,
                CASE WHEN POSITION ( #{chker,jdbcType=VARCHAR} IN C.chker ) > 0 THEN '1' ELSE'0' END AS auditFlag
            FROM
            (
                SELECT
                    parent_audit_bchno AS auditBchno,
                    string_agg ( spler, ',' ) AS spler,
                    SUM ( SUM ) AS SUM,
                    string_agg ( b.emp_name, ',' ) AS crter,
                    string_agg ( A.craete_time, ',' ) AS craeteTime
                FROM
                    ecs_drug_reim_detai
                    A LEFT JOIN hrm_employee_info b ON A.crter = b.emp_code
                GROUP BY
                    parent_audit_bchno
            ) b
            LEFT JOIN (
                SELECT
                *
                FROM
                (
                    SELECT
                        bchno,
                        chker,
                        chk_seq,
                        MIN ( chk_seq ) OVER ( PARTITION BY bchno ) AS min_seq
                    FROM
                        ecs_audit_rcdfm M
                WHERE
                    chk_time IS NULL
                    AND EXISTS ( SELECT 1 FROM ecs_audit_res n WHERE M.bchno = n.bchno AND n.audit_res = '3' )
            ) A
            WHERE
            min_seq = chk_seq
            ) C ON b.auditBchno = C.bchno
        ) M
        where M.auditFlag = #{auditFlag,jdbcType=VARCHAR}-->

        SELECT
        M.auditBchno,
        M.spler,
        M.SUM,
        ( string_to_array( M.crter, ',' ) ) [ 1 ] AS crter,
        ( string_to_array( M.craeteTime, ',' ) ) [ 1 ] AS craeteTime,
        M.auditFlag,
        M.tbAudit
    FROM
        (
        SELECT
            b.*,
        CASE WHEN POSITION ( #{chker,jdbcType=VARCHAR} IN C.chker ) > 0 THEN
                '1' ELSE'0'
            END AS auditFlag,
        CASE WHEN position(#{chker,jdbcType=VARCHAR} in d.chker) > 0 then '1'  else '0' end as tbAudit
        FROM
            (
            SELECT
                parent_audit_bchno AS auditBchno,
                string_agg ( spler, ',' ) AS spler,
                SUM ( SUM ) AS SUM,
                string_agg ( b.emp_name, ',' ) AS crter,
                string_agg ( A.craete_time, ',' ) AS craeteTime
            FROM
                ecs_drug_reim_detai
                A LEFT JOIN hrm_employee_info b ON A.crter = b.emp_code
            GROUP BY
                parent_audit_bchno
            ) b
            LEFT JOIN (
            SELECT
                *
            FROM
                (
                SELECT
                    bchno,
                    chker,
                    chk_seq,
                    MIN ( chk_seq ) OVER ( PARTITION BY bchno ) AS min_seq
                FROM
                    ecs_audit_rcdfm M
                WHERE
                    chk_time IS NULL
                ) A
            WHERE
                min_seq = chk_seq
            ) C ON b.auditBchno = C.bchno
            LEFT JOIN (
                select bchno, string_agg(chker, ',') as chker
                from ecs_audit_rcdfm
                where chk_time is not null
                group by bchno
            ) d
            on b.auditBchno = d.bchno
        ) M
    WHERE

        <choose>
            <!-- 待审核 -->
            <when test='auditFlag =="1"'>
                M.auditFlag = #{auditFlag,jdbcType=VARCHAR}
            </when>
            <!-- 已审核 -->
            <when test='auditFlag =="0"'>
                M.tbAudit = '1'
            </when>
        </choose>
        <if test="spler != null and spler != ''">
            AND M.spler like concat('%',#{spler,jdbcType=VARCHAR},'%')
        </if>
    </select>

</mapper>
