<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.drugMgt.mapper.write.EcsStoinWriteMapper">
    <insert id="saveDrugStoin">
        INSERT INTO ecs_stoin(
        xh,
        stoin_num,
        stoin_date,
        totlcnt,
        rtal_amt,
        purcpric_amt,
        spler,
        sync_date,
        hospital_id,
        reim_flag,
        stoin_type,
        is_back
        ) values
        <foreach collection="stoins" item="item" separator=",">
            (
            #{item.xh},
            #{item.stoinNum},
            TO_CHAR(to_timestamp(#{item.stoinDate},'YYYYMMDDHH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS'),
            #{item.totlcnt},
            #{item.rtalAmt},
            #{item.purcpricAmt},
            #{item.spler},
            to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
            #{item.hospitalId},
            '0',
            #{item.stoinType},
            #{item.isBack}
            )
        </foreach>
    </insert>

    <insert id="saveDrugStoinDetail">
        INSERT INTO ecs_stoin_detail(
            zd_xh,
            drug_name,
            batch_num,
            unit,
            drug_num,
            purc_price,
            purcpric_amt,
            rtal_price,
            rtalpric_amt,
            is_back
        ) VALUES
        <foreach collection="stoinDetails" item="detail" separator=",">
            (
            #{detail.zdXh,jdbcType=INTEGER},
            #{detail.drugName,jdbcType=VARCHAR},
            #{detail.batchNum,jdbcType=VARCHAR},
            #{detail.unit,jdbcType=VARCHAR},
            #{detail.drugNum,jdbcType=DOUBLE},
            #{detail.purcPrice,jdbcType=DOUBLE},
            #{detail.purcpricAmt,jdbcType=DOUBLE},
            #{detail.rtalPrice,jdbcType=DOUBLE},
            #{detail.rtalpricAmt,jdbcType=DOUBLE},
            #{detail.isBack,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
