<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsActigCfgReadMapper">

    <select id="queryList" resultType="com.jp.med.ecs.modules.config.vo.EcsActigCfgVo">
        select
            a.id as id,
            a.sub_code as subCode,
            a.sub_name as subName,
            a.sub_type as subType,
            a.pinyin as pinyin,
            a.remarks as remarks,
            a.asst_info as asstInfo,
            a.actig_elem as actigElem,
            a.actig_sys as actigSys,
            a.balc_dirc as balcDirc,
            a.emp_type as empType,
            a.crter as crter,
            a.create_time as createTime,
            a.modi_time as modiTime,
            a.hospital_id as hospitalId,
            a.active_flag as activeFlag,
            a.parent_code as parentCode
        from ecs_actig_cfg a
        <where>
            <choose>
                <when test="year != null and year != ''">
                    and a.year = #{year,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and a.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
                </otherwise>
            </choose>
            <if test="sub != null and sub != ''">
                and (a.sub_code like concat('%',#{sub,jdbcType=VARCHAR},'%')
                or a.sub_name like concat('%',#{sub,jdbcType=VARCHAR},'%'))
            </if>
            <if test="status != null and status != ''">
                and a.actig_sys = #{status,jdbcType=VARCHAR}
            </if>
            <if test="activeFlag != null and activeFlag != ''">
                and a.active_flag = #{activeFlag,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.id
    </select>

</mapper>
