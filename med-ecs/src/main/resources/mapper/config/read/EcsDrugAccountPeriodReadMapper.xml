<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsDrugAccountPeriodReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.vo.EcsDrugAccountPeriodVo" id="drugAccountPeriodMap">
        <result property="id" column="id"/>
        <result property="accountPeriod" column="account_period"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.vo.EcsDrugAccountPeriodVo">
        select
            id as id,
            account_period as accountPeriod,
            crter as crter,
            create_time as createTime
        from ecs_drug_account_period
        order by id desc
    </select>

</mapper>
