<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsActigAsstDeptCfgReadMapper">

    <select id="queryList" resultType="com.jp.med.ecs.modules.config.vo.EcsActigAsstDeptCfgVo">
        select
            a.id as id,
            a.dept_code as deptCode,
            a.dept_name as deptName,
            a.pinyin as pinyin,
            a.remarks as remarks,
            a.crter as crter,
            a.create_time as createTime,
            a.modi_time as modiTime,
            a.hospital_id as hospitalId,
            a.active_flag as activeFlag,
            a.parent_dept_code as parentDeptCode,
            a.dept_code as value,
            a.dept_name as label
        from ecs_actig_asst_dept_cfg a
        <where>
            <if test="qs != null and qs != ''">
                and (a.dept_code like concat('%',#{qs,jdbcType=VARCHAR},'%')
                or a.dept_name like concat('%',#{qs,jdbcType=VARCHAR},'%')
                or a.pinyin like concat('%',upper(#{qs,jdbcType=VARCHAR}),'%')
                )
            </if>
            <if test="activeFlag != null and activeFlag != ''">
                and a.active_flag = #{activeFlag,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.create_time desc
    </select>

</mapper>
