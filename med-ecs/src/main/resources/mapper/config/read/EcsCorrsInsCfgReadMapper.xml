<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsCorrsInsCfgReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.config.vo.EcsCorrsInsCfgVo" id="corrsInsCfgMap">
        <result property="id" column="id"/>
        <result property="insCode" column="ins_code"/>
        <result property="insName" column="ins_name"/>
        <result property="fulname" column="fulname"/>
        <result property="insType" column="ins_type"/>
        <result property="commAddr" column="comm_addr"/>
        <result property="poscode" column="poscode"/>
        <result property="conerName" column="coner_name"/>
        <result property="conerTel" column="coner_tel"/>
        <result property="fax" column="fax"/>
        <result property="email" column="email"/>
        <result property="insNatu" column="ins_natu"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="modiTime" column="modi_time"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="activeFlag" column="active_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ecs.modules.config.vo.EcsCorrsInsCfgVo">
        select
            a.id as id,
            a.ins_code as insCode,
            a.ins_name as insName,
            a.fulname as fulname,
            a.ins_type as insType,
            a.comm_addr as commAddr,
            a.poscode as poscode,
            a.coner_name as conerName,
            a.coner_tel as conerTel,
            a.fax as fax,
            a.email as email,
            a.ins_natu as insNatu,
            a.crter as crter,
            a.create_time as createTime,
            a.modi_time as modiTime,
            a.hospital_id as hospitalId,
            a.active_flag as activeFlag,
            a.ins_code as value,
            a.ins_name as label
        from ecs_corrs_ins_cfg a
        <where>
            <choose>
                <when test="year != null and year != ''">
                    and a.year = #{year,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and a.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
                </otherwise>
            </choose>
            <if test="qs != null and qs != ''">
                and (a.ins_code like concat('%',#{qs,jdbcType=VARCHAR},'%')
                or a.ins_name like concat('%',#{qs,jdbcType=VARCHAR},'%')
                or a.fulname like concat('%',upper(#{qs,jdbcType=VARCHAR}),'%')
                )
            </if>
            <if test="activeFlag != null and activeFlag != ''">
                and a.active_flag = #{activeFlag,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
