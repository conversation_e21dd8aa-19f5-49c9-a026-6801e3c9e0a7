<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsItemCfgReadMapper">

    <select id="queryList" resultType="com.jp.med.ecs.modules.config.vo.EcsItemCfgVo">
        select
            a.id as id,
            a.item_code as itemCode,
            a.item_name as itemName,
            a.pinyin as pinyin,
            a.begn_date as begnDate,
            a.end_date as endDate,
            a.dept as dept,
            a.resper as resper,
            a.esta_year as estaYear,
            a.crter as crter,
            a.create_time as createTime,
            a.modi_time as modiTime,
            a.hospital_id as hospitalId,
            a.active_flag as activeFlag,
            a.parent_item_code as parentItemCode,
            a.item_code as value,
            a.item_name as label
        from ecs_item_cfg a
        <where>
            <choose>
                <when test="year != null and year != ''">
                    and a.year = #{year,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and a.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
                </otherwise>
            </choose>
            <if test="qs != null and qs != ''">
                and (a.item_code like concat('%',#{qs,jdbcType=VARCHAR},'%')
                or a.item_name like concat('%',#{qs,jdbcType=VARCHAR},'%')
                or a.pinyin like concat('%',upper(#{qs,jdbcType=VARCHAR}),'%')
                )
            </if>
            <if test="activeFlag != null and activeFlag != ''">
                and a.active_flag = #{activeFlag,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
