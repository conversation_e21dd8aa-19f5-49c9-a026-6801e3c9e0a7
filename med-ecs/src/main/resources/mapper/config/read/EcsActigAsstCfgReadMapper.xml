<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsActigAsstCfgReadMapper">

    <select id="queryList" resultType="com.jp.med.ecs.modules.config.vo.EcsActigAsstCfgVo">
        select
            a.id as id,
            a.asst_code as asstCode,
            a.asst_name as asstName,
            a.pinyin as pinyin,
            a.remarks as remarks,
            a.crter as crter,
            a.create_time as createTime,
            a.modi_time as modiTime,
            a.hospital_id as hospitalId,
            a.active_flag as activeFlag,
            a.data_souc as dataSouc
        from ecs_actig_asst_cfg a
        <where>
            <if test="asst != null and asst != ''">
                and (a.asst_code like concat('%',#{asst,jdbcType=VARCHAR},'%')
                or a.asst_name like concat('%',#{asst,jdbcType=VARCHAR},'%'))
            </if>
        </where>
        order by a.create_time desc
    </select>

</mapper>
