<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsReimFixedAsstDetailReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.vo.EcsReimFixedAsstDetailVo" id="reimFixedAsstDetailMap">
        <result property="id" column="id"/>
        <result property="fixedAsstId" column="fixed_asst_id"/>
        <result property="payTypeCode" column="pay_type_code"/>
        <result property="payTypeName" column="pay_type_name"/>
        <result property="actigSubCode" column="actig_sub_code"/>
        <result property="actigSubName" column="actig_sub_name"/>
        <result property="actigSys" column="actig_sys"/>
        <result property="deptCode" column="dept_code"/>
        <result property="deptName" column="dept_name"/>
        <result property="relCoCode" column="rel_co_code"/>
        <result property="relCoName" column="rel_co_name"/>
        <result property="funSubCode" column="fun_sub_code"/>
        <result property="funSubName" column="fun_sub_name"/>
        <result property="econSubCode" column="econ_sub_code"/>
        <result property="econSubName" column="econ_sub_name"/>
        <result property="projCode" column="proj_code"/>
        <result property="projName" column="proj_name"/>
        <result property="cashFlowCode" column="cash_flow_code"/>
        <result property="cashFlowName" column="cash_flow_name"/>
        <result property="actigAmtType" column="actig_amt_type"/>
        <result property="abst" column="abst"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.vo.EcsReimFixedAsstDetailVo">
        select
            id as id,
            fixed_asst_id as fixedAsstId,
            pay_type_code as payTypeCode,
            pay_type_name as payTypeName,
            actig_sub_code as actigSubCode,
            actig_sub_name as actigSubName,
            actig_sys as actigSys,
            dept_code as deptCode,
            dept_name as deptName,
            rel_co_code as relCoCode,
            rel_co_name as relCoName,
            fun_sub_code as funSubCode,
            fun_sub_name as funSubName,
            econ_sub_code as econSubCode,
            econ_sub_name as econSubName,
            proj_code as projCode,
            proj_name as projName,
            cash_flow_code as cashFlowCode,
            cash_flow_name as cashFlowName,
            fund_type as fundType,
            fund_type_name as fundTypeName,
            actig_amt_type as actigAmtType,
            abst as abst,
            crter as crter,
            create_time as createTime
        from ecs_reim_fixed_asst_detail
        <where>
            <if test="fixedAsstId !=null and fixedAsstId !=''">
                and fixed_asst_id = #{fixedAsstId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
