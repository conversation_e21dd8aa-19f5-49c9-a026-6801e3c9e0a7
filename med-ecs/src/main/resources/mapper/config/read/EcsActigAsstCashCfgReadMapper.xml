<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsActigAsstCashCfgReadMapper">

    <select id="queryList" resultType="com.jp.med.ecs.modules.config.vo.EcsActigAsstCashCfgVo">
        select
            a.id as id,
            a.sub_code as subCode,
            a.sub_name as subName,
            a.flow_dire as flowDire,
            a.pinyin as pinyin,
            a.remarks as remarks,
            a.crter as crter,
            a.create_time as createTime,
            a.modi_time as modiTime,
            a.hospital_id as hospitalId,
            a.active_flag as activeFlag,
            a.parent_sub_code as parentSubCode,
            a.sub_code as value,
            a.sub_name as label
        from ecs_actig_asst_cash_cfg a
        <where>
            <choose>
                <when test="year != null and year != ''">
                    and a.year = #{year,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and a.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
                </otherwise>
            </choose>
            <if test="qs != null and qs != ''">
                and (a.sub_code like concat('%',#{qs,jdbcType=VARCHAR},'%')
                or a.sub_name like concat('%',#{qs,jdbcType=VARCHAR},'%')
                or a.pinyin like concat('%',upper(#{qs,jdbcType=VARCHAR}),'%')
                )
            </if>
            <if test="activeFlag != null and activeFlag != ''">
                and a.active_flag = #{activeFlag,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.create_time desc
    </select>

</mapper>
