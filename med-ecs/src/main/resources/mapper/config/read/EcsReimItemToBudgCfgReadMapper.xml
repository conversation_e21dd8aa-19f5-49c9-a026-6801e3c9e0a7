<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsReimItemToBudgCfgReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.vo.ecs.EcsReimItemToBudgCfgVo" id="reimItemToBudgCfgMap">
        <result property="id" column="id"/>
        <result property="reimItemCode" column="reim_item_code"/>
        <result property="reimItemName" column="reim_item_name"/>
        <result property="budgetCode" column="budget_code"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="modiTime" column="modi_time"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="activeFlag" column="active_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.vo.ecs.EcsReimItemToBudgCfgVo">
        select
            a.id as id,
            a.reim_item_code as reimItemCode,
            a.reim_item_name as reimItemName,
            a.budget_code as budgetCode,
            a.crter as crter,
            a.create_time as createTime,
            a.modi_time as modiTime,
            a.hospital_id as hospitalId,
            a.active_flag as activeFlag,
            a."year" as "year",
            a.type,
            b.budget_name as budgetName,
            a.bgt_summary as bgtSummary
        from ecs_reim_item_to_budg_cfg a
        left join bms_budget_proj b
        on a.budget_code = b.budget_code
        and a.year = b.budget_year
        <where>
            a.year = #{year,jdbcType=VARCHAR}
            and a.type = #{type,jdbcType=VARCHAR}
            <if test="reimItemCode != null and reimItemCode != ''">
            and a.reim_item_code = #{reimItemCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
