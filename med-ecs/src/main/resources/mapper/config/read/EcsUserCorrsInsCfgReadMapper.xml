<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsUserCorrsInsCfgReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.config.vo.EcsUserCorrsInsCfgVo" id="userCorrsInsCfgMap">
        <result property="id" column="id"/>
        <result property="empCode" column="emp_code"/>
        <result property="insCode" column="ins_code"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="year" column="year"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="activeFlag" column="active_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ecs.modules.config.vo.EcsUserCorrsInsCfgVo">
        select
            id as id,
            emp_code as empCode,
            ins_code as insCode,
            crter as crter,
            create_time as createTime,
            year as year,
            hospital_id as hospitalId,
            active_flag as activeFlag
        from ecs_user_corrs_ins_cfg
    </select>

</mapper>
