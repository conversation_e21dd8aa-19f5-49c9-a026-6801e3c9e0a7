<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.write.EcsReimFixedAsstDetailWriteMapper">
    <delete id="removeByFixedAsstId" parameterType="java.lang.Integer">
        DELETE FROM ecs_reim_fixed_asst_detail
        WHERE fixed_asst_id = #{id}
    </delete>

    <insert id="addFixedDetails">
        INSERT INTO ecs_reim_fixed_asst_detail (
        fixed_asst_id,
        pay_type_code,
        pay_type_name,
        actig_sub_code,
        actig_sub_name,
        actig_sys,
        dept_code,
        dept_name,
        rel_co_code,
        rel_co_name,
        fun_sub_code,
        fun_sub_name,
        econ_sub_code,
        econ_sub_name,
        proj_code,
        proj_name,
        cash_flow_code,
        cash_flow_name,
        fund_type,
        fund_type_name,
        actig_amt_type,
        abst,
        crter,
        create_time
        )
        VALUES
        <foreach collection="fixedAsstDetails" item="item" separator=",">
            (
            #{item.fixedAsstId,jdbcType=INTEGER},
            #{item.payTypeCode,jdbcType=VARCHAR},
            #{item.payTypeName,jdbcType=VARCHAR},
            #{item.actigSubCode,jdbcType=VARCHAR},
            #{item.actigSubName,jdbcType=VARCHAR},
            #{item.actigSys,jdbcType=VARCHAR},
            #{item.deptCode,jdbcType=VARCHAR},
            #{item.deptName,jdbcType=VARCHAR},
            #{item.relCoCode,jdbcType=VARCHAR},
            #{item.relCoName,jdbcType=VARCHAR},
            #{item.funSubCode,jdbcType=VARCHAR},
            #{item.funSubName,jdbcType=VARCHAR},
            #{item.econSubCode,jdbcType=VARCHAR},
            #{item.econSubName,jdbcType=VARCHAR},
            #{item.projCode,jdbcType=VARCHAR},
            #{item.projName,jdbcType=VARCHAR},
            #{item.cashFlowCode,jdbcType=VARCHAR},
            #{item.cashFlowName,jdbcType=VARCHAR},
            #{item.fundType,jdbcType=VARCHAR},
            #{item.fundTypeName,jdbcType=VARCHAR},
            #{item.actigAmtType,jdbcType=VARCHAR},
            #{item.abst,jdbcType=VARCHAR},
            #{item.crter,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
