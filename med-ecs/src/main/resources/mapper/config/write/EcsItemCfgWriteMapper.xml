<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.write.EcsItemCfgWriteMapper">
    <insert id="saveItemCfg">
        INSERT INTO ecs_item_cfg (
            item_code,
            item_name,
            pinyin,
            begn_date,
            end_date,
            esta_year,
            crter,
            create_time,
            hospital_id,
            active_flag,
            parent_item_code,
            year
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.itemCode,jdbcType=VARCHAR},
                #{item.itemName,jdbcType=VARCHAR},
                #{item.pinyin,jdbcType=VARCHAR},
                #{item.begnDate,jdbcType=VARCHAR},
                #{item.endDate,jdbcType=VARCHAR},
                #{item.estaYear,jdbcType=VARCHAR},
                #{item.crter,jdbcType=VARCHAR},
                to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
                'zjxrmyy',
                '1',
                #{item.parentItemCode,jdbcType=VARCHAR},
                #{item.year,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
