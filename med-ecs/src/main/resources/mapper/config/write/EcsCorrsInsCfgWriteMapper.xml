<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.write.EcsCorrsInsCfgWriteMapper">

    <insert id="saveCorrsInsCfg">
        INSERT INTO ecs_corrs_ins_cfg (
            ins_code,
            ins_name,
            fulname,
            ins_type,
            crter,
            create_time,
            hospital_id,
            active_flag,
            year
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.insCode,jdbcType=VARCHAR},
                #{item.insName,jdbcType=VARCHAR},
                #{item.fulname,jdbcType=VARCHAR},
                #{item.insType,jdbcType=VARCHAR},
                #{item.crter,jdbcType=VARCHAR},
                to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
                'zjxrmyy',
                '1',
                #{item.year,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
