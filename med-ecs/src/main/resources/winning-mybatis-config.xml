<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <!-- 设置全局属性 -->
    <settings>
        <!-- 开启驼峰命名自动映射 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>

    <!-- 配置数据源 -->
    <environments default="postgresql">
        <!-- 用友数据库 -->
        <environment id="sqlServer">
            <!-- 使用JDBC事务管理 -->
            <transactionManager type="JDBC"/>
            <!-- 配置数据源 -->
            <dataSource type="POOLED">
                <!-- SQL Server 驱动 -->
                <property name="driver" value="com.microsoft.sqlserver.jdbc.SQLServerDriver"/>
                <!-- SQL Server 数据库连接 URL -->
                <property name="url" value="***********************************************************************"/>
                <property name="username" value="zjrmyy_hrp"/>
                <property name="password" value="hrp@11223"/>
            </dataSource>
        </environment>
        <!-- 卫林 -->
        <environment id="winning">
            <!-- 使用JDBC事务管理 -->
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <!-- SQL Server 驱动 -->
                <property name="driver" value="com.microsoft.sqlserver.jdbc.SQLServerDriver"/>
                <!-- SQL Server 数据库连接 URL -->
                <property name="url" value="******************************************************************"/>
                <property name="username" value="winning"/>
                <property name="password" value="sql2012!"/>
            </dataSource>
        </environment>
        <!-- 卫材入库单数据源 -->
        <environment id="sanMat">
            <transactionManager type="JDBC" />
            <dataSource type="POOLED">
                <property name="driver" value="oracle.jdbc.driver.OracleDriver" />
                <property name="url" value="***************************************" />
                <property name="username" value="ERP_ITF_VS" />
                <property name="password" value="Erp_123456!!"/>
            </dataSource>
        </environment>
    </environments>

    <!-- 配置映射器 -->
    <mappers>
        <!-- 扫描映射器接口所在的包 -->
        <!--        <package name="com.jp.med.hrm.modules.salaryManagement.**.mapper"/>-->
        <!-- 或者逐个指定映射器接口 -->
        <mapper resource="mapper/drugMgt/read/EcsStoinReadMapper.xml"/>
        <mapper resource="mapper/drugMgt/read/EcsSatmatReadMapper.xml"/>
    </mappers>
</configuration>
