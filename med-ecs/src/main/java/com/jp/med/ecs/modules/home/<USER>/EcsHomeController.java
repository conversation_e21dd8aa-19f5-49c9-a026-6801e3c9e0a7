package com.jp.med.ecs.modules.home.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimDetailReadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "home页面", tags = "home页面")
@RestController
@RequestMapping("home")
public class EcsHomeController {


    @Autowired
    private EcsReimDetailReadService ecsReimDetailReadService;

    /**
     * 列表
     */
    @ApiOperation("查询报销明细")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimDetailDto dto){
        return CommonResult.paging(ecsReimDetailReadService.queryList(dto));
    }

    /**
     * 消息通知
     */
    @ApiOperation("查询报销明细")
    @PostMapping("/msgNote")
    public CommonResult<?> msgNote(@RequestBody EcsReimDetailDto dto){
        return CommonResult.paging(ecsReimDetailReadService.msgNote(dto));
    }

    /**
     * 费用概况
     */
    @ApiOperation("查询报销明细")
    @PostMapping("/expenseOverview")
    public CommonResult<?> expenseOverview(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.expenseOverview(dto));
    }

    /**
     * 报销趋势
     */
    @ApiOperation("报销趋势")
    @PostMapping("/expenseTrends")
    public CommonResult<?> expenseTrends(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.expenseTrends(dto));
    }

    /**
     * 我的申请
     */
    @ApiOperation("我的申请")
    @PostMapping("/myAppr")
    public CommonResult<?> myAppr(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.myAppr(dto));
    }

    /**
     * 我的审批
     */
    @ApiOperation("我的审批")
    @PostMapping("/myAudit")
    public CommonResult<?> myAudit(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.myAudit(dto));
    }
}
