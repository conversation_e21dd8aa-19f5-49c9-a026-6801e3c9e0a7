package com.jp.med.ecs.modules.reimMgt.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 科研经费报销任务表
 * <AUTHOR>
 * @email -
 * @date 2024-11-26 05:30:48
 */
@Data
public class EcsResearchFundingTaskVo {

	/** ID */
	private Integer id;

	/** 申请人编号 */
	private String appyer;

	/** 申请科室 */
	private String appyerDept;

	/** 科研项目ID */
	private Integer projectId;

	/** 项目名称 */
	private String projectName;

	/** 项目负责人 */
	private String projectLeader;

	/** 项目申报级别 */
	private String projectLevel;

	/** 课题类别 */
	private String topicCategory;

	/** 需要报销的金额 */
	private BigDecimal reimAmt;

	/** 计划付款时间 */
	private String schedulePayTime;

	/** 是否报销 0:未报销 1:已报销 */
	private String reimFlag;

	/** 报销ID */
	private Integer reimId;

	/** 项目阶段记录id **/
	private Integer recordId;

	/** 项目经费总预算 **/
	private BigDecimal budget;

	/** 已报销金额 **/
	private BigDecimal alreadyUsedAmt;

	/** 预算金额 **/
	private BigDecimal budgetAmt;

}
