package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDetailDto;
import com.jp.med.common.vo.EcsReimDeprTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDeprTaskDetailReadMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimDeprTaskDetailReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsReimDeprTaskDetailReadServiceImpl extends ServiceImpl<EcsReimDeprTaskDetailReadMapper, EcsReimDeprTaskDetailDto> implements EcsReimDeprTaskDetailReadService {

    @Autowired
    private EcsReimDeprTaskDetailReadMapper ecsReimDeprTaskDetailReadMapper;

    @Override
    public List<EcsReimDeprTaskDetailVo> queryList(EcsReimDeprTaskDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimDeprTaskDetailReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimDeprTaskDetailVo> queryPageList(EcsReimDeprTaskDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsReimDeprTaskDetailReadMapper.queryList(dto);
    }

}
