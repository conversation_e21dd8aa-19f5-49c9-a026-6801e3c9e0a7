package com.jp.med.ecs.modules.reimMgt.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.ReimTypeConst;
import com.jp.med.common.dto.ecs.EcsReimPurcTask;
import com.jp.med.common.dto.ecs.EcsReimPurcTaskDetail;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimPurcTaskWriteMapper;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimPurcTaskWriteService;
import io.seata.common.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 零星采购报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Service
@Transactional(readOnly = false)
public class EcsReimPurcTaskWriteServiceImpl extends ServiceImpl<EcsReimPurcTaskWriteMapper, EcsReimPurcTask> implements EcsReimPurcTaskWriteService {

    @Autowired
    private EcsReimPurcTaskWriteMapper ecsReimPurcTaskWriteMapper;

    @Override
    public void savePurcTask(EcsReimPurcTask dto) {
        List<EcsReimPurcTaskDetail> details = dto.getDetails();
        if (Objects.isNull(details) || CollectionUtils.isEmpty(details)) {
            throw new AppException(EcsConst.PURC_TASK_DETAILS_NOT_EXIST);
        }
        //保存零星采购任务
        dto.setId(null);
        //设置报销任务状态
        // 1 - 零星采购报销
        // 3 - 总务库房物资报销
        //默认不传就是零星采购
        if (StringUtils.isEmpty(dto.getReimTaskType())){
            dto.setReimTaskType(MedConst.TYPE_1);
        }
        ecsReimPurcTaskWriteMapper.insert(dto);
        //保存零星采购任务明细
        details.forEach(item -> {
            item.setId(null);
            item.setTaskId(dto.getId());
            item.setReimType(ReimTypeConst.QTZWCL);
        });
        BatchUtil.batch("insertPurcTaskDetail",details, EcsReimPurcTaskWriteMapper.class);
    }
}
