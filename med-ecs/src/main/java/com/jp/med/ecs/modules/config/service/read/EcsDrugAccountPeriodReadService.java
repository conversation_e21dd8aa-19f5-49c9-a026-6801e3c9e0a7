package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsDrugAccountPeriodDto;
import com.jp.med.common.vo.EcsDrugAccountPeriodVo;

import java.util.List;

/**
 * 药品账期
 * <AUTHOR>
 * @email -
 * @date 2025-02-21 10:36:30
 */
public interface EcsDrugAccountPeriodReadService extends IService<EcsDrugAccountPeriodDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsDrugAccountPeriodVo> queryList(EcsDrugAccountPeriodDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsDrugAccountPeriodVo> queryPageList(EcsDrugAccountPeriodDto dto);
}

