package com.jp.med.ecs.modules.reimMgt.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 合同报销任务详情
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
@Data
public class EcsReimContractTaskDetailVo {

	/** id */
	private Integer id;

	/** 任务id */
	private Integer taskId;

	/** 报销摘要 */
	private String reimAbst;

	/** 报销科室 */
	private String orgId;

	/** 报销类别 */
	private String reimType;

	/** 报销金额 */
	private BigDecimal reimAmt;

	/** 附件 */
	private String att;

	/** 类别对应预算编码 **/
	private String budgetCode;

	/** 科目对应预算是否是汇总预算 **/
	private String bgtSummary;

}
