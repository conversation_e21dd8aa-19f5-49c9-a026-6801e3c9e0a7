package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsTravelAccomStandardCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsTravelAccomStandardCfgVo;

import java.util.List;

/**
 * 差旅住宿费标准
 * <AUTHOR>
 * @email -
 * @date 2024-05-23 11:12:00
 */
public interface EcsTravelAccomStandardCfgReadService extends IService<EcsTravelAccomStandardCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsTravelAccomStandardCfgVo> queryList(EcsTravelAccomStandardCfgDto dto);

    /**
     * 查询列表(不分页)
     * @param dto
     * @return
     */
    List<EcsTravelAccomStandardCfgVo> queryNoPageList(EcsTravelAccomStandardCfgDto dto);
}

