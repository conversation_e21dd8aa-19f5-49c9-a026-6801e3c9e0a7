package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstCfgVo;

import java.util.List;

/**
 * 会计科目辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 15:45:43
 */
public interface EcsActigAsstCfgReadService extends IService<EcsActigAsstCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsActigAsstCfgVo> queryList(EcsActigAsstCfgDto dto);
}

