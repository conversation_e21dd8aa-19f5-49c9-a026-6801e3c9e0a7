package com.jp.med.ecs.modules.reimMgt.rabbitMq;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.enums.ecs.ReimTypeEnum;
import com.jp.med.common.enums.ecs.ShareTypeEnum;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimItemDetail;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimSubsItemDetail;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDetailReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsInvoRcdWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDetailWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimTravelApprWriteMapper;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo;
import com.rabbitmq.client.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@Getter
@Setter
public class EcsReimClincBpmApproveMessageHandle extends AbstractBpmApproveMessageHandle {

    // @Override
    // public String[] getProcessIdentifier() {
    // return new String[] { "oa_leave" };
    // }

    @Autowired
    private EcsReimDetailReadMapper ecsReimDetailReadMapper;

    @Autowired
    private EcsReimDetailWriteMapper ecsReimDetailWriteMapper;

    @Autowired
    private EcsInvoRcdWriteMapper ecsInvoRcdWriteMapper;

    @Autowired
    private EcsReimTravelApprWriteMapper ecsReimTravelApprWriteMapper;

    public String[] processIdentifier = {"REIM_TRADE_TRANSACTIONS","ECS_DUTY_TRIP_APPR_TRIP_REIM","ECS_CLINICAL_TRIP_APPR_TRIP_REIM","ECS_EXPENSE_REIM_CLINC","ECS_EXPENSE_REIM_OTHER","ECS_EXPENSE_REIM_PCM","ECS_LOAN_REIM_OTHER","ECS_EXPENSE_REIM_PURM"};

    @Override
    @RabbitListener(queues = {"REIM_TRADE_TRANSACTIONS","ECS_DUTY_TRIP_APPR_TRIP_REIM","ECS_CLINICAL_TRIP_APPR_TRIP_REIM","ECS_EXPENSE_REIM_CLINC","ECS_EXPENSE_REIM_OTHER","ECS_EXPENSE_REIM_PCM","ECS_LOAN_REIM_OTHER","ECS_EXPENSE_REIM_PURM"})
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        receiveMessage0(msg);

        // try {
        // // 处理消息
        //
        // System.out.println(Arrays.toString(message.getBody()));
        // System.out.println(msg);
        // receiveMessage0(msg);
        //
        // // 手动确认消息
        // channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        //// throw new Exception();
        // } catch (Exception e) {
        // log.info("异常{}:{}",msg.getProcessDefinitionKey(),msg.getBusinessKey());
        // // 处理异常，选择是否重试或拒绝消息
        // channel.basicNack(message.getMessageProperties().getDeliveryTag(), false,
        // true);
        // }
    }

    /**
     * 处理创建的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {

    }

    // @RabbitListener(queues = processIdentifier)
    // public void receiveMessage(BpmProcessInstanceStatus message) {
    //
    // receiveMessage0(message);
    //
    // }

    /**
     * 处理审批通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {

        log.info("------------message-----------",message);
        String businessKey = message.getBusinessKey();
        List<Long> businessIdList = Arrays.stream(businessKey.split(",")).map(Long::parseLong).collect(Collectors.toList());
        businessIdList.forEach(businessId -> {
            //1.查询当前业务key下所有发票记录id
            EcsReimDetailDto ecsReimDetailDto = new EcsReimDetailDto();
            ecsReimDetailDto.setId(businessId);
            List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryListNew(ecsReimDetailDto);
            //更新发票状态
            if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
                updateInvoStatus(ecsReimDetailVos.get(0),MedConst.TYPE_2);
            }
            LambdaUpdateWrapper<EcsReimDetailDto> wrapper = Wrappers.lambdaUpdate();
            wrapper.eq(EcsReimDetailDto::getId,ecsReimDetailVos.get(0).getId() );
            //获取报销类型，如果是零星采购和物资采购，则只更新busstas为审核通过(此时未上传付款文件)
            if (StringUtils.equals(ecsReimDetailVos.get(0).getType(), ReimTypeEnum.PURC_FEE.getCode()) ||
                    StringUtils.equals(ecsReimDetailVos.get(0).getType(), ReimTypeEnum.WZCG_FEE.getCode())) {
                //更新为已审核即可
                wrapper.set(EcsReimDetailDto::getBusstas, EcsConst.REIM_BUSSTAS_PASS);
            } else {
                //更新当前付款状态为已付款
                wrapper.set(EcsReimDetailDto::getBusstas, MedConst.TYPE_1);
            }
            ecsReimDetailWriteMapper.update(null,wrapper);
        });

    }

    /**
     * 处理审批不通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        String businessKey = message.getBusinessKey();
        List<Long> businessIdList = Arrays.stream(businessKey.split(",")).map(Long::parseLong).collect(Collectors.toList());
        businessIdList.forEach(businessId -> {
            //1.查询当前业务key下所有发票记录id
            EcsReimDetailDto ecsReimDetailDto = new EcsReimDetailDto();
            ecsReimDetailDto.setId(businessId);
            List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryListNew(ecsReimDetailDto);

            if (CollectionUtil.isEmpty(ecsReimDetailVos)) {
                return;
            }
            EcsReimDetailVo vo = ecsReimDetailVos.get(0);
            //更新发票状态
            updateInvoStatus(vo,MedConst.TYPE_1);
            //更新报销的业务状态
            LambdaUpdateWrapper<EcsReimDetailDto> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(EcsReimDetailDto::getBusstas, EcsConst.REIM_BUSSTAS_REJECTED)
                    .eq(EcsReimDetailDto::getId,vo.getId());
            ecsReimDetailWriteMapper.update(null,updateWrapper);
            //如果是差旅和培训报销，设置差旅、培训申请为可回退
            //暂时不进行回退，而是直接重新编辑
        /*if (StringUtils.equals(vo.getType(),MedConst.TYPE_1) || StringUtils.equals(vo.getType(),MedConst.TYPE_2)){
            LambdaUpdateWrapper<EcsReimTravelApprDto> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(EcsReimTravelApprDto::getReimFlag,MedConst.TYPE_0)
                    .eq(EcsReimTravelApprDto::getId,vo.getTravelApprId());
            ecsReimTravelApprWriteMapper.update(null,wrapper);
        }*/
        });
    }

    /**
     * 处理审批中的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {

    }

    /**
     * 处理已取消的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {

    }

    /**
     * 报销审核成功/拒绝都需要更新发票状态
     * @param ecsReimDetailVo
     * @param status
     */
    private void updateInvoStatus(EcsReimDetailVo ecsReimDetailVo,String status) {
        if (!Objects.isNull(ecsReimDetailVo)) {
            EcsReimDetailDto detailDto = new EcsReimDetailDto();
            detailDto.setId(ecsReimDetailVo.getId());
            List<EcsReimItemDetail> itemDetails = ecsReimDetailReadMapper.queryItemDetail(detailDto);
            List<EcsReimSubsItemDetail> subsItemDetails = ecsReimDetailReadMapper.querySubsItemDetail(detailDto);
            List<Long> ids = new ArrayList<>();
            //报销自带发票
            if (StringUtils.isNotEmpty(ecsReimDetailVo.getInvoId())){
                ids.addAll(getInvoIds(ecsReimDetailVo.getInvoId()));
            }
            // 项目
            if (CollectionUtil.isNotEmpty(itemDetails)) {
                itemDetails.forEach(i -> {
                    if (StringUtils.isNotEmpty(i.getInvoId())) {
                        ids.addAll(getInvoIds(i.getInvoId()));
                    }
                });
            }

            // 补助项目
            if (CollectionUtil.isNotEmpty(subsItemDetails)) {
                subsItemDetails.forEach(i -> {
                    if (StringUtils.isNotEmpty(i.getInvoId())) {
                        ids.addAll(getInvoIds(i.getInvoId()));
                    }
                });
            }

            if (CollectionUtil.isNotEmpty(ids)) {
                // 更新发票记录表状态，更改为已报销(2为已报销)
                ecsInvoRcdWriteMapper.updateStateByIds(ids, status,StringUtils.equals(status,MedConst.TYPE_1)?"":getReimInvoUsedBy(ecsReimDetailVo));
            }
        }
    }

    private String getReimInvoUsedBy(EcsReimDetailVo reim) {
        if (StringUtils.equals(reim.getType(),MedConst.TYPE_4)) {
            return ShareTypeEnum.getByType(reim.getShareType()).getName();
        }
        return ReimTypeEnum.getByCode(reim.getType()).getMessage();
    }

    private List<Long> getInvoIds(String invoId){
        List<Long> ids = new ArrayList<>();
        String[] split = invoId.split(",");
        for (int i = 0; i < split.length; i++) {
            ids.add(Long.parseLong(split[i]));
        }
        return ids;
    }

    // /**
    // * 处理已退回的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleReturned(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理委派中的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleDelegated(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理审批通过中的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleApproving(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理待审批的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleWaiting(BpmProcessInstanceStatus message) {
    //
    // }

}
