package com.jp.med.ecs.modules.common.service.write;

import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.payload.AuditPayload;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 14:28
 * @description:
 */
public interface EcsAuditWriteService {

    /**
     * 审核完成
     * @param dto
     */
    void complete(AuditDetail dto);
}
