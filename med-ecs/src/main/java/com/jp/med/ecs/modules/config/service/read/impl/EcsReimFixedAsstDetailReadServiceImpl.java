package com.jp.med.ecs.modules.config.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDetailDto;
import com.jp.med.common.vo.EcsReimFixedAsstDetailVo;
import com.jp.med.ecs.modules.config.mapper.read.EcsReimFixedAsstDetailReadMapper;
import com.jp.med.ecs.modules.config.service.read.EcsReimFixedAsstDetailReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsReimFixedAsstDetailReadServiceImpl extends ServiceImpl<EcsReimFixedAsstDetailReadMapper, EcsReimFixedAsstDetailDto> implements EcsReimFixedAsstDetailReadService {

    @Autowired
    private EcsReimFixedAsstDetailReadMapper ecsReimFixedAsstDetailReadMapper;

    @Override
    public List<EcsReimFixedAsstDetailVo> queryList(EcsReimFixedAsstDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsReimFixedAsstDetailReadMapper.queryList(dto);
    }

}
