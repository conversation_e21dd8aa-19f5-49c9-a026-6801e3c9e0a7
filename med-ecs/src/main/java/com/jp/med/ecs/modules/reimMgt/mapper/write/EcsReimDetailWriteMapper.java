package com.jp.med.ecs.modules.reimMgt.mapper.write;

import com.jp.med.ecs.modules.config.entity.EcsReimShareEntity;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ecs.modules.reimMgt.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报销明细
 * <AUTHOR>
 * @email -
 * @date 2023-12-04 22:01:14
 */
@Mapper
public interface EcsReimDetailWriteMapper extends BaseMapper<EcsReimDetailDto> {

    /**
     * 写入项目
     * @param item
     */
    void insertItemDetail(EcsReimItemDetail item);

    /**
     * 更新项目
     * @param item
     */
    void updateItemDetail(EcsReimItemDetail item);

    /**
     * 更新合同项目信息
     * @param item
     */
    void updateContractItems(EcsReimItemDetail item);

    /**
     * 写入补助项目
     * @param item
     */
    void insertSubsItemDetail(EcsReimSubsItemDetail item);

    /**
     * 更新补助项目
     * @param item
     */
    void updateSubsItemDetail(EcsReimSubsItemDetail item);

    /**
     * 更新随行人员
     * @param item
     */
    void updatePsnDetail(EcsReimPsnDetail item);

    /**
     * 写入人员
     * @param item
     */
    void insertPsnDetail(EcsReimPsnDetail item);

    /**
     * 写入往来单位
     * @param item
     */
    void insertRelCoDetail(EcsReimRelCoDetail item);

    /**
     * 写入分摊费用明细
     * @param item
     */
    void insertShareDetail(EcsReimShareEntity item);

    /**
     * 写入辅项
     * @param reimAsstDetails
     */
    void addReimAsst(List<EcsReimAsstDetail> reimAsstDetails);

    /**
     * 删除差旅申请随行人员
     * @param reimId
     * @param type
     */
    void deletePsnWithReimDetailId(@Param("reimId")Integer reimId, @Param("type")String type);

    /**
     * 删除报销项目信息
     * @param reimId
     */
    void deleteReimItemWithReimId(@Param("reimId")Integer reimId);

    /**
     * 删除报销补助项目信息
     * @param reimId
     */
    void deleteReimSubItemWithReimId(@Param("reimId") Integer reimId);

    void updateHrmResearcherFundingApplyStatus(@Param("ids")List<Long> ids,@Param("status") String status);
}
