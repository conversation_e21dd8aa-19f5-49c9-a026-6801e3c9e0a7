package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.dto.ecs.EcsResearchFundingTaskDetailDto;
import com.jp.med.common.dto.ecs.EcsResearchFundingTaskDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsResearchFundingTaskWriteMapper;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsResearchFundingTaskWriteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 科研经费报销任务表
 * <AUTHOR>
 * @email -
 * @date 2024-11-26 05:30:48
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = false)
public class EcsResearchFundingTaskWriteServiceImpl extends ServiceImpl<EcsResearchFundingTaskWriteMapper, EcsResearchFundingTaskDto> implements EcsResearchFundingTaskWriteService {
    @Override
    public Integer saveFundingTask(EcsResearchFundingTaskDto dto) {
        if (super.baseMapper.insert(dto) <= 0) {
            throw new AppException("生成报销任务失败");
        }
        List<EcsResearchFundingTaskDetailDto> details = dto.getDetails();
        details.forEach(item -> item.setTaskId(dto.getId()));
        BatchUtil.batch("insertResearchFundingTaskDetail", details, EcsResearchFundingTaskWriteMapper.class);
        return dto.getId();
    }
}
