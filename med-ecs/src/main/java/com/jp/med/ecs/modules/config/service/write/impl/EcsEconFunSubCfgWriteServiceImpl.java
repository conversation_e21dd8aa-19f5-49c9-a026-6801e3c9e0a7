package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.ecs.modules.config.dto.EcsEconFunSubCfgDto;
import com.jp.med.common.dto.ecs.EcsReimItemToBudgCfgDto;
import com.jp.med.ecs.modules.config.mapper.read.EcsEconFunSubCfgReadMapper;
import com.jp.med.ecs.modules.config.mapper.write.EcsEconFunSubCfgWriteMapper;
import com.jp.med.ecs.modules.config.service.read.EcsReimItemToBudgCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsEconFunSubCfgWriteService;
import com.jp.med.ecs.modules.config.utils.YySyncUtils;
import com.jp.med.common.vo.ecs.EcsReimItemToBudgCfgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 经济和功能科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Service
@Transactional(readOnly = false)
public class EcsEconFunSubCfgWriteServiceImpl extends ServiceImpl<EcsEconFunSubCfgWriteMapper, EcsEconFunSubCfgDto> implements EcsEconFunSubCfgWriteService {

    @Autowired
    private EcsEconFunSubCfgWriteMapper ecsEconFunSubCfgWriteMapper;
    
    @Autowired
    private EcsEconFunSubCfgReadMapper ecsEconFunSubCfgReadMapper;

    @Autowired
    private EcsReimItemToBudgCfgReadService ecsReimItemToBudgCfgReadService;

    @Value("${urls.mid.yy.econ-fun-sync}")
    private String econFunSyncUrl;

    private static final int BATCH_SIZE = 500;  ///每次批处理大小

    @Override
    public void sync(EcsEconFunSubCfgDto dto) {

        int start = 1;
        int end = start + BATCH_SIZE;
//        int end = 2;
        Map<String,Object> params = new HashMap<>();
        params.put("year",dto.getYear());


        /*params.put("start",start);
        params.put("end",end);
        List<EcsEconFunSubCfgDto> efcDtos = doSync(econFunSyncUrl, params, EcsEconFunSubCfgDto.class);
        //处理parentCode
        efcDtos.forEach(item -> {
            if (item.getSubCode().length()> 3) {
                item.setParentCode(item.getSubCode().substring(0,item.getSubCode().length()-2));
            }
        });
        ecsEconFunSubCfgWriteMapper.saveEcsEconSubCfg(efcDtos);*/

        //删除某年度经济功能科目
        LambdaQueryWrapper<EcsEconFunSubCfgDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EcsEconFunSubCfgDto::getYear,dto.getYear());
        ecsEconFunSubCfgWriteMapper.delete(wrapper);

        //查询某年度经济科目映射预算项目数据
        EcsReimItemToBudgCfgDto budget = new EcsReimItemToBudgCfgDto();
        budget.setYear(dto.getYear());
        budget.setPageNum(1);
        budget.setPageSize(999999);
        budget.setType(MedConst.TYPE_2);
        List<EcsReimItemToBudgCfgVo> budgCfgVos = ecsReimItemToBudgCfgReadService.queryList(budget);
        //转为Map  key:reimItemCode  value:budgetCode
        Map<String,EcsReimItemToBudgCfgVo> budgetMap = budgCfgVos
                .stream()
                .collect(Collectors.toMap(EcsReimItemToBudgCfgVo::getReimItemCode,vo -> vo));
        List<EcsEconFunSubCfgDto> efcDtos;
        do{
            params.put("start",start);
            params.put("end",end);

            efcDtos = YySyncUtils.doSync(econFunSyncUrl, params, EcsEconFunSubCfgDto.class);
            if (!efcDtos.isEmpty()) {
                String crter = dto.getSysUser().getHrmUser().getEmpCode();
                //处理parentCode
                efcDtos.forEach(item -> {
                    item.setCrter(crter);
                    if (item.getSubCode().length()> 3) {
                        item.setParentSubCode(item.getSubCode().substring(0,item.getSubCode().length()-2));
                    }
                    EcsReimItemToBudgCfgVo ecsReimItemToBudgCfgVo = budgetMap.get(item.getSubCode());
                    item.setBudgetCode(Objects.isNull(ecsReimItemToBudgCfgVo)?null: ecsReimItemToBudgCfgVo.getBudgetCode());
                    item.setBgtSummary(Objects.isNull(ecsReimItemToBudgCfgVo)?null: ecsReimItemToBudgCfgVo.getBgtSummary());
                });

                //插入经济功能科目
                ecsEconFunSubCfgWriteMapper.saveEcsEconSubCfg(efcDtos);

                start +=BATCH_SIZE;
                end +=BATCH_SIZE;
            }
        }while(!efcDtos.isEmpty());
    }



}
