package com.jp.med.ecs.modules.config.vo;

import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 经济和功能科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Data
public class EcsEconFunSubCfgVo implements BaseTree<String, EcsEconFunSubCfgVo> {

	/** id */
	private Integer id;

	/** 科目代码 */
	private String subCode;

	/** 父科目代码 */
	private String parentSubCode;

	/** 科目名称 */
	private String subName;

	/** 科目类型 */
	private String subType;

	/** 拼音助记码 */
	private String pinyin;

	/** 备注 */
	private String remarks;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改时间 */
	private String modiTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 启用标志 */
	private String activeFlag;

	/** 预算编码 */
	private String budgetCode;

	/** 科目对应预算是否是汇总预算 **/
	private String bgtSummary;

	/** 子集 */
	private List<EcsEconFunSubCfgVo> children;

	/**  */
	private String value;

	/**  */
	private String label;

	@Override
 	public String getCode() {
		return this.subCode;
	}

	@Override
	public void setCode(String code) {
		this.subCode = code;
	}

	@Override
	public String getPid() {
		return this.parentSubCode;
	}

	@Override
	public void setPid(String pid) {
		this.parentSubCode = pid;
	}

	@Override
	public void addChild(EcsEconFunSubCfgVo node) {
		if (Objects.isNull(this.children)){
			this.children = new ArrayList<>();
		}
		this.children.add(node);
	}


}
