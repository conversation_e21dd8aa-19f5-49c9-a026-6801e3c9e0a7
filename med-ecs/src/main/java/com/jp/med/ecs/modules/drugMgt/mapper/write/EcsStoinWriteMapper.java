package com.jp.med.ecs.modules.drugMgt.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ecs.modules.drugMgt.dto.EcsStoinDetailDto;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 入库单
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:44
 */
@Mapper
public interface EcsStoinWriteMapper extends BaseMapper<EcsStoinDto> {

    void saveDrugStoin(List<EcsStoinDto> stoins);

    void saveDrugStoinDetail(List<EcsStoinDetailDto> stoinDetails);


}
