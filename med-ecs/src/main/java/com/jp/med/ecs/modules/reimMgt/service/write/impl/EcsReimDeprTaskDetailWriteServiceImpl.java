package com.jp.med.ecs.modules.reimMgt.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDetailDto;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDeprTaskDetailWriteMapper;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimDeprTaskDetailWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 折旧任务明细
 * <AUTHOR>
 * @email -
 * @date 2025-02-19 15:12:46
 */
@Service
@Transactional(readOnly = false)
public class EcsReimDeprTaskDetailWriteServiceImpl extends ServiceImpl<EcsReimDeprTaskDetailWriteMapper, EcsReimDeprTaskDetailDto> implements EcsReimDeprTaskDetailWriteService {
}
