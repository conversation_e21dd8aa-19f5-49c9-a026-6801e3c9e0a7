package com.jp.med.ecs.modules.reimMgt.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsResearchFundingTaskDto;

/**
 * 科研经费报销任务表
 * <AUTHOR>
 * @email -
 * @date 2024-11-26 05:30:48
 */
public interface EcsResearchFundingTaskWriteService extends IService<EcsResearchFundingTaskDto> {
    /**
     * 新增科研经费报销任务
     * @param dto 参数
     * @return 任务ID
     */
    Integer saveFundingTask(EcsResearchFundingTaskDto dto);
}

