package com.jp.med.ecs.modules.config.service.read.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsCorrsInsCfgReadMapper;
import com.jp.med.ecs.modules.config.dto.EcsCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsCorrsInsCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsCorrsInsCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsCorrsInsCfgReadServiceImpl extends ServiceImpl<EcsCorrsInsCfgReadMapper, EcsCorrsInsCfgDto> implements EcsCorrsInsCfgReadService {

    @Autowired
    private EcsCorrsInsCfgReadMapper ecsCorrsInsCfgReadMapper;

    @Override
    public List<EcsCorrsInsCfgVo> queryList(EcsCorrsInsCfgDto dto) {
        return ecsCorrsInsCfgReadMapper.queryList(dto);
    }

}
