package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDetailDto;
import com.jp.med.common.vo.EcsReimDeprTaskDetailVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 折旧任务明细
 * <AUTHOR>
 * @email -
 * @date 2025-02-19 15:12:46
 */
@Mapper
public interface EcsReimDeprTaskDetailReadMapper extends BaseMapper<EcsReimDeprTaskDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimDeprTaskDetailVo> queryList(EcsReimDeprTaskDetailDto dto);
}
