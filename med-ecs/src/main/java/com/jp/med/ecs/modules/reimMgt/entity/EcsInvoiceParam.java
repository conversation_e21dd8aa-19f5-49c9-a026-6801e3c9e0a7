package com.jp.med.ecs.modules.reimMgt.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/2 23:34
 * @description: 发票核验参数
 */
@Data
public class EcsInvoiceParam {

    /** 发票代码 */
    private String invoiceCode;

    /** 发票号码 */
    private String invoiceNo;

    /** 发票日期 */
    private String invoiceDate;

    /** 验证码 */
    private String verifyCode;

    /** 发票类型代码为 01，03，15，20，31，32 时必填：为 01，03，20 时填写发票不含税金额；为 15 时填写发票车价合计；为 31，32 时填写含税金额。 */
    private BigDecimal invoiceSum;

    /** 是否区块链发票 **/
    private Integer invoiceKind;
}
