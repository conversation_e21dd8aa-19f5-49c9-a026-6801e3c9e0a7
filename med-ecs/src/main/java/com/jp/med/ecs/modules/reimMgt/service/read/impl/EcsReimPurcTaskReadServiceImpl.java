package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.dto.ecs.EcsReimPurcTask;
import com.jp.med.common.vo.EcsReimPurcTaskDetailVo;
import com.jp.med.common.vo.EcsReimPurcTaskVo;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimPurcTaskReadMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimPurcTaskReadService;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class EcsReimPurcTaskReadServiceImpl extends ServiceImpl<EcsReimPurcTaskReadMapper, EcsReimPurcTask>
        implements EcsReimPurcTaskReadService {

    @Autowired
    private EcsReimPurcTaskReadMapper ecsReimPurcTaskReadMapper;

    @Override
    public List<EcsReimPurcTaskVo> queryList(EcsReimPurcTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<EcsReimPurcTaskVo> resultVos = ecsReimPurcTaskReadMapper.queryList(dto);
        if (Collections.isEmpty(resultVos)) {
            return resultVos;
        }
        List<EcsReimPurcTaskDetailVo> purcTaskDetails = ecsReimPurcTaskReadMapper.queryPurcTaskDetails(
                resultVos.stream().map(EcsReimPurcTaskVo::getId).collect(Collectors.toList()), dto);
        resultVos.forEach(ecsReimPurcTaskVo -> {
            ecsReimPurcTaskVo.setChildren(purcTaskDetails.stream().filter(
                    ecsReimPurcTaskDetailVo -> ecsReimPurcTaskDetailVo.getTaskId().equals(ecsReimPurcTaskVo.getId())).collect(Collectors.toList())
            );
        });
        return resultVos;
    }

    @Override
    public List<EcsReimPurcTaskVo> queryPageList(EcsReimPurcTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsReimPurcTaskReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimPurcTaskDetailVo> queryPurcTaskDetail(EcsReimPurcTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimPurcTaskReadMapper.queryPurcTaskDetail(dto);
    }

    @Override
    public List<EcsReimPurcTaskVo> queryPurcTaskByReimId(EcsReimPurcTask dto) {
        return ecsReimPurcTaskReadMapper.queryPurcTaskByReimId(dto);
    }
}
