package com.jp.med.ecs.modules.reimMgt.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EcsSalaryIndiReduceExcel {

    /** 部门名称 **/
    @ExcelProperty("部门名称")
    private String orgName;

    /** 部门人数 **/
    @ExcelProperty("人数")
    private Integer peoNum;

    /** 月份 **/
    @ExcelProperty("月份")
    private String month;

    /** 养老保险 **/
    @ExcelProperty("养老保险")
    private BigDecimal pensionInsurance;

    /** 医疗保险 **/
    @ExcelProperty("医疗保险")
    private BigDecimal medicalInsurance;

    /** 失业保险 **/
    @ExcelProperty("失业保险")
    private BigDecimal unemploymentInsurance;

    /** 住房基金 **/
    @ExcelProperty("住房基金")
    private BigDecimal housingFund;

    /** 职业年金 **/
    @ExcelProperty("职业年金")
    private BigDecimal occupationalAnnuity;

    /** 工会会费 **/
    @ExcelProperty("工会会费")
    private BigDecimal laborUnion;

    /**
     * 个人所得税
     **/
    @ExcelProperty("个人所得税")
    private BigDecimal personTax;
}
