package com.jp.med.ecs.modules.drugMgt.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsDrugReimDetaiReadService;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsDrugReimDetaiWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 药品报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:45
 */
@Api(value = "药品报销详情", tags = "药品报销详情")
@RestController
@RequestMapping("ecsDrugReimDetai")
public class EcsDrugReimDetaiController {

    @Autowired
    private EcsDrugReimDetaiReadService ecsDrugReimDetaiReadService;

    @Autowired
    private EcsDrugReimDetaiWriteService ecsDrugReimDetaiWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询药品报销详情")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsDrugReimDetaiDto dto){
        return CommonResult.paging(ecsDrugReimDetaiReadService.queryList(dto));
    }

    /**
     * 列表-不分页
     */
    @ApiOperation("查询药品报销详情")
    @PostMapping("/listNoPage")
    public CommonResult<?> listNoPage(@RequestBody EcsDrugReimDetaiDto dto){
        return CommonResult.success(ecsDrugReimDetaiReadService.queryListNoPage(dto));
    }


    @ApiOperation("查询药品报销详情")
    @PostMapping("/auditData")
    public CommonResult<?> auditData(@RequestBody EcsDrugReimDetaiDto dto){
        return CommonResult.paging(ecsDrugReimDetaiReadService.queryAuditData(dto));
    }

    @ApiOperation("查询app审核详情数据")
    @PostMapping("/appAuditDetail")
    public CommonResult<?> queryAppAuditDetail(@RequestBody EcsDrugReimDetaiDto dto){
        return CommonResult.success(ecsDrugReimDetaiReadService.queryAppAuditDetail(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增药品报销详情")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsDrugReimDetaiDto dto){
        ecsDrugReimDetaiWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增药品报销详情")
    @PostMapping("/batchSave")
    public CommonResult<?> batchSave(EcsDrugReimDetaiDto dto){
        ecsDrugReimDetaiWriteService.batchSave(dto);
        return CommonResult.success();
    }

    /**
     * 保存New(使用新审批流程)
     */
    @ApiOperation("新增药品报销详情")
    @PostMapping("/batchSaveNew")
    public CommonResult<?> batchSaveNew(EcsDrugReimDetaiDto dto){
        ecsDrugReimDetaiWriteService.batchSaveNew(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改药品报销详情")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsDrugReimDetaiDto dto){
        ecsDrugReimDetaiWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除药品报销详情")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsDrugReimDetaiDto dto){
        ecsDrugReimDetaiWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 列表
     */
    @ApiOperation("查询药品报销详情")
    @PostMapping("/updEcsDrugPayRcpt")
    public CommonResult<?> updEcsDrugPayRcpt(EcsDrugReimDetaiDto dto){
        ecsDrugReimDetaiWriteService.updEcsDrugPayRcpt(dto);
        return CommonResult.success();
    }

    /**
     * 打印药品付款单
     */
    @ApiOperation("查询药品报销详情")
    @PostMapping("/printRcptDoc")
    public CommonResult<?> printRcptDoc(@RequestBody EcsDrugReimDetaiDto dto){
        return CommonResult.success(ecsDrugReimDetaiWriteService.printRcptDoc(dto));
    }

    /**
     * 查询父批次号下所有报销
     */
    @ApiOperation("查询父批次号下所有报销")
    @PostMapping("/queryEcsDrugReims")
    public CommonResult<?> queryEcsDrugReims(@RequestBody EcsDrugReimDetaiDto dto){
        return CommonResult.success(ecsDrugReimDetaiWriteService.queryEcsDrugReims(dto));
    }

    /**
     * 更新报销期号
     */
    @ApiOperation("更新报销期号")
    @PostMapping("/updIssue")
    public CommonResult<?> updIssue(@RequestBody EcsDrugReimDetaiDto dto){
        ecsDrugReimDetaiWriteService.updIssue(dto);
        return CommonResult.success();
    }

    /**
     * 删除未选择的药品报销记录
     */
    @ApiOperation("删除未选择的药品报销记录")
    @PostMapping("/delDrugReimNoChoose")
    public CommonResult<?> delDrugReimNoChoose(@RequestBody EcsDrugReimDetaiDto dto){
        ecsDrugReimDetaiWriteService.delDrugReimNoChoose(dto);
        return CommonResult.success();
    }
}
