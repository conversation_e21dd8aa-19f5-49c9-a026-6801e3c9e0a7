package com.jp.med.ecs.modules.drugMgt.mapper.read;

import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 药品报销付款详情
 * <AUTHOR>
 * @email -
 * @date 2024-11-25 12:03:17
 */
@Mapper
public interface EcsDrugPayDetailReadMapper extends BaseMapper<EcsDrugPayDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsDrugPayDetailVo> queryList(EcsDrugPayDetailDto dto);
}
