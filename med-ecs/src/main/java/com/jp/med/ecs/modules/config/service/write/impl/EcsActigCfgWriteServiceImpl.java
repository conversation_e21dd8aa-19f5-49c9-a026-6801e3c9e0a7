package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ecs.modules.config.dto.EcsActigCfgDto;
import com.jp.med.ecs.modules.config.mapper.write.EcsActigCfgWriteMapper;
import com.jp.med.ecs.modules.config.service.write.EcsActigCfgWriteService;
import com.jp.med.ecs.modules.config.utils.YySyncUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会计科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Service
@Transactional(readOnly = false)
public class EcsActigCfgWriteServiceImpl extends ServiceImpl<EcsActigCfgWriteMapper, EcsActigCfgDto> implements EcsActigCfgWriteService {

    @Autowired
    private EcsActigCfgWriteMapper ecsActigCfgWriteMapper;

    @Value("${urls.mid.yy.actig-sync}")
    private String actigSyncUrl;

    private static final int BATCH_SIZE = 500;      //批处理大小

    @Override
    public void sync(EcsActigCfgDto dto) {

        int start = 1;
        int end = start + BATCH_SIZE;
//        int end = 2;
        Map<String,Object> params = new HashMap<>();
        params.put("year",dto.getYear());

        /*params.put("start",start);
        params.put("end",end);
        List<EcsEconFunSubCfgDto> efcDtos = doSync(econFunSyncUrl, params, EcsEconFunSubCfgDto.class);
        //处理parentCode
        efcDtos.forEach(item -> {
            if (item.getSubCode().length()> 3) {
                item.setParentCode(item.getSubCode().substring(0,item.getSubCode().length()-2));
            }
        });
        ecsEconFunSubCfgWriteMapper.saveEcsEconSubCfg(efcDtos);*/

        //删除某年度会计科目
        LambdaQueryWrapper<EcsActigCfgDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EcsActigCfgDto::getYear,dto.getYear());
        ecsActigCfgWriteMapper.delete(wrapper);

        List<EcsActigCfgDto> efcDtos;
        do{
            params.put("start",start);
            params.put("end",end);

            efcDtos = YySyncUtils.doSync(actigSyncUrl, params, EcsActigCfgDto.class);
            if (!efcDtos.isEmpty()) {
                String crter = dto.getSysUser().getHrmUser().getEmpCode();
                //处理parentCode
                efcDtos.forEach(item -> {
                    item.setCrter(crter);
                    if (item.getSubCode().length()> 4) {
                        item.setParentCode(item.getSubCode().substring(0,item.getSubCode().length()-2));
                    }
                });

                //插入会计科目
                ecsActigCfgWriteMapper.saveActigCfg(efcDtos);

                start +=BATCH_SIZE;
                end +=BATCH_SIZE;
            }
        }while(!efcDtos.isEmpty());

    }
}
