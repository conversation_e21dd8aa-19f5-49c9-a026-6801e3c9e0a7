package com.jp.med.ecs.modules.reimMgt.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
@Data
@TableName("ecs_invo_rcd" )
public class EcsInvoRcdDto extends CommonQueryDto {

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 发票代码 */
    @TableField("invo_code")
    private String invoCode;

    /** 发票号码 */
    @TableField("invo_num")
    private String invoNum;

    /** 开票日期 */
    @TableField("invo_date")
    private String invoDate;

    /** 校验码 */
    @TableField("chk_code")
    private String chkCode;

    /** 发票附件 */
    @TableField("att")
    private String att;

    /** 发票附件名称 */
    @TableField("att_name")
    private String attName;

    /** 校验状态(1:有效,0:无效) */
    @TableField("chk_state")
    private String chkState;

    /** 校验时间 */
    @TableField("chk_time")
    private String chkTime;

    /** OCR识别错误信息 */
    @TableField("idtf_err_msg")
    private String idtfErrMsg;

    /** 创建日期 */
    @TableField("create_time")
    private String createTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 手动修正标志 **/
    @TableField("manual_amend")
    private String manualAmend;

    /** 修正人员 **/
    @TableField("amend_user")
    private String amendUser;

    /** 审核状态 **/
    @TableField("status")
    private String status;

    /** 校正申请人 **/
    @TableField("amend_applyer")
    private String amendApplyer;

    /** 备注 **/
    @TableField("abs")
    private String abs;

    /** 购方名称 **/
    @TableField("purchaser_name")
    private String purchaserName;

    /** 纳税人识别号 **/
    @TableField("purchaser_taxpayer_number")
    private String purchaserTaxpayerNumber;

    /** 发票金额(不含税) **/
    @TableField("invoice_money")
    private String invoiceMoney;

    /** 价税合计 **/
    @TableField("all_valorem_tax")
    private String allValoremTax;

    /** 状态，1：可报销，2：已报销，3：重复发票，4：发票报销审核中，写入时不写，后续更改状态 */
    //@TableField(exist = false)
    @TableField("state")
    private String state;

    /** 核验的数据 */
    @TableField("chk_data")
    private String chkData;

    /** 发票创建人 **/
    @TableField("create_user")
    private String createUser;

    /** 发票使用人 **/
    @TableField("invo_user")
    private String invoUser;

    /** 发票来源   1:hrp 2:供应商 3：卫材  不同来源表不同 **/
    @TableField("invo_from")
    private String invoFrom;

    /** 发票类型 **/
    @TableField("invo_type")
    private String invoType;

    /** 子发票ids **/
    @TableField("sub_invo_ids")
    private String subInvoIds;

    /** 是否子发票 **/
    @TableField("is_sub")
    private String isSub;

    /** 发票用于的业务 **/
    @TableField("invo_used_by")
    private String invoUsedBy;

    /** 上传日期 **/
    @TableField(exist = false)
    private String updDate;


    /** 附件 */
    @TableField(exist = false)
    private MultipartFile attFile;

    /** 是否是发票 */
    @TableField(exist = false)
    private String isInvo;

    /** 文件唯一标识 */
//    @TableField(exist = false)
    private String fileIdentifier;

    /** 记录唯一标识 **/
    @TableField(exist = false)
    private String rcdIdentifier;

    /** 不含税金额 */
    @TableField(exist = false)
    private BigDecimal invoiceAmountPreTax;

    /** 含税金额 */
    @TableField(exist = false)
    private BigDecimal totalAmount;

    /** 税额 */
    @TableField(exist = false)
    private BigDecimal invoiceTax;

    /** 发票ids **/
    @TableField(exist = false)
    private List<Integer> ids;

    /**
     * 子发票信息
     */
    @TableField(exist = false)
    private List<EcsInvoRcdDto> subInvos;

    /** 发票二维码识别信息 示例：01,32,,24512000000144186957,30.00,20240717,,2935 **/
    @TableField(exist = false)
    private String qrCodeData;

    /** 是否为区块链发票 **/
    @TableField(exist = false)
    private String invoiceKind;


}
