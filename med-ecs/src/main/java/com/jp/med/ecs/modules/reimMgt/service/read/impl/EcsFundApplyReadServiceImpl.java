package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.feign.AuditFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsFundApplyReadMapper;
import com.jp.med.ecs.modules.reimMgt.dto.EcsFundApplyDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsFundApplyVo;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsFundApplyReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsFundApplyReadServiceImpl extends ServiceImpl<EcsFundApplyReadMapper, EcsFundApplyDto> implements EcsFundApplyReadService {

    @Autowired
    private EcsFundApplyReadMapper ecsFundApplyReadMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Override
    public List<EcsFundApplyVo> queryList(EcsFundApplyDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
        return ecsFundApplyReadMapper.queryList(dto);
    }

    @Override
    public EcsFundApplyVo queryAppAuditDetail(EcsFundApplyDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<AuditDetail> auditDetails = auditFeignService.getAuditDetails(new AuditDetail(dto.getAuditBchno(), dto.getSysUser().getHrmUser().getEmpCode(), OSSConst.BUCKET_ECS));
        List<EcsFundApplyVo> ecsFundApplyVos = ecsFundApplyReadMapper.queryList(dto);
        EcsFundApplyVo ecsFundApplyVo = new EcsFundApplyVo();
        if (CollectionUtil.isNotEmpty(auditDetails)) {
            ecsFundApplyVo = ecsFundApplyVos.get(0);
            auditDetails.sort(Comparator.comparing(AuditDetail::getChkSeq));
            ecsFundApplyVo.setAuditDetails(auditDetails);
        }
        return ecsFundApplyVo;
    }
}
