package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.ecs.modules.config.dto.EcsActigAsstDeptCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstDeptCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 会计科目科室辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2024-02-01 16:36:57
 */
@Mapper
public interface EcsActigAsstDeptCfgReadMapper extends BaseMapper<EcsActigAsstDeptCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsActigAsstDeptCfgVo> queryList(EcsActigAsstDeptCfgDto dto);
}
