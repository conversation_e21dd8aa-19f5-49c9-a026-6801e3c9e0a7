package com.jp.med.ecs.modules.reimMgt.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工资汇总表-人员类型
 */
@Data
public class EcsSalarySummary3Excel {

    /** 姓名 **/
    @ExcelProperty(index = 0)
    private String empName;

    /** 工资号（工号） **/
    @ExcelProperty(index = 1)
    private String salaryNum;

    /** 身份证号 **/
    private String icdCard;

    /** 性质 **/
    @ExcelProperty(index = 2)
    private String empType;

    /** 岗位工资 **/
    @ExcelProperty(index = 3)
    private BigDecimal postSalary;

    /** 护士10% **/
    @ExcelProperty(index = 4)
    private BigDecimal nurseSalary;

    /** 薪级工资 **/
    @ExcelProperty(index = 5)
    private BigDecimal salGradeSalary;

    /** 基础绩效 **/
    @ExcelProperty(index = 6)
    private BigDecimal basicPerf;

    /** 护龄补贴 **/
    @ExcelProperty(index = 7)
    private BigDecimal ageSalary;

    /** 驾驶员津贴 **/
    @ExcelProperty(index = 8)
    private BigDecimal driverSalary;

    /** 通讯费补贴 **/
    @ExcelProperty(index = 9)
    private BigDecimal communicationFeeAllowance;
    /** 生活补贴 **/
    @ExcelProperty(index = 10)
    private BigDecimal lifeSalary;

    /** 地区附件津贴 **/
    @ExcelProperty(index = 11)
    private BigDecimal areaSalary;

    /** 人力临时增加 **/
    @ExcelProperty(index = 12)
    private BigDecimal temporaryAddSalary;

    /** 财务临时增加 **/
    @ExcelProperty(index = 13)
    private BigDecimal temporaryAddSalary2;

    /** 应发合计 **/
//    @ExcelProperty(index = 14)
    private BigDecimal totalPayable;

    /** 养老保险 **/
    @ExcelProperty(index = 15)
    private BigDecimal pensionInsurance;

    /** 医疗保险 **/
    @ExcelProperty(index = 16)
    private BigDecimal medicalInsurance;

    /** 失业保险 **/
    @ExcelProperty(index = 17)
    private BigDecimal unemploymentInsurance;

    /** 住房基金 **/
    @ExcelProperty(index = 18)
    private BigDecimal housingFund;

    /** 职业年金 **/
    @ExcelProperty(index = 19)
    private BigDecimal occupationalAnnuity;

    /** 工会会费 **/
    private BigDecimal laborUnion;

    /** 个人所得税 **/
    @ExcelProperty(index = 20)
    private BigDecimal personTax;

    /** 房租费 **/
    @ExcelProperty(index = 21)
    private BigDecimal rent;

    /** 水费 **/
    @ExcelProperty(index = 22)
    private BigDecimal waterCharge;

    /** 人力临时扣款 **/
    @ExcelProperty(index = 23)
    private BigDecimal temporaryReduceSalary;

    /** 财务临时扣款 **/
    @ExcelProperty(index = 24)
    private BigDecimal temporaryReduceSalary2;

    /** 扣款合计 **/
    @ExcelProperty(index = 25)
    private BigDecimal reduceSalaryTotal;

    /** 实发合计 **/
    @ExcelProperty(index = 26)
    private BigDecimal totalPaid;
}
