package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimContractTask;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 合同报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
@Mapper
public interface EcsReimContractTaskReadMapper extends BaseMapper<EcsReimContractTask> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimContractTaskVo> queryList(EcsReimContractTask dto);

    /**
     * 查询合同明细
     * @param dto
     * @return
     */
    List<EcsReimContractTaskDetailVo> queryContractTaskDetail(EcsReimContractTask dto);
}
