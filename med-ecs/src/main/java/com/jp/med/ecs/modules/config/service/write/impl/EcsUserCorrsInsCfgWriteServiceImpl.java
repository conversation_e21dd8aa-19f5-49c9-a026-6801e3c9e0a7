package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ecs.modules.config.dto.EcsUserCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.mapper.write.EcsUserCorrsInsCfgWriteMapper;
import com.jp.med.ecs.modules.config.service.write.EcsUserCorrsInsCfgWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 费用报销用户-往来单位映射配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-30 10:36:18
 */
@Service
@Transactional(readOnly = false)
public class EcsUserCorrsInsCfgWriteServiceImpl extends ServiceImpl<EcsUserCorrsInsCfgWriteMapper, EcsUserCorrsInsCfgDto> implements EcsUserCorrsInsCfgWriteService {
}
