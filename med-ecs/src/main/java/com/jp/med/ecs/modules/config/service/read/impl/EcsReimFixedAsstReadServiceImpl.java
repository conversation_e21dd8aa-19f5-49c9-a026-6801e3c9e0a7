package com.jp.med.ecs.modules.config.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDetailDto;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDto;
import com.jp.med.common.vo.EcsReimFixedAsstDetailVo;
import com.jp.med.common.vo.EcsReimFixedAsstVo;
import com.jp.med.ecs.modules.config.mapper.read.EcsReimFixedAsstDetailReadMapper;
import com.jp.med.ecs.modules.config.mapper.read.EcsReimFixedAsstReadMapper;
import com.jp.med.ecs.modules.config.service.read.EcsReimFixedAsstReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsReimFixedAsstReadServiceImpl extends ServiceImpl<EcsReimFixedAsstReadMapper, EcsReimFixedAsstDto> implements EcsReimFixedAsstReadService {

    @Autowired
    private EcsReimFixedAsstReadMapper ecsReimFixedAsstReadMapper;

    @Autowired
    private EcsReimFixedAsstDetailReadMapper ecsReimFixedAsstDetailReadMapper;

    @Override
    public List<EcsReimFixedAsstVo> queryList(EcsReimFixedAsstDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        if (dto.getPaging() !=null && dto.getPaging()) {
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        List<EcsReimFixedAsstVo> ecsReimFixedAsstVos = ecsReimFixedAsstReadMapper.queryList(dto);
        if (ecsReimFixedAsstVos.isEmpty()){
            return new ArrayList<>();
        }
        ecsReimFixedAsstVos.stream().forEach(item -> {
            EcsReimFixedAsstDetailDto detailDto = new EcsReimFixedAsstDetailDto();
            detailDto.setFixedAsstId(item.getId());
            List<EcsReimFixedAsstDetailVo> detailVos = ecsReimFixedAsstDetailReadMapper.queryList(detailDto);
            item.setFixedAsstDetails(detailVos);
        });


        return ecsReimFixedAsstVos;
    }

    @Override
    public List<EcsReimFixedAsstVo> queryFixedTree(EcsReimFixedAsstDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimFixedAsstReadMapper.queryFixedTree(dto);
    }

}
