package com.jp.med.ecs.modules.reimMgt.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;

import com.jp.med.common.entity.audit.AuditCommonRes;
import lombok.Data;

/**
 * 经费申请
 * <AUTHOR>
 * @email -
 * @date 2023-12-28 17:58:39
 */
@Data
public class EcsFundApplyVo extends AuditCommonRes {

	/** id */
	private Integer id;

	/** 申请人 */
	private String appyer;

	/** 申请科室 */
	private String appyerDept;

	/** 申请时间 */
	private String appyerTime;

	/** 申请事由 */
	private String appyerRea;

	/** 合计金额小写(元) */
	private BigDecimal sum;

	/** 合计金额大写 */
	private String capSum;

	/** 开户银行 */
	private String bank;

	/** 户名 */
	private String acctname;

	/** 银行账(卡)号 */
	private String bankcode;

	/** 审核批次号 */
	private String auditBchno;

	/** 创建人 */
	private String crter;

	/** 医疗机构id */
	private String hospitalId;

	/** 审核标识 */
	private String auditFlag;

}
