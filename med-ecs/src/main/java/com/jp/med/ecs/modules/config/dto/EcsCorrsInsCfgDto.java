package com.jp.med.ecs.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 往来单位配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Data
@TableName("ecs_corrs_ins_cfg" )
public class EcsCorrsInsCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 单位代码 */
    @TableField("ins_code")
    private String insCode;

    /** 单位名称 */
    @TableField("ins_name")
    private String insName;

    /** 单位全称 */
    @TableField("fulname")
    private String fulname;

    /** 单位类型 */
    @TableField("ins_type")
    private String insType;

    /** 通信地址 */
    @TableField("comm_addr")
    private String commAddr;

    /** 邮政编码 */
    @TableField("poscode")
    private String poscode;

    /** 联系人姓名 */
    @TableField("coner_name")
    private String conerName;

    /** 联系人电话 */
    @TableField("coner_tel")
    private String conerTel;

    /** 传真 */
    @TableField("fax")
    private String fax;

    /** 邮箱 */
    @TableField("email")
    private String email;

    /** 单位属性 */
    @TableField("ins_natu")
    private String insNatu;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改时间 */
    @TableField("modi_time")
    private String modiTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 */
    @TableField("active_flag")
    private String activeFlag;

    @TableField("year")
    private String year;

    /** 查询字符串 */
    @TableField(exist = false)
    private String qs;

}
