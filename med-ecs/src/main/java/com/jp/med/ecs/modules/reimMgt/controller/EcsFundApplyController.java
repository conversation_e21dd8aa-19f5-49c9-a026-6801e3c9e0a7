package com.jp.med.ecs.modules.reimMgt.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ecs.modules.reimMgt.dto.EcsFundApplyDto;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsFundApplyReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsFundApplyWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 经费申请
 * <AUTHOR>
 * @email -
 * @date 2023-12-28 17:58:39
 */
@Api(value = "经费申请", tags = "经费申请")
@RestController
@RequestMapping("ecsFundApply")
public class EcsFundApplyController {

    @Autowired
    private EcsFundApplyReadService ecsFundApplyReadService;

    @Autowired
    private EcsFundApplyWriteService ecsFundApplyWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询经费申请")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsFundApplyDto dto){
        return CommonResult.paging(ecsFundApplyReadService.queryList(dto));
    }

    @ApiOperation("查询APP审核详情")
    @PostMapping("/appAuditDetail")
    public CommonResult<?> queryAppAuditDetail(@RequestBody EcsFundApplyDto dto){
        return CommonResult.success(ecsFundApplyReadService.queryAppAuditDetail(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增经费申请")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsFundApplyDto dto){
        ecsFundApplyWriteService.saveFundApply(dto);
        return CommonResult.success();
    }


    /**
     * 修改
     */
    @ApiOperation("修改经费申请")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsFundApplyDto dto){
        ecsFundApplyWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除经费申请")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsFundApplyDto dto){
        ecsFundApplyWriteService.removeById(dto);
        return CommonResult.success();
    }

}
