package com.jp.med.ecs.modules.reimMgt.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 在编招聘-按科室
 */
@Data
public class EcsSalarySummary2Excel {

    /** 部门名称 **/
    @ExcelProperty("部门名称")
    private String orgName;

    /** 人数 **/
    @ExcelProperty("人数")
    private Integer peoNum;

    /** 发放月份 **/
    @ExcelProperty("发放月份")
    private String month;

    /** 岗位工资 **/
    @ExcelProperty("岗位工资")
    private BigDecimal postSalary;

    /** 薪级工资 **/
    @ExcelProperty("薪级工资")
    private BigDecimal salGradeSalary;

    /** 护士10% **/
    @ExcelProperty("护士10%")
    private BigDecimal nurseSalary;

    /** 地区附件津贴 **/
    @ExcelProperty("地区附加津贴")
    private BigDecimal areaSalary;

    /** 护龄补贴 **/
    @ExcelProperty("护龄补贴")
    private BigDecimal ageSalary;

    /** 驾驶员津贴 **/
    @ExcelProperty("驾驶员津贴")
    private BigDecimal driverSalary;

    /** 通讯费补贴 **/
    @ExcelProperty("通讯费补贴")
    private BigDecimal communicationFeeAllowance;

    /** 基础绩效 **/
    @ExcelProperty("基础绩效")
    private BigDecimal basicPerf;

    /**
     * 生活补贴
     **/
    @ExcelProperty("生活补贴")
    private BigDecimal lifeSalary;

    /** 人力临时增加 **/
    @ExcelProperty("人力临时增加")
    private BigDecimal temporaryAddSalary;

    /** 财务临时增加 **/
    @ExcelProperty("财务临时增加")
    private BigDecimal temporaryAddSalary2;

    /** 应发合计 **/
    @ExcelProperty("应发合计")
    private BigDecimal totalPayable;

    /** 养老保险 **/
    @ExcelProperty("养老保险")
    private BigDecimal pensionInsurance;

    /** 医疗保险 **/
    @ExcelProperty("医疗保险")
    private BigDecimal medicalInsurance;

    /** 失业保险 **/
    @ExcelProperty("失业保险")
    private BigDecimal unemploymentInsurance;

    /** 住房基金 **/
    @ExcelProperty("住房基金")
    private BigDecimal housingFund;

    /** 职业年金 **/
    @ExcelProperty("职业年金")
    private BigDecimal occupationalAnnuity;

    /** 工会会费 **/
    @ExcelProperty("工会会费")
    private BigDecimal laborUnion;

    /** 个人所得税 **/
    @ExcelProperty("个人所得税")
    private BigDecimal personTax;

    /** 人力临时扣款 **/
    @ExcelProperty("人力临时扣款")
    private BigDecimal temporaryReduceSalary;

    /** 财务临时扣款 **/
    @ExcelProperty("财务临时扣款")
    private BigDecimal temporaryReduceSalary2;

    /** 扣款合计 **/
    @ExcelProperty("扣款合计")
    private BigDecimal reduceSalaryTotal;

    /** 实发合计 **/
    @ExcelProperty("实发合计")
    private BigDecimal totalPaid;












}
