package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.reimMgt.dto.EcsFundApplyDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsFundApplyVo;

import java.util.List;

/**
 * 经费申请
 * <AUTHOR>
 * @email -
 * @date 2023-12-28 17:58:39
 */
public interface EcsFundApplyReadService extends IService<EcsFundApplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsFundApplyVo> queryList(EcsFundApplyDto dto);

    /**
     * 查询APP审核详情
     * @param dto
     * @return
     */
    EcsFundApplyVo queryAppAuditDetail(EcsFundApplyDto dto);
}

