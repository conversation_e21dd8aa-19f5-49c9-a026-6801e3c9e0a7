package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDetailDto;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.DateUtil;
import com.jp.med.ecs.modules.config.mapper.read.EcsReimFixedAsstReadMapper;
import com.jp.med.ecs.modules.config.mapper.write.EcsReimFixedAsstDetailWriteMapper;
import com.jp.med.ecs.modules.config.mapper.write.EcsReimFixedAsstWriteMapper;
import com.jp.med.ecs.modules.config.service.write.EcsReimFixedAsstWriteService;
import io.seata.common.util.CollectionUtils;
import io.seata.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;


/**
 * 会计科目固定项
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Service
@Transactional(readOnly = false)
public class EcsReimFixedAsstWriteServiceImpl extends ServiceImpl<EcsReimFixedAsstWriteMapper, EcsReimFixedAsstDto> implements EcsReimFixedAsstWriteService {

    @Autowired
    private EcsReimFixedAsstDetailWriteMapper ecsReimFixedAsstDetailWriteMapper;

    @Autowired
    private EcsReimFixedAsstReadMapper ecsReimFixedAsstReadMapper;

    @Override
    public boolean removeByIdWithAsstDetail(EcsReimFixedAsstDto dto) {
        List<Integer> ids = dto.getIds();
        if (ids.isEmpty()){
            throw new AppException("参数不能为空");
        }

        //删除固定项
        removeBatchByIds(ids);
        //删除固定项详情
        LambdaQueryWrapper<EcsReimFixedAsstDetailDto> query = new LambdaQueryWrapper<>();
        query.in(EcsReimFixedAsstDetailDto::getFixedAsstId,ids);

        ecsReimFixedAsstDetailWriteMapper.delete(query);
        return true;
    }

    @Override
    public void saveFixedAsstWithDetail(EcsReimFixedAsstDto dto) {
        //查询固定项编码是否存在
        dto.setSqlAutowiredHospitalCondition(true);
        LambdaQueryWrapper<EcsReimFixedAsstDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EcsReimFixedAsstDto::getFixedSubCode,dto.getFixedSubCode())
                .eq(EcsReimFixedAsstDto::getFundType,dto.getFundType())
                .eq(EcsReimFixedAsstDto::getDeptType,dto.getDeptType())
                .eq(EcsReimFixedAsstDto::getSubType,dto.getSubType())
                .eq(EcsReimFixedAsstDto::getEconFunSubCode,dto.getEconFunSubCode());
        //如果是药品报销，则需要再判断备注信息
        if (StringUtils.equals(dto.getFixedSubCode(), MedConst.PAY_TYPECODE_DRUG)) {
            queryWrapper.eq(EcsReimFixedAsstDto::getAbst,dto.getAbst());
        }
        //新增项为科目才判断资金类型是否重复,为目录则不判断
        /*if (StringUtils.equals(dto.getItemType(),MedConst.TYPE_2)){
            queryWrapper.eq(EcsReimFixedAsstDto::getFundType,dto.getFundType());
            //新增项为分摊类型才添加shareType条件的唯一性判断
            if (StringUtils.equals(dto.getFixedType(), MedConst.TYPE_2)){
                queryWrapper.eq(EcsReimFixedAsstDto::getShareType,dto.getShareType());
            }
        }*/
        EcsReimFixedAsstDto one = ecsReimFixedAsstReadMapper.selectOne(queryWrapper);
        if (!Objects.isNull(one)){
            throw new AppException("当前固定项编码已存在");
        }

        //新增固定项
        dto.setCrter(dto.getSysUser().getUsername());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        save(dto);
        addFixedAsstDetails(dto);
    }

    @Override
    public void updateFixedAsstWithDetail(EcsReimFixedAsstDto dto) {
        //修改固定项
        updateById(dto);
        //删除当前固定项辅助项
        ecsReimFixedAsstDetailWriteMapper.removeByFixedAsstId(dto.getId());
        //新增辅助项
        addFixedAsstDetails(dto);
    }

    private void addFixedAsstDetails(EcsReimFixedAsstDto dto){
        if (CollectionUtils.isNotEmpty(dto.getFixedAsstDetails())){
            for (EcsReimFixedAsstDetailDto detail: dto.getFixedAsstDetails()){
                detail.setFixedAsstId(dto.getId());
                detail.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
                detail.setCreateTime(DateUtil.getCurrentTime(null));
                detail.setHospitalId(dto.getHospitalId());
            }
            ecsReimFixedAsstDetailWriteMapper.addFixedDetails(dto.getFixedAsstDetails());
        }
    }
}
