package com.jp.med.ecs.modules.config.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDto;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
public interface EcsReimFixedAsstWriteService extends IService<EcsReimFixedAsstDto> {

    /**
     * 删除固定项及对应的详情信息
     * @param dto
     * @return
     */
    boolean removeByIdWithAsstDetail(EcsReimFixedAsstDto dto);

    /**
     * 新增固定项及其辅助项
     * @param dto
     */
    void saveFixedAsstWithDetail(EcsReimFixedAsstDto dto);

    /**
     * 修改固定项及其辅助项
     * @param dto
     */
    void updateFixedAsstWithDetail(EcsReimFixedAsstDto dto);
}

