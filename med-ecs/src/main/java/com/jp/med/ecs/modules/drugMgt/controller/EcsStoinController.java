package com.jp.med.ecs.modules.drugMgt.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsStoinReadService;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsStoinWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 入库单
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:44
 */
@Api(value = "入库单", tags = "入库单")
@RestController
@RequestMapping("ecsStoin")
public class EcsStoinController {

    @Autowired
    private EcsStoinReadService ecsStoinReadService;

    @Autowired
    private EcsStoinWriteService ecsStoinWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询入库单")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsStoinDto dto){
        return CommonResult.paging(ecsStoinReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询入库单")
    @PostMapping("/listNoPage")
    public CommonResult<?> listNoPage(@RequestBody EcsStoinDto dto){
        return CommonResult.success(ecsStoinReadService.queryList(dto));
    }

    @ApiOperation("查询月度数量")
    @PostMapping("/monthNum")
    public CommonResult<?> monthNum(@RequestBody EcsStoinDto dto){
        return CommonResult.success(ecsStoinReadService.monthNum(dto));
    }

    /**
     * 同步入库单
     * @param dto
     * @return
     */
    @ApiOperation("同步入库单")
    @PostMapping("/drugSync")
    public CommonResult<?> drugSync(@RequestBody EcsStoinDto dto){
        ecsStoinWriteService.drugSync(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增入库单")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsStoinDto dto){
        ecsStoinWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改入库单")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsStoinDto dto){
        ecsStoinWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除入库单")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsStoinDto dto){
        ecsStoinWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 查询入库单明细
     * @param dto
     * @return
     */
    @ApiOperation("查询入库单明细")
    @PostMapping("/queryStoinDetails")
    public CommonResult<?> queryStoinDetails(@RequestBody EcsStoinDto dto) {
        return CommonResult.success(ecsStoinReadService.queryStoinDetails(dto));
    }
}
