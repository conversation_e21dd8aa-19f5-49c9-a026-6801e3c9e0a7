package com.jp.med.ecs.modules.reimMgt.controller;

import com.jp.med.common.dto.ecs.EcsReimDeprTaskDetailDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimDeprTaskDetailReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimDeprTaskDetailWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 折旧任务明细
 * <AUTHOR>
 * @email -
 * @date 2025-02-19 15:12:46
 */
@Api(value = "折旧任务明细", tags = "折旧任务明细")
@RestController
@RequestMapping("ecsReimDeprTaskDetail")
public class EcsReimDeprTaskDetailController {

    @Autowired
    private EcsReimDeprTaskDetailReadService ecsReimDeprTaskDetailReadService;

    @Autowired
    private EcsReimDeprTaskDetailWriteService ecsReimDeprTaskDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询折旧任务明细")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsReimDeprTaskDetailDto dto){
        return CommonResult.paging(ecsReimDeprTaskDetailReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询折旧任务明细")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimDeprTaskDetailDto dto){
        return CommonResult.success(ecsReimDeprTaskDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增折旧任务明细")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsReimDeprTaskDetailDto dto){
        ecsReimDeprTaskDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改折旧任务明细")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimDeprTaskDetailDto dto){
        ecsReimDeprTaskDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除折旧任务明细")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimDeprTaskDetailDto dto){
        ecsReimDeprTaskDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
