package com.jp.med.ecs.modules.config.service.read.impl;

import com.jp.med.common.util.TreeNewUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsActigAsstDeptCfgReadMapper;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstDeptCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstDeptCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsActigAsstDeptCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsActigAsstDeptCfgReadServiceImpl extends ServiceImpl<EcsActigAsstDeptCfgReadMapper, EcsActigAsstDeptCfgDto> implements EcsActigAsstDeptCfgReadService {

    @Autowired
    private EcsActigAsstDeptCfgReadMapper ecsActigAsstDeptCfgReadMapper;

    @Override
    public List<EcsActigAsstDeptCfgVo> queryList(EcsActigAsstDeptCfgDto dto) {
        TreeNewUtil<String, EcsActigAsstDeptCfgVo> treeNewUtil = new TreeNewUtil<>();
        return treeNewUtil.buildTree(ecsActigAsstDeptCfgReadMapper.queryList(dto));
    }

}
