package com.jp.med.ecs.modules.drugMgt.rabbitMq;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugReimDetaiReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsStoinReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsDrugReimDetaiWriteMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsStoinWriteMapper;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsInvoRcdWriteMapper;
import com.rabbitmq.client.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@Getter
@Setter
public class EcsDrugDetailBpmApproveMessageHandle extends AbstractBpmApproveMessageHandle {

    // @Override
    // public String[] getProcessIdentifier() {
    // return new String[] { "oa_leave" };
    // }
    @Autowired
    private EcsDrugReimDetaiReadMapper ecsDrugReimDetaiReadMapper;

    @Autowired
    private EcsDrugReimDetaiWriteMapper ecsDrugReimDetaiWriteMapper;

    @Autowired
    private EcsStoinReadMapper ecsStoinReadMapper;

    @Autowired
    private EcsStoinWriteMapper ecsStoinWriteMapper;

    @Autowired
    private EcsInvoRcdWriteMapper ecsInvoRcdWriteMapper;


    public String[] processIdentifier = {"ECS_DRUG_REIM_XDYP","ECS_DRUG_REIM_ZXY"};

    @Override
    @RabbitListener(queues = {"ECS_DRUG_REIM_XDYP","ECS_DRUG_REIM_ZXY"})
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        receiveMessage0(msg);

        // try {
        // // 处理消息
        //
        // System.out.println(Arrays.toString(message.getBody()));
        // System.out.println(msg);
        // receiveMessage0(msg);
        //
        // // 手动确认消息
        // channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        //// throw new Exception();
        // } catch (Exception e) {
        // log.info("异常{}:{}",msg.getProcessDefinitionKey(),msg.getBusinessKey());
        // // 处理异常，选择是否重试或拒绝消息
        // channel.basicNack(message.getMessageProperties().getDeliveryTag(), false,
        // true);
        // }
    }

    /**
     * 处理创建的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {

    }

    // @RabbitListener(queues = processIdentifier)
    // public void receiveMessage(BpmProcessInstanceStatus message) {
    //
    // receiveMessage0(message);
    //
    // }

    /**
     * 处理审批通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {
        //审核通过，更新留存的药品报销为通过状态(某些报销可能在某些节点已被选为不通过且已被删除）
        //流程实例对应的报销id
        List<String> reimIds = Arrays.asList(message.getBusinessKey().split(","));
        //查询留存的药品报销 某些可能已经被删除
        EcsDrugReimDetaiDto param = new EcsDrugReimDetaiDto();
        param.setIds(reimIds.stream().map(Integer::parseInt).collect(Collectors.toList()));
        List<EcsDrugReimDetaiVo> ecsDrugReimDetaiVos = ecsDrugReimDetaiReadMapper.queryList(param);
        /*LambdaQueryWrapper<EcsDrugReimDetaiDto> drugReimWrapper = Wrappers.lambdaQuery(EcsDrugReimDetaiDto.class);
        drugReimWrapper.in(EcsDrugReimDetaiDto::getId, reimIds);
        List<EcsDrugReimDetaiDto> ecsDrugReimDetaiDtos = ecsDrugReimDetaiReadMapper.selectList(drugReimWrapper);*/
        //审核成功处理
        handleDrugReimResult(ecsDrugReimDetaiVos,AuditConst.STATE_SUCCESS);

    }

    private void handleDrugReimResult(List<EcsDrugReimDetaiVo> drugReims,String result) {
        //更新药品报销状态
        updateDrugReimStatus(drugReims,result);

        //获取入库单
        List<EcsStoinDto> stoins = getStoinDtoFromDrugReim(drugReims);

        //更新入库单状态
        updateStoinStatus(stoins,result);

        //获取并更新发票状态
        Set<Long> invoIds = getInvoIdsFromStoins(stoins);
        if (!invoIds.isEmpty()) {
            updateInvoiceStatus(invoIds,result);
        }

        //删除审核失败的报销
        if (StringUtils.equals(AuditConst.STATE_FAIL,result)) {
            List<Long> reimIds = drugReims.stream().map(EcsDrugReimDetaiVo::getId).collect(Collectors.toList());
            LambdaQueryWrapper<EcsDrugReimDetaiDto> deleteWrapper = Wrappers.lambdaQuery(EcsDrugReimDetaiDto.class);
            deleteWrapper.in(EcsDrugReimDetaiDto::getId,reimIds);
            ecsDrugReimDetaiWriteMapper.delete(deleteWrapper);
        }
    }

    // 更新药品报销状态
    private void updateDrugReimStatus(List<EcsDrugReimDetaiVo> drugReims, String result) {
        List<Long> reimIds = drugReims.stream().map(EcsDrugReimDetaiVo::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<EcsDrugReimDetaiDto> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(EcsDrugReimDetaiDto::getId, reimIds);
        wrapper.set(EcsDrugReimDetaiDto::getStatus,
                        StringUtils.equals(AuditConst.STATE_SUCCESS, result)
                                ? EcsConst.DRUG_REIM_STATUS_SUCCESS
                                : EcsConst.DRUG_REIM_STATUS_FAILED)
                .set(EcsDrugReimDetaiDto::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        ecsDrugReimDetaiWriteMapper.update(null, wrapper);
    }

    //获取入库单
    private List<EcsStoinDto> getStoinDtoFromDrugReim(List<EcsDrugReimDetaiVo> drugReims) {
        List<Long> reimIds = drugReims.stream().map(EcsDrugReimDetaiVo::getId).collect(Collectors.toList());
        LambdaQueryWrapper<EcsStoinDto> stoinWrapper = Wrappers.lambdaQuery();
        stoinWrapper.in(EcsStoinDto::getDrugReimDetailId, reimIds);
        return ecsStoinReadMapper.selectList(stoinWrapper);
    }

    //更新入库单状态
    private void updateStoinStatus(List<EcsStoinDto> stoins,String result) {
        //如果非审核成功，则更新入库单为可报销状态
        if (!StringUtils.equals(AuditConst.STATE_SUCCESS, result)) {
            List<Integer> stoinIds = stoins.stream().map(EcsStoinDto::getId).collect(Collectors.toList());
            LambdaUpdateWrapper<EcsStoinDto> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(EcsStoinDto::getReimFlag, MedConst.TYPE_0)
                    .set(EcsStoinDto::getDrugReimDetailId,null)
                    .set(EcsStoinDto::getAtt,null)
                    .set(EcsStoinDto::getAttName,null)
                    .set(EcsStoinDto::getInvoId,null)
                    .in(EcsStoinDto::getId,stoinIds);
            ecsStoinWriteMapper.update(null,updateWrapper);
        }
    }

    private Set<Long> getInvoIdsFromStoins(List<EcsStoinDto> ecsStoinDtos) {
        Set<Long> invoIds = new HashSet<>();
        ecsStoinDtos.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getInvoId())) {
                Arrays.stream(item.getInvoId().split(","))
                        .map(Long::valueOf)
                        .forEach(invoIds::add);
            }
        });
        return invoIds;
    }

    private void updateInvoiceStatus(Set<Long> invoIds, String result) {
        String newState = StringUtils.equals(AuditConst.STATE_SUCCESS, result)
                ? MedConst.TYPE_2
                : MedConst.TYPE_1;
        ecsInvoRcdWriteMapper.updateStateByIds(new ArrayList<>(invoIds), newState,StringUtils.equals(newState,MedConst.TYPE_1)?"": "药品报销");
    }

    /**
     * 处理审批不通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        //更新所有药品报销为不通过
        //流程实例对应的报销id
        List<String> reimIds = Arrays.asList(message.getBusinessKey().split(","));
        //查询留存的药品报销 某些可能已经被删除
        EcsDrugReimDetaiDto param = new EcsDrugReimDetaiDto();
        param.setIds(reimIds.stream().map(Integer::parseInt).collect(Collectors.toList()));
        List<EcsDrugReimDetaiVo> ecsDrugReimDetaiVos = ecsDrugReimDetaiReadMapper.queryList(param);
        //审核成功处理
        handleDrugReimResult(ecsDrugReimDetaiVos,AuditConst.STATE_FAIL);
    }

    /**
     * 处理审批中的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {

    }

    /**
     * 处理已取消的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {

    }

    // /**
    // * 处理已退回的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleReturned(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理委派中的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleDelegated(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理审批通过中的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleApproving(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理待审批的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleWaiting(BpmProcessInstanceStatus message) {
    //
    // }

}
