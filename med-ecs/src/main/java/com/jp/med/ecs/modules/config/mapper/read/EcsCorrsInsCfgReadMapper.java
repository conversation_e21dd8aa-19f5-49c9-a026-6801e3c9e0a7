package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.ecs.modules.config.dto.EcsCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsCorrsInsCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 往来单位配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Mapper
public interface EcsCorrsInsCfgReadMapper extends BaseMapper<EcsCorrsInsCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsCorrsInsCfgVo> queryList(EcsCorrsInsCfgDto dto);
}
