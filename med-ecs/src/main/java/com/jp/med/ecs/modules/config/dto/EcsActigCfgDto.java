package com.jp.med.ecs.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * 会计科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Data
@TableName("ecs_actig_cfg" )
public class EcsActigCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 科目代码 */
    @TableField("sub_code")
    private String subCode;

    /** 科目名称 */
    @TableField("sub_name")
    private String subName;

    /** 科目类型 */
    @TableField("sub_type")
    private String subType;

    /** 备注 */
    @TableField("remarks")
    private String remarks;

    /** 拼音助记码 */
    @TableField("pinyin")
    private String pinyin;

    /** 辅助信息 */
    @TableField("asst_info")
    private String asstInfo;

    /** 会计要素 */
    @TableField("actig_elem")
    private String actigElem;

    /** 会计体系 */
    @TableField("actig_sys")
    private String actigSys;

    /** 余额方向 */
    @TableField("balc_dirc")
    private String balcDirc;

    /** 单位类型 */
    @TableField("emp_type")
    private String empType;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改时间 */
    @TableField("modi_time")
    private String modiTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 年度 **/
    @TableField("year")
    private String year;

    /** 父科目代码 **/
    @TableField("parent_code")
    private String parentCode;

    /** 单位类型数组 */
    @TableField(exist = false)
    private String[] empTypeArr;

    /** 辅助信息数组 */
    @TableField(exist = false)
    private String[] asstInfoArr;

    /** 状态 */
    @TableField(exist = false)
    private String status;

    /** 科目代码或名称 */
    @TableField(exist = false)
    private String sub;
}
