package com.jp.med.ecs.modules.reimMgt.entity;

import com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/1 16:48
 * @description: OCR识别结果
 */
@Data
public class EcsOCRRes {

    /** 状态码 200成功，其他失败 */
    private String code;

    /** 错误信息 */
    private String message;

    /** OCR识别的数据 */
    private String identifyData;

    /** 返回的数据 */
    private EcsInvoRcdVo data;
}
