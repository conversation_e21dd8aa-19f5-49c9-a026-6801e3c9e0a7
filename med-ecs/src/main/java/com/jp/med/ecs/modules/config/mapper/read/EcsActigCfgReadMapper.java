package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.ecs.modules.config.dto.EcsActigCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 会计科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Mapper
public interface EcsActigCfgReadMapper extends BaseMapper<EcsActigCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsActigCfgVo> queryList(EcsActigCfgDto dto);
}
