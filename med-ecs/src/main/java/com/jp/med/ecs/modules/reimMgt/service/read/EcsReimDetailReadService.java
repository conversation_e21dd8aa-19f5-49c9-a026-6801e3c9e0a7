package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimApprManage;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimItemDetail;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimPsnDetail;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.HomeMsgNoteVo;

import java.util.List;
import java.util.Map;

/**
 * 报销明细
 * <AUTHOR>
 * @email -
 * @date 2023-12-04 22:01:14
 */
public interface EcsReimDetailReadService extends IService<EcsReimDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimDetailVo> queryList(EcsReimDetailDto dto);

    /**
     * 查询列表
     * @param dto
     * @return
     */
    List<EcsReimDetailVo> queryListNew(EcsReimDetailDto dto);

    /**
     * 查询项目详情
     * @param dto
     * @return
     */
    Map<String,Object> queryItemDetail(EcsReimDetailDto dto);

    /**
     * 查询科室已经报销金额
     * @param dto
     * @return
     */
    List<EcsReimPsnDetail> queryDeptAmt(EcsReimDetailDto dto);

    /**
     * 查询报销人员
     * @param dto
     * @return
     */
    List<EcsReimPsnDetail> psnDetails(EcsReimDetailDto dto);

    /**
     * 查询APP审核详情
     * @param dto
     * @return
     */
    EcsReimDetailVo queryAppAuditDetail(EcsReimDetailDto dto);

    /**
     * 查询APP审核详情-其他费用报销
     * @param dto
     * @return
     */
    EcsReimDetailVo queryAppAuditDetail2(EcsReimDetailDto dto);

    Integer queryExpenseAuditWarnNum(EcsReimDetailDto dto);

    List<EcsReimApprManage> queryTravelPsnInfo(EcsReimDetailDto dto);

    /**
     * 生成费用报销打印
     * @param params
     * @return
     */
    Map<String,String> queryEcsReimDoc(Map<String,Object> params);

    List<EcsReimDetailVo> queryNoPageList(EcsReimDetailDto dto);

    List<HomeMsgNoteVo> msgNote(EcsReimDetailDto dto);

    List<EcsReimDetailVo>  queryNoPageListNew(EcsReimDetailDto dto);

    Map<String,Object> expenseOverview(EcsReimDetailDto dto);

    Map<String,Object> expenseTrends(EcsReimDetailDto dto);

    List<EcsReimPsnDetail> queryZCFDeptAmt(EcsReimDetailDto dto);

    Map<Long,List<EcsReimPsnDetail>> psnDetailsMultiTrip(EcsReimDetailDto dto);

    List<EcsReimDetailVo> queryListMultiTrip(EcsReimDetailDto dto);

    Map<String,String>  queryEcsReimDocMultiTrip(List<Map<String,Object>> params);

    List<EcsReimItemDetail> queryItemDetail2(EcsReimDetailDto dto);

    Object myAppr(EcsReimDetailDto dto);

    Object myAudit(EcsReimDetailDto dto);
}

