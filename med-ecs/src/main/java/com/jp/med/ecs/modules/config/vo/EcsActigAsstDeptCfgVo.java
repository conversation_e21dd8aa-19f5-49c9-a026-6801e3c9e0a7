package com.jp.med.ecs.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 会计科目科室辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2024-02-01 16:36:57
 */
@Data
public class EcsActigAsstDeptCfgVo implements BaseTree<String, EcsActigAsstDeptCfgVo> {

	/** id */
	private Integer id;

	/** 科室编码 */
	private String deptCode;

	/** 科室名称 */
	private String deptName;

	/** 拼音助记码 */
	private String pinyin;

	/** 备注 */
	private String remarks;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改时间 */
	private String modiTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 启用标志 */
	private String activeFlag;

	/** 上级科室编码 */
	private String parentDeptCode;

	/**  */
	private String value;

	/**  */
	private String label;

	/** 子集 */
	private List<EcsActigAsstDeptCfgVo> children;

	@Override
	public String getCode() {
		return this.deptCode;
	}

	@Override
	public void setCode(String code) {
		this.deptCode = code;
	}

	@Override
	public String getPid() {
		return this.parentDeptCode;
	}

	@Override
	public void setPid(String pid) {
		this.parentDeptCode = pid;
	}

	@Override
	public void addChild(EcsActigAsstDeptCfgVo node) {
		if (Objects.isNull(this.children)){
			this.children = new ArrayList<>();
		}
		this.children.add(node);
	}
}
