package com.jp.med.ecs.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ecs.modules.config.dto.EcsTravelAccomStandardCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsTravelAccomStandardCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsTravelAccomStandardCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 差旅住宿费标准
 * <AUTHOR>
 * @email -
 * @date 2024-05-23 11:12:00
 */
@Api(value = "差旅住宿费标准", tags = "差旅住宿费标准")
@RestController
@RequestMapping("ecsTravelAccomStandardCfg")
public class EcsTravelAccomStandardCfgController {

    @Autowired
    private EcsTravelAccomStandardCfgReadService ecsTravelAccomStandardCfgReadService;

    @Autowired
    private EcsTravelAccomStandardCfgWriteService ecsTravelAccomStandardCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询差旅住宿费标准")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsTravelAccomStandardCfgDto dto){
        return CommonResult.paging(ecsTravelAccomStandardCfgReadService.queryList(dto));
    }

    /**
     * 查询列表(不分页)
     * @param dto
     * @return
     */
    @ApiOperation("查询差旅住宿费标准")
    @PostMapping("/noPageList")
    public CommonResult<?> noPageList(@RequestBody EcsTravelAccomStandardCfgDto dto){
        return CommonResult.success(ecsTravelAccomStandardCfgReadService.queryNoPageList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增差旅住宿费标准")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsTravelAccomStandardCfgDto dto){
        ecsTravelAccomStandardCfgWriteService.saveTravelAccomStandard(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改差旅住宿费标准")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsTravelAccomStandardCfgDto dto){
        ecsTravelAccomStandardCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除差旅住宿费标准")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsTravelAccomStandardCfgDto dto){
        ecsTravelAccomStandardCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
