package com.jp.med.ecs.modules.config.mapper.write;

import com.jp.med.ecs.modules.config.dto.EcsItemCfgDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 财务科目辅助项目项目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Mapper
public interface EcsItemCfgWriteMapper extends BaseMapper<EcsItemCfgDto> {
    void saveItemCfg(List<EcsItemCfgDto> efcDtos);

}
