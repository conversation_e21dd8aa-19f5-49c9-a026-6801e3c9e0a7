package com.jp.med.ecs.modules.reimMgt.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 分摊类型-往来单位详情
 */
@Data
public class EcsReimRelCoDetail {

    /** id */
    @ExcelIgnore
    private Integer id;

    /** 报销明细 */
    @ExcelIgnore
    private Integer reimDetailId;

    /** 往来单位code */
    @ExcelProperty(index = 1)
    private String relCoCode;

    /** 往来单位name */
    @ExcelProperty(index = 0)
    private String relCoName;

    /** 金额 */
    @ExcelProperty(index = 4)
    private BigDecimal amt;
}
