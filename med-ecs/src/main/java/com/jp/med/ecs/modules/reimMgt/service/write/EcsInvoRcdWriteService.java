package com.jp.med.ecs.modules.reimMgt.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;

/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
public interface EcsInvoRcdWriteService extends IService<EcsInvoRcdDto> {

    /**
     * 发票人工校正
     * @param dto
     * @return
     */
    void manualAmendInvo(EcsInvoRcdDto dto);

    /**
     * 发票校正审核
     * @param dto
     */
    void invoAudit(EcsInvoRcdDto dto);

    void invoDel(EcsInvoRcdDto dto);
}

