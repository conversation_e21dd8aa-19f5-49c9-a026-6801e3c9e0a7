package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDetailDto;
import com.jp.med.ecs.modules.config.mapper.write.EcsReimFixedAsstDetailWriteMapper;
import com.jp.med.ecs.modules.config.service.write.EcsReimFixedAsstDetailWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Service
@Transactional(readOnly = false)
public class EcsReimFixedAsstDetailWriteServiceImpl extends ServiceImpl<EcsReimFixedAsstDetailWriteMapper, EcsReimFixedAsstDetailDto> implements EcsReimFixedAsstDetailWriteService {
}
