package com.jp.med.ecs.modules.drugMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.ecs.modules.drugMgt.dto.*;
import com.jp.med.ecs.modules.drugMgt.vo.EcsStoinDetailVo;
import com.jp.med.common.vo.ecs.drug.EcsStoinVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 入库单
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:44
 */
@Mapper
public interface EcsStoinReadMapper extends BaseMapper<EcsStoinDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsStoinVo> queryList(EcsStoinDto dto);

    /**
     * 查询月份数量
     * @param dto
     * @return
     */
    List<Map<String, Integer>> monthNum(EcsStoinDto dto);

    /**
     * 查询入库单最大序号
     * @return
     */
    Integer queryMaxXh(@Param("isBack") String isBack);

    /**
     * 更新审核状态
     * @param ecsDrugReimDetaiDto
     */
    void updateReimState(EcsDrugReimDetaiDto ecsDrugReimDetaiDto);

    Integer selectNum();

    /** 同步入库单 **/
    List<EcsStoinDto> list(@Param("floor")int floor, @Param("offset")int offset, @Param("batchSize")int batchSize);

    /** 同步入库单退货 **/
    List<EcsStoinDto> listStoinBack(@Param("floor")int floor, @Param("offset")int offset, @Param("batchSize")int batchSize);

    /** 同步入库单明细 **/
    List<EcsStoinDetailDto> listStoinDetails(List<Integer> ids);

    /** 同步入库单退货明细 **/
    List<EcsStoinDetailDto> listStoinBackDetails(List<Integer> ids);

    List<EcsStoinDetailVo> queryStoinDetails(EcsStoinDto dto);
}
