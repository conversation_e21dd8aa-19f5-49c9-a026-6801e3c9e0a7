package com.jp.med.ecs.modules.drugMgt.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsStoinReadMapper;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsStoinReadService;
import com.jp.med.common.vo.ecs.drug.EcsStoinVo;
import com.jp.med.ecs.modules.drugMgt.vo.EcsStoinDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Transactional(readOnly = true)
@Service
@Slf4j
public class EcsStoinReadServiceImpl extends ServiceImpl<EcsStoinReadMapper, EcsStoinDto> implements EcsStoinReadService {

    @Autowired
    private EcsStoinReadMapper ecsStoinReadMapper;

    @Override
    public List<EcsStoinVo> queryList(EcsStoinDto dto) {
        if (dto.getPageNum() != null && dto.getPageSize() !=null) {
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        return ecsStoinReadMapper.queryList(dto);
    }

    @Override
    public List<Map<String, Integer>> monthNum(EcsStoinDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        if (StringUtils.isNotEmpty(dto.getIssue()) && dto.getIssue().length() > 4) {
            dto.setYear(dto.getIssue().substring(0,4));
            dto.setIssue(null);
        }
        return ecsStoinReadMapper.monthNum(dto);
    }

    @Override
    public List<EcsStoinDetailVo> queryStoinDetails(EcsStoinDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsStoinReadMapper.queryStoinDetails(dto);
    }
}
