package com.jp.med.ecs.modules.reimMgt.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 识别发票结果类型
 */
public enum InvoiceOcrEnum {
    INVOICE("增值税发票", "INVOICE","1"),
    CARINVOICE("机动车销售统一发票", "CARINVOICE","1"),
    QUOTAINVOICE("定额发票", "QUOTAINVOICE","0"),
    AIRITINERARY("机票行程单", "AIRITINERARY","0"),
    TRAINTICKET("火车票", "TRAINTICKET","0"),
    TOLLINVOICE("过路过桥费发票", "TOLLINVOICE","0"),
    ROLLTICKET("增值税发票卷票", "ROLLTICKET","1"),
    BANKACCEPTANCE("银行承兑汇票", "BANKACCEPTANCE","0"),
    BUSSHIPTICKET("客运车船票", "BUSSHIPTICKET","0"),
    NONTAXINVOICE("非税收入票据", "NONTAXINVOICE","0"),
    COMMONPRINTEDINVOICE("通用机打发票", "COMMONPRINTEDINVOICE","0"),

    TAXIINVOICE("出租车票","TAXIINVOICE","0");

    private final String typeCode;
    private final String typeName;

    private final String needCheck;

    InvoiceOcrEnum(String typeName,String typeCode,String needCheck) {
        this.typeCode = typeCode;
        this.typeName = typeName;
        this.needCheck = needCheck;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public String getNeedCheck() {
        return needCheck;
    }

    public static InvoiceOcrEnum getByTypeName(String typeName) {
        for (InvoiceOcrEnum type : InvoiceOcrEnum.values()) {
            if (StringUtils.equals(typeName,type.getTypeName())) {
                return type;
            }
        }
        return null;
    }

    public static InvoiceOcrEnum getByTypeCode(String typeCode) {
        for (InvoiceOcrEnum type : InvoiceOcrEnum.values()) {
            if (StringUtils.equals(typeCode,type.getTypeCode())) {
                return type;
            }
        }
        return null;
    }
}
