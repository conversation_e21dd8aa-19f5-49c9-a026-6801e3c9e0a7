package com.jp.med.ecs.modules.reimMgt.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.dto.ecs.EcsReimContractTask;
import com.jp.med.common.dto.ecs.EcsReimContractTaskDetail;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimContractTaskWriteMapper;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimContractTaskWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 合同报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
@Service
@Transactional(readOnly = false)
public class EcsReimContractTaskWriteServiceImpl extends ServiceImpl<EcsReimContractTaskWriteMapper, EcsReimContractTask> implements EcsReimContractTaskWriteService {

    @Autowired
    private EcsReimContractTaskWriteMapper ecsReimContractTaskWriteMapper;

    @Override
    public void saveContractTask(EcsReimContractTask dto) {
        List<EcsReimContractTaskDetail> details = dto.getDetails();
        if (Objects.isNull(details) || CollectionUtil.isEmpty(details)) {
            throw new AppException(EcsConst.CONTRACT_TASK_DETAIL_NOT_EXIST);
        }
        //保存合同任务
        ecsReimContractTaskWriteMapper.insert(dto);
        //保存合同任务明细
        details.stream().forEach(item -> {
            item.setTaskId(dto.getId());
        });
        BatchUtil.batch("insertContractTaskDetail",details, EcsReimContractTaskWriteMapper.class);
    }
}
