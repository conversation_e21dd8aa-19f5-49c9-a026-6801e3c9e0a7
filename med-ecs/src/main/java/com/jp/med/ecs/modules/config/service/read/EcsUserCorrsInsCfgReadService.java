package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsUserCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsUserCorrsInsCfgVo;

import java.util.List;

/**
 * 费用报销用户-往来单位映射配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-30 10:36:18
 */
public interface EcsUserCorrsInsCfgReadService extends IService<EcsUserCorrsInsCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsUserCorrsInsCfgVo> queryList(EcsUserCorrsInsCfgDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsUserCorrsInsCfgVo> queryPageList(EcsUserCorrsInsCfgDto dto);
}

