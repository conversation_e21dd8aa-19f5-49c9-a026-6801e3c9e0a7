package com.jp.med.ecs.modules.drugMgt.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatReimDetailDto;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsSatmatReimDetailReadService;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsSatmatReimDetailWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 卫材报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-08-02 09:28:49
 */
@Api(value = "卫材报销详情", tags = "卫材报销详情")
@RestController
@RequestMapping("ecsSatmatReimDetail")
public class EcsSatmatReimDetailController {

    @Autowired
    private EcsSatmatReimDetailReadService ecsSatmatReimDetailReadService;

    @Autowired
    private EcsSatmatReimDetailWriteService ecsSatmatReimDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询卫材报销详情")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsSatmatReimDetailDto dto){
        return CommonResult.paging(ecsSatmatReimDetailReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询卫材报销详情")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsSatmatReimDetailDto dto){
        return CommonResult.success(ecsSatmatReimDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增卫材报销详情")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsSatmatReimDetailDto dto){
        ecsSatmatReimDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改卫材报销详情")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsSatmatReimDetailDto dto){
        ecsSatmatReimDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除卫材报销详情")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsSatmatReimDetailDto dto){
        ecsSatmatReimDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增卫材报销详情")
    @PostMapping("/batchSave")
    public CommonResult<?> batchSave(EcsSatmatReimDetailDto dto){
        ecsSatmatReimDetailWriteService.batchSave(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询app审核详情数据")
    @PostMapping("/appAuditDetail")
    public CommonResult<?> queryAppAuditDetail(@RequestBody EcsSatmatReimDetailDto dto){
        return CommonResult.success(ecsSatmatReimDetailReadService.queryAppAuditDetail(dto));
    }

    @ApiOperation("查询卫材报销详情")
    @PostMapping("/auditData")
    public CommonResult<?> auditData(@RequestBody EcsSatmatReimDetailDto dto){
        return CommonResult.paging(ecsSatmatReimDetailReadService.queryAuditData(dto));
    }
}
