package com.jp.med.ecs.modules.reimMgt.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
@Data
public class EcsInvoRcdVo {

	/** id */
	private Long id;

	/** 发票代码 */
	private String invoCode;

	/** 发票号码 */
	private String invoNum;

	/** 开票日期 */
	private String invoDate;

	/** 校验码 */
	private String chkCode;

	/** 发票附件 */
	private String att;

	/** 发票附件名称 */
	private String attName;

	/** 校验状态(1:有效,0:无效) */
	private String chkState;

	/** 校验时间 */
	private String chkTime;

	/** OCR识别错误信息 */
	private String idtfErrMsg;

	/** 创建日期 */
	private String createTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 是否为发票 */
	private String isInvo;

	/** 状态，1：可报销，2：已报销，3：重复发票，4：发票报销审核中 */
	private String state;

	/** 核验数据 */
	private String chkData;

	/** 文件唯一标识 */
	private String fileIdentifier;

	/** 记录唯一标识 **/
	private String rcdIdentifier;

	/** 不含税金额 */
	private BigDecimal invoiceAmountPreTax;

	/** 含税金额 */
	private BigDecimal totalAmount;

	/** 税额 */
	private BigDecimal invoiceTax;

	/** 手动修正标志 **/
	private String manualAmend;

	/** 修正人员code **/
	private String amendUser;

	/** 校正人员Name **/
	private String amendUserName;

	/** 发票创建人 **/
	private String createUser;

	/** 发票使用人 **/
	private String invoUser;

	/** 审核状态 **/
	private String status;

	/** 校正申请人Name **/
	private String amendApplyerName;

	/** 备注 **/
	private String abs;

	/** 购方名称 **/
	private String purchaserName;

	/** 纳税人识别号 **/
	private String purchaserTaxpayerNumber;

	/** 发票金额(不含税) **/
	private String invoiceMoney;

	/** 价税合计 **/
	private String allValoremTax;

	/** 发票来源   1:hrp 2:供应商 3：卫材  不同来源表不同 **/
	private String invoFrom;

	/** 发票类型 **/
	private String invoType;

	/** 子发票ids **/
	private String subInvoIds;

	/** 是否子发票 **/
	private String isSub;

	/** 发票用于的业务 **/
	private String invoUsedBy;

}
