package com.jp.med.ecs.modules.config.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsDrugAccountPeriodDto;

/**
 * 药品账期
 * <AUTHOR>
 * @email -
 * @date 2025-02-21 10:36:30
 */
public interface EcsDrugAccountPeriodWriteService extends IService<EcsDrugAccountPeriodDto> {
    String reverseAccount(EcsDrugAccountPeriodDto dto);

    void closeAccount(EcsDrugAccountPeriodDto dto);
}

