package com.jp.med.ecs.modules.config.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDetailDto;
import com.jp.med.common.vo.EcsReimFixedAsstDetailVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Mapper
public interface EcsReimFixedAsstDetailReadMapper extends BaseMapper<EcsReimFixedAsstDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimFixedAsstDetailVo> queryList(EcsReimFixedAsstDetailDto dto);
}
