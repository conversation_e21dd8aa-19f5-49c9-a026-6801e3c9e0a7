package com.jp.med.ecs.modules.reimMgt.mapper.write;

import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ecs.modules.reimMgt.entity.EcsInvoChkRcdDetailEntity;
import com.jp.med.ecs.modules.reimMgt.entity.EcsInvoChkRcdEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
@Mapper
public interface EcsInvoRcdWriteMapper extends BaseMapper<EcsInvoRcdDto> {

    /**
     * 通过id更新状态
     * @param invoIds
     * @param type
     */
    void updateStateByIds(@Param("ids") List<Long> invoIds, @Param("type") String type,@Param("business") String businessType);

    /**
     * 插入发票核验记录
     * @param entity
     */
    void insertInvoChkRcd(EcsInvoChkRcdEntity entity);

    /**
     * 插入发票核验记录明细
     * @param entity
     */
    void insertInvoChkRcdDetail(EcsInvoChkRcdDetailEntity entity);
}
