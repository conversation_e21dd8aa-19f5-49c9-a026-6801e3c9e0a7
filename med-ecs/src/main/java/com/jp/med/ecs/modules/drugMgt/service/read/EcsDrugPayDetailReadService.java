package com.jp.med.ecs.modules.drugMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo;

import java.util.List;

/**
 * 药品报销付款详情
 * <AUTHOR>
 * @email -
 * @date 2024-11-25 12:03:17
 */
public interface EcsDrugPayDetailReadService extends IService<EcsDrugPayDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsDrugPayDetailVo> queryList(EcsDrugPayDetailDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsDrugPayDetailVo> queryPageList(EcsDrugPayDetailDto dto);
}

