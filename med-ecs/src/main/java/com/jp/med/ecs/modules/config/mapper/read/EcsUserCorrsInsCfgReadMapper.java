package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.ecs.modules.config.dto.EcsUserCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsUserCorrsInsCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 费用报销用户-往来单位映射配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-30 10:36:18
 */
@Mapper
public interface EcsUserCorrsInsCfgReadMapper extends BaseMapper<EcsUserCorrsInsCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsUserCorrsInsCfgVo> queryList(EcsUserCorrsInsCfgDto dto);
}
