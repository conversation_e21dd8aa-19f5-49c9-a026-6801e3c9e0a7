package com.jp.med.ecs.modules.drugMgt.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatDto;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsSatmatReadService;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsSatmatWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 卫材入库单
 * <AUTHOR>
 * @email -
 * @date 2024-07-26 15:06:37
 */
@Api(value = "卫材入库单", tags = "卫材入库单")
@RestController
@RequestMapping("ecsSatmat")
public class EcsSatmatController {

    @Autowired
    private EcsSatmatReadService ecsSatmatReadService;

    @Autowired
    private EcsSatmatWriteService ecsSatmatWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询卫材入库单")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsSatmatDto dto){
        return CommonResult.paging(ecsSatmatReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询卫材入库单")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsSatmatDto dto){
        return CommonResult.success(ecsSatmatReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增卫材入库单")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsSatmatDto dto){
        ecsSatmatWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改卫材入库单")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsSatmatDto dto){
        ecsSatmatWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除卫材入库单")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsSatmatDto dto){
        ecsSatmatWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 同步卫材入库单
     * @param dto
     * @return
     */
    @ApiOperation("同步卫材入库单")
    @PostMapping("/sanMatSync")
    public CommonResult<?> sanMatSync(@RequestBody EcsSatmatDto dto) {
        ecsSatmatWriteService.sanMatSync(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询月度数量")
    @PostMapping("/monthNum")
    public CommonResult<?> monthNum(@RequestBody EcsSatmatDto dto){
        return CommonResult.success(ecsSatmatReadService.monthNum(dto));
    }
}
