package com.jp.med.ecs.modules.reimMgt.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 工资汇总表-人员类型
 */
@Data
public class EcsSalarySummaryExcel {

    /**
     * 人数
     **/
    @ExcelProperty(index = 0)
    private Integer peoNum;

    /**
     * 性质
     **/
    @ExcelProperty(index = 1)
    private String EmpType;

    /**
     * 岗位工资
     **/
    @ExcelProperty(index = 2)
    private BigDecimal postSalary;

    /**
     * 护士10%
     **/
    @ExcelProperty(index = 3)
    private BigDecimal nurseSalary;

    /**
     * 薪级工资
     **/
    @ExcelProperty(index = 4)
    private BigDecimal salGradeSalary;

    /**
     * 基础绩效
     **/
    @ExcelProperty(index = 5)
    private BigDecimal basicPerf;

    /**
     * 护龄补贴
     **/
    @ExcelProperty(index = 6)
    private BigDecimal ageSalary;

    /** 驾驶员津贴 **/
//    @ExcelProperty(index = 7)
//    private BigDecimal driverSalary;

    /**
     * 通讯费补贴
     **/
    @ExcelProperty(index = 8)
    private BigDecimal communicationFeeAllowance;
    /**
     * 生活补贴
     **/
    @ExcelProperty(index = 9)
    private BigDecimal lifeSalary;

    /**
     * 地区附件津贴
     **/
    @ExcelProperty(index = 10)
    private BigDecimal areaSalary;

    /**
     * 人力临时增加
     **/
    @ExcelProperty(index = 11)
    private BigDecimal temporaryAddSalary;

    /**
     * 财务临时增加
     **/
    @ExcelProperty(index = 12)
    private BigDecimal temporaryAddSalary2;

    /**
     * 应发合计
     **/
//    @ExcelProperty(index = 11)
    private BigDecimal totalPayable;

    /**
     * 养老保险
     **/
    @ExcelProperty(index = 14)
    private BigDecimal pensionInsurance;

    /**
     * 医疗保险
     **/
    @ExcelProperty(index = 15)
    private BigDecimal medicalInsurance;

    /**
     * 失业保险
     **/
    @ExcelProperty(index = 16)
    private BigDecimal unemploymentInsurance;

    /**
     * 住房基金
     **/
    @ExcelProperty(index = 17)
    private BigDecimal housingFund;

    /**
     * 职业年金
     **/
    @ExcelProperty(index = 18)
    private BigDecimal occupationalAnnuity;

    /** 工会会费 **/
    private BigDecimal laborUnion;

    /**
     * 个人所得税
     **/
    @ExcelProperty(index = 19)
    private BigDecimal personTax;

    /**
     * 房租费
     **/
    @ExcelProperty(index = 20)
    private BigDecimal rent;

    /**
     * 水费
     **/
    @ExcelProperty(index = 21)
    private BigDecimal waterCharge;

    /**
     * 人力临时扣款
     **/
    @ExcelProperty(index = 22)
    private BigDecimal temporaryReduceSalary;

    /**
     * 财务临时扣款
     **/
    @ExcelProperty(index = 23)
    private BigDecimal temporaryReduceSalary2;

    /**
     * 扣款合计
     **/
    @ExcelProperty(index = 24)
    private BigDecimal reduceSalaryTotal;

    /**
     * 实发合计
     **/
    @ExcelProperty(index = 25)
    private BigDecimal totalPaid;
}
