package com.jp.med.ecs.modules.reimMgt.feign.mmis;

import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.ecs.modules.reimMgt.entity.EcsPurmsReimIdEntity;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RefreshScope
@FeignClient(name = "MmisFeignService", url = "${custom.gateway.med-mmis-service-uri}")
public interface MmisFeignService {
	/**
	 * 报销完成更新采购申请
	 * @param entity
	 * @return
	 */
	@PostMapping("/mmisAsetStorage/updateDetailsReimIdAndStatus")
	CommonFeignResult updatePurcTaskReimId(@RequestBody EcsPurmsReimIdEntity entity);

}
