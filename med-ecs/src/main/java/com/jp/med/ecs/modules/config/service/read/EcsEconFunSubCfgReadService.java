package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsEconFunSubCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsEconFunSubCfgVo;

import java.util.List;

/**
 * 经济和功能科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
public interface EcsEconFunSubCfgReadService extends IService<EcsEconFunSubCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsEconFunSubCfgVo> queryList(EcsEconFunSubCfgDto dto);
}

