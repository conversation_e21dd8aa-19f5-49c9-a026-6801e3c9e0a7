package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.config.dto.EcsCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsCorrsInsCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsCorrsInsCfgWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 往来单位配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Api(value = "往来单位配置", tags = "往来单位配置")
@RestController
@RequestMapping("ecsCorrsInsCfg")
public class EcsCorrsInsCfgController {

    @Autowired
    private EcsCorrsInsCfgReadService ecsCorrsInsCfgReadService;

    @Autowired
    private EcsCorrsInsCfgWriteService ecsCorrsInsCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询往来单位配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsCorrsInsCfgDto dto){
        return CommonResult.success(ecsCorrsInsCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增往来单位配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsCorrsInsCfgDto dto){
        ecsCorrsInsCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改往来单位配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsCorrsInsCfgDto dto){
        ecsCorrsInsCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除往来单位配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsCorrsInsCfgDto dto){
        ecsCorrsInsCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 同步
     */
    @ApiOperation("查询经济和功能科目配置")
    @PostMapping("/sync")
    public CommonResult<?> sync(@RequestBody EcsCorrsInsCfgDto dto){
        ecsCorrsInsCfgWriteService.sync(dto);
        return CommonResult.success();
    }
}
