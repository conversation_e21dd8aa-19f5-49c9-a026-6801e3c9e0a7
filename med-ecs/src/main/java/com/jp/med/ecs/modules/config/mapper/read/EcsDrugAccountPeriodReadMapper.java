package com.jp.med.ecs.modules.config.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsDrugAccountPeriodDto;
import com.jp.med.common.vo.EcsDrugAccountPeriodVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 药品账期
 * <AUTHOR>
 * @email -
 * @date 2025-02-21 10:36:30
 */
@Mapper
public interface EcsDrugAccountPeriodReadMapper extends BaseMapper<EcsDrugAccountPeriodDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsDrugAccountPeriodVo> queryList(EcsDrugAccountPeriodDto dto);
}
