package com.jp.med.ecs.modules.drugMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatDto;
import com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 卫材入库单
 * <AUTHOR>
 * @email -
 * @date 2024-07-26 15:06:37
 */
@Mapper
public interface EcsSatmatReadMapper extends BaseMapper<EcsSatmatDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsSatmatVo> queryList(EcsSatmatDto dto);

    List<EcsSatmatDto> listSatMats(@Param("floor")Integer floor,@Param("startRow")Integer startRow,@Param("endRow") Integer endRow);

    /**
     * 查询入库单起始单号
     * @return
     */
    Integer queryFloor();

    /**
     * 查询月份数量
     * @param dto
     * @return
     */
    List<Map<String, Integer>> monthNum(EcsSatmatDto dto);
}
