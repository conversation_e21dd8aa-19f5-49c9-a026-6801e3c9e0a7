package com.jp.med.ecs.modules.reimMgt.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.dto.bpm.BpmProcessInstanceCreateReqDTO;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.util.*;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.dto.SaveMultiTripDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimPsnDetail;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimTravelApprReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDetailWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimTravelApprWriteMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimTravelApprReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimTravelApprWriteService;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报销差旅审批
 *
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 13:48:41
 */
@Service
@Transactional(readOnly = false)
public class EcsReimTravelApprWriteServiceImpl extends ServiceImpl<EcsReimTravelApprWriteMapper, EcsReimTravelApprDto> implements EcsReimTravelApprWriteService {

    @Autowired
    private EcsReimTravelApprWriteMapper ecsReimTravelApprWriteMapper;

    @Autowired
    private EcsReimTravelApprReadMapper ecsReimTravelApprReadMapper;

    @Autowired
    private EcsReimTravelApprReadService ecsReimTravelApprReadService;

    @Autowired
    private EcsReimDetailWriteMapper ecsReimDetailWriteMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Autowired
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;

    @Override
//    @GlobalTransactional
    public void saveReimTravelAppr(EcsReimTravelApprDto dto) {
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        dto.setAppyer(appyer);
        dto.setAppyerTime(DateUtil.getCurrentTime(null));
        if (StringUtils.isNotEmpty(dto.getEvectionTime())) {
            String[] split = dto.getEvectionTime().split(",");
            if (split.length == 2) {
                dto.setEvectionBegnTime(split[0]);
                dto.setEvectionEndTime(split[1]);
            }
        }
        // 附件
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            List<List<String>> ossPaths = OSSUtil.getOSSPaths(dto.getAttFiles(), OSSConst.BUCKET_ECS, "reim/appy/");
            dto.setAtt(String.join(",", ossPaths.get(0)));
            dto.setAttName(String.join(",", ossPaths.get(1)));
        }
        String batchNum = AuditConst.ECS_CLSQ + ULIDUtil.generate();
        dto.setAuditBchno(batchNum);
        dto.setReimFlag(MedConst.TYPE_0);
        //设置status状态
        dto.setStatus(EcsConst.APPR_STATUS_AUDITING);
        //如果id不为空，则为重新提交的申请，先删再增
        if (!Objects.isNull(dto.getId())) {
            //删除申请
            ecsReimTravelApprWriteMapper.deleteById(dto.getId());
            //删除随行人员
            ecsReimDetailWriteMapper.deletePsnWithReimDetailId(dto.getId().intValue(), MedConst.TYPE_1);
        }
        ecsReimTravelApprWriteMapper.insert(dto);
        // 出差人
        if (CollectionUtil.isNotEmpty(dto.getPsnDetails())) {
            dto.getPsnDetails().forEach(item -> {
                item.setType(MedConst.TYPE_1);
                item.setReimDetailId(dto.getId());
            });
            BatchUtil.batch("insertPsnDetail", dto.getPsnDetails(), EcsReimDetailWriteMapper.class);
        }
        push(dto, hrmUser, batchNum, appyer, "出差申请");
    }

    @Override
//    @GlobalTransactional
    public void saveReimTravelApprNew(EcsReimTravelApprDto dto) {
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        dto.setAppyer(appyer);
        dto.setAppyerTime(DateUtil.getCurrentTime(null));
        if (StringUtils.isNotEmpty(dto.getEvectionTime())) {
            String[] split = dto.getEvectionTime().split(",");
            if (split.length == 2) {
                dto.setEvectionBegnTime(split[0]);
                dto.setEvectionEndTime(split[1]);
            }
        }
        // 附件
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            List<List<String>> ossPaths = OSSUtil.getOSSPaths(dto.getAttFiles(), OSSConst.BUCKET_ECS, "reim/appy/");
            dto.setAtt(String.join(",", ossPaths.get(0)));
            dto.setAttName(String.join(",", ossPaths.get(1)));
        }
        String batchNum = AuditConst.ECS_CLSQ + ULIDUtil.generate();
//        dto.setAuditBchno(batchNum);
        dto.setReimFlag(MedConst.TYPE_0);
        //设置status状态
        dto.setStatus(EcsConst.APPR_STATUS_AUDITING);
        //如果id不为空，则为重新提交的申请，先删再增
        if (!Objects.isNull(dto.getId())) {
            //删除申请
            ecsReimTravelApprWriteMapper.deleteById(dto.getId());
            //删除随行人员
            ecsReimDetailWriteMapper.deletePsnWithReimDetailId(dto.getId().intValue(), MedConst.TYPE_1);
        }
        ecsReimTravelApprWriteMapper.insert(dto);
        //-----------BPM流程测试-----------

        //发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        //放入参数 职能非职能  省内省外 自驾非自驾
        dto.getBpmParams().forEach((key, value) -> {
            processInstanceVariables.put(key, value);
        });
        //获取附件外链路径
        /*if (!Objects.isNull(dto.getAtt())) {
            List<String> attPaths = new ArrayList<>();
            List<String> attNames = new ArrayList<>();
            String[] att = dto.getAtt().split(",");
            String[] attName = dto.getAttName().split(",");
            for (int i = 0; i < att.length; i++) {
                attPaths.add(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_ECS,att[i]));
                attNames.add(attName[i]);
            }
            processInstanceVariables.put("attPaths",attPaths);
            processInstanceVariables.put("attNames",attNames);
        }*/

        String userCode = dto.getSysUser().getHrmUser().getEmpCode();
        String PROCESS_KEY ;
        if (StringUtils.equals(dto.getApprDeptType(),MedConst.TYPE_1)) {
            //职能申请
            PROCESS_KEY = "ECS_FUN_DEPT_TRAVEL_APPR";
        } else {
            //临床申请
            PROCESS_KEY = "ECS_KJ_SHORT_TRAIN_APPR";
        }

        processInstanceVariables.put(OSSConst.APP_ATT_PATHS, dto.getAtt());
        processInstanceVariables.put(OSSConst.APP_ATT_NAMES, dto.getAttName());
        processInstanceVariables.put(OSSConst.APP_ATT_BUCKET_NAME,OSSConst.BUCKET_ECS);
        try {
            val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
            bpmProcessInstanceCreateReqDTO
                    .setUserId(userCode)
                    .setProcessDefinitionKey(PROCESS_KEY)
                    .setVariables(processInstanceVariables)
                    .setBusinessKey(String.valueOf(dto.getId()));
//                    .setStartUserSelectAssignees(dto.getStartUserSelectAssignees());
            CommonFeignResult processInstance = bpmProcessInstanceFeignApi.createProcessInstance(bpmProcessInstanceCreateReqDTO);
            if (!StringUtils.equals(processInstance.get("code").toString(),"200")) {
                throw new AppException("生成BPM流程异常");
            }
            String processInstanceId = processInstance.get("data").toString();
            //将工作流编号，更新到申请中
            LambdaUpdateWrapper<EcsReimTravelApprDto> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(EcsReimTravelApprDto::getProcessInstanceId,processInstanceId)
                    .eq(EcsReimTravelApprDto::getId,dto.getId());
            ecsReimTravelApprWriteMapper.update(null,updateWrapper);
        } catch (Exception e) {
            log.error("BPM流程生成失败", e);
            throw new AppException("BPM流程生成失败");
        }

        //-----------BPM流程测试-----------
        // 出差人
        if (CollectionUtil.isNotEmpty(dto.getPsnDetails())) {
            dto.getPsnDetails().forEach(item -> {
                item.setType(MedConst.TYPE_1);
                item.setReimDetailId(dto.getId());
            });
            BatchUtil.batch("insertPsnDetail", dto.getPsnDetails(), EcsReimDetailWriteMapper.class);
        }
        push(dto, hrmUser, batchNum, appyer, "出差申请");
    }

    @Override
    public void saveReimTravelApprMultiTrip(SaveMultiTripDto dto) {

        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        String apprDeptType = MedConst.TYPE_1;



        // 统一的批次号
        String batchNum = AuditConst.ECS_CLSQ + ULIDUtil.generate();
        // 统一附件
        // 附件
        List<List<String>> ossPaths = null;
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            ossPaths = OSSUtil.getOSSPaths(dto.getAttFiles(), OSSConst.BUCKET_ECS, "reim/appy/");
        }

        List<EcsReimPsnDetail> psnDetails = new ArrayList<>();
        List<EcsReimTravelApprDto> ecsReimTravelApprDtoList = new ArrayList<>();
        // 1、构建对象
        // 1.1、获取参数
        String dataForms = dto.getDataForms();
        JSONArray dataFormsJson = JSON.parseArray(dataForms);
        for (int i = 0; i < dataFormsJson.size(); i++) {
            EcsReimTravelApprDto ecsReimTravelApprDto = new EcsReimTravelApprDto();
            ecsReimTravelApprDto.setAppyer(appyer);
            ecsReimTravelApprDto.setAppyerTime(DateUtil.getCurrentTime(null));
            ecsReimTravelApprDto.setHospitalId(dto.getHospitalId());

            JSONObject jsonObject = dataFormsJson.getJSONObject(i);
            JSONArray evectionTime = jsonObject.getJSONArray("evectionTime");
            if (ObjectUtil.isNotEmpty(evectionTime)) {
                if (evectionTime.size() == 2) {
                    ecsReimTravelApprDto.setEvectionBegnTime(evectionTime.getString(0));
                    ecsReimTravelApprDto.setEvectionEndTime(evectionTime.getString(1));
                }
            }

            // 附件
            if (CollectionUtil.isNotEmpty(ossPaths)) {
                ecsReimTravelApprDto.setAtt(String.join(",", ossPaths.get(0)));
                ecsReimTravelApprDto.setAttName(String.join(",", ossPaths.get(1)));
            }


            ecsReimTravelApprDto.setReimFlag(MedConst.TYPE_0);
            //设置status状态
            ecsReimTravelApprDto.setStatus(EcsConst.APPR_STATUS_AUDITING);

            ecsReimTravelApprDto.setType(jsonObject.getString("type"));
            ecsReimTravelApprDto.setApprDept(jsonObject.getString("apprDept"));
            ecsReimTravelApprDto.setApprDeptType(jsonObject.getString("apprDeptType"));
            ecsReimTravelApprDto.setBusMet(jsonObject.getString("busMet"));
            ecsReimTravelApprDto.setPlanAmt(jsonObject.getBigDecimal("planAmt"));
            ecsReimTravelApprDto.setPlanAmt2(jsonObject.getBigDecimal("planAmt2"));
            ecsReimTravelApprDto.setTrnp(jsonObject.getJSONArray("trnp").stream().map(Object::toString).collect(Collectors.joining(",")));
            ecsReimTravelApprDto.setTrnpNum(jsonObject.getString("trnpNum"));
            ecsReimTravelApprDto.setEvectionAddrName(jsonObject.getString("evectionAddrName"));
            ecsReimTravelApprDto.setPrse(jsonObject.getString("prse"));
            ecsReimTravelApprDto.setEvectionAddr(jsonObject.getInteger("evectionAddr"));
            ecsReimTravelApprDto.setTravelRange(jsonObject.getString("travelRange"));
            ecsReimTravelApprDto.setEvectionDetlAddr(jsonObject.getString("evectionDetlAddr"));
            ecsReimTravelApprDto.setDetourOrNot(jsonObject.getString("detourOrNot"));
            ecsReimTravelApprDto.setFood(jsonObject.getString("food"));
            ecsReimTravelApprDto.setStay(jsonObject.getString("stay"));
            ecsReimTravelApprDto.setSelfDriveRea(jsonObject.getString("selfDriveRea"));
            ecsReimTravelApprDto.setEvectionRea(jsonObject.getString("evectionRea"));

            //如果id不为空，则为重新提交的申请，先删再增
            Integer id = jsonObject.getInteger("id");
            if (!Objects.isNull(id)) {
                //删除申请
                ecsReimTravelApprWriteMapper.deleteById(id);
                //删除随行人员
                ecsReimDetailWriteMapper.deletePsnWithReimDetailId(id, MedConst.TYPE_1);
            }

            ecsReimTravelApprWriteMapper.insert(ecsReimTravelApprDto);
            ecsReimTravelApprDtoList.add(ecsReimTravelApprDto);
            apprDeptType = jsonObject.getString("apprDeptType");
            List<EcsReimPsnDetail> ecsReimPsnDetails = jsonObject.getObject("psnDetails", new TypeReference<List<EcsReimPsnDetail>>() {
            });
            ecsReimPsnDetails.forEach(ecsReimPsnDetail -> {
                ecsReimPsnDetail.setType(MedConst.TYPE_1);
                ecsReimPsnDetail.setReimDetailId(ecsReimTravelApprDto.getId());
            });
            psnDetails.addAll(ecsReimPsnDetails);
        }

        //-----------BPM流程测试-----------

        //发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        //放入参数 职能非职能  省内省外 自驾非自驾
        dto.getBpmParams().forEach((key, value) -> {
            processInstanceVariables.put(key, value);
        });
        //获取附件外链路径

        String userCode = dto.getSysUser().getHrmUser().getEmpCode();
        String PROCESS_KEY ;
        if (StringUtils.equals(apprDeptType,MedConst.TYPE_1)) {
            //职能申请
            PROCESS_KEY = "ECS_DUTY_TRIP_APPR_TRIP";
        } else {
            //临床申请
            PROCESS_KEY = "ECS_CLINICAL_TRIP_APPR_TRIP";
        }

        processInstanceVariables.put(OSSConst.APP_ATT_PATHS, CollectionUtil.isNotEmpty(ossPaths) ? ossPaths.get(0) : "");
        processInstanceVariables.put(OSSConst.APP_ATT_NAMES, CollectionUtil.isNotEmpty(ossPaths) ? ossPaths.get(1) : "");
        processInstanceVariables.put(OSSConst.APP_ATT_BUCKET_NAME,OSSConst.BUCKET_ECS);
        try {
            val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
            bpmProcessInstanceCreateReqDTO
                    .setUserId(userCode)
                    .setProcessDefinitionKey(PROCESS_KEY)
                    .setVariables(processInstanceVariables)
                    .setBusinessKey(ecsReimTravelApprDtoList.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.joining(",")));

            CommonFeignResult processInstance = bpmProcessInstanceFeignApi.createProcessInstance(bpmProcessInstanceCreateReqDTO);
            if (!StringUtils.equals(processInstance.get("code").toString(),"200")) {
                throw new AppException("生成BPM流程异常");
            }
            String processInstanceId = processInstance.get("data").toString();
            //将工作流编号，更新到申请中
            LambdaUpdateWrapper<EcsReimTravelApprDto> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(EcsReimTravelApprDto::getProcessInstanceId,processInstanceId)
                    .in(EcsReimTravelApprDto::getId,ecsReimTravelApprDtoList.stream().map(EcsReimTravelApprDto::getId).collect(Collectors.toList()));
            ecsReimTravelApprWriteMapper.update(null,updateWrapper);
        } catch (Exception e) {
            log.error("BPM流程生成失败", e);
            throw new AppException("BPM流程生成失败");
        }

        //-----------BPM流程测试-----------
        // 出差人
        if (CollectionUtil.isNotEmpty(psnDetails)) {

            BatchUtil.batch("insertPsnDetail", psnDetails, EcsReimDetailWriteMapper.class);
        }
        ecsReimTravelApprDtoList.forEach(ecsReimTravelApprDto -> push(ecsReimTravelApprDto, hrmUser, batchNum, appyer, "出差申请"));
    }

    @Override
    public void updatePageImage(EcsReimTravelApprDto dto) {
        //页面图片
        if (dto.getPageImageFile() != null) {
            String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/appy/", dto.getPageImageFile());
            dto.setPageImage(filePath);
        }
        ecsReimTravelApprWriteMapper.updateById(dto);
    }

    /**
     * 更新公里数
     *
     * @param dto
     */
    @Override
    public void updateKil(EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteMapper.updateById(dto);
    }

    /**
     * 流程未进行审核，可删除差旅盛情
     *
     * @param dto
     */
    @Override
    public void deleteNoAudit(EcsReimTravelApprDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        //通过审核批次号查询当前是否已经进行过审核
        AuditDetail stepOne = ecsReimTravelApprReadMapper.getFirstAudit(dto);
        if (!StringUtils.equals(stepOne.getChkState(), MedConst.TYPE_0) || StringUtils.isNotEmpty(stepOne.getChkTime())) {
            throw new AppException("审核流程已进行过审核，不能删除");
        }
        ecsReimTravelApprWriteMapper.deleteById(dto.getId());
    }

    @Override
    public void deleteNoAuditNew(EcsReimTravelApprDto dto) {
        //删除申请，前端已判断过，直接删除
        ecsReimTravelApprWriteMapper.deleteById(dto.getId());
        //删除对应流程实例
        Map<String,Object> variables = new HashMap<>();
        variables.put("isRunning",StringUtils.equals(dto.getStatus(),EcsConst.APPR_STATUS_AUDIT_FAILD)?MedConst.TYPE_0:MedConst.TYPE_1);
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                .setVariables(variables)
                .setProcessId(dto.getProcessInstanceId());
        CommonFeignResult processIns = bpmProcessInstanceFeignApi.deleteRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
        if (!StringUtils.equals(processIns.get("code").toString(),"200")) {
            throw new AppException("删除BPM流程异常");
        }
    }

    @Override
    public void deleteNoAuditMultiTrip(EcsReimTravelApprDto dto) {
        //删除申请，前端已判断过，直接删除
        ecsReimTravelApprWriteMapper.delete(
                Wrappers.lambdaUpdate(EcsReimTravelApprDto.class)
                        .eq(EcsReimTravelApprDto::getProcessInstanceId,dto.getProcessInstanceId())
        );
        //删除对应流程实例
        Map<String,Object> variables = new HashMap<>();
        variables.put("isRunning",StringUtils.equals(dto.getStatus(),EcsConst.APPR_STATUS_AUDIT_FAILD)?MedConst.TYPE_0:MedConst.TYPE_1);
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                .setVariables(variables)
                .setProcessId(dto.getProcessInstanceId());
        CommonFeignResult processIns = bpmProcessInstanceFeignApi.deleteRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
        if (!StringUtils.equals(processIns.get("code").toString(),"200")) {
            throw new AppException("删除BPM流程异常");
        }
    }

    public void push(EcsReimTravelApprDto dto, HrmUser hrmUser, String batchNum, String appyer, String title) {
        // 审核流程
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            AppMsgSup appMsgSup = new AppMsgSup();
            appMsgSup.setTitle(title);
            String appyerName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName() : dto.getSysUser().getNickname();
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);
            appMsgSup.setContent("出差地点是[" + dto.getEvectionAddrName() + "]，" +
                    "出差事由是[" + dto.getEvectionRea() + "]，" +
                    "预计出差时间[" + dto.getEvectionBegnTime() + " 至 " + dto.getEvectionEndTime() + "]，" +
                    "预计出差费用[" + dto.getPlanAmt() + "元]");
            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(batchNum);
            auditPayload.setAdditionItems(JSON.toJSONString(List.of(new HashMap<>() {
                {
                    put("title", "出差地点");
                    put("value", dto.getEvectionAddrName());
                }
            })));
            auditPayload.setTableTitle("出差人");
            auditPayload.setTableHeader("科室,出差人");
            auditPayload.setTableContent("dept,tripPsn");
            auditPayload.setDetailUrl("/ecs/ecsReimTravelAppr/appAuditDetail");
            Map<String, Object> map = new HashMap<>();
            map.put("evectionTime", "出差时间");
            map.put("evectionRea", "出差事由");
            auditPayload.setDisplayItem(map);
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(new AuditDetail(batchNum, dto.getAuditDetails(), appMsgSup, auditPayload, OSSConst.BUCKET_ECS)));
        }
    }

    @Override
    public void apprAuditTakeBack(EcsReimTravelApprDto dto) {
        //获取审核流程
        HrmUser user = dto.getSysUser().getHrmUser();
        if (StringUtils.isEmpty(dto.getAuditBchno())) {
            throw new AppException("审核批次号不能为空");
        }
        dto.setSqlAutowiredHospitalCondition(true);
        //申请审核的下一审核人已审核后则不能收回
        List<AuditDetail> auditDetails = ecsReimTravelApprReadMapper.queryNextAuditedNode(dto.getAuditBchno(), dto.getSysUser().getHrmUser().getEmpCode());
        if (auditDetails.size() > 1) {
            throw new AppException("下一审核人已审核，无法收回");
        }
        //当前申请已提交报销后，不能收回
        Integer reimNum = ecsReimTravelApprReadMapper.queryReimWithAppr(dto);
        if (reimNum > 0) {
            throw new AppException("当前申请已生成报销记录，无法收回");
        }

        //重置当前审核节点信息
        Integer curId = auditDetails.get(0).getId().intValue();
        ecsReimTravelApprWriteMapper.resetTravelApprAuditNode(curId);
        //重置审核状态
        ecsReimTravelApprWriteMapper.resetTravelApprAuditRes(dto.getAuditBchno());
    }

    @Override
    public void apprAuditSendBack(EcsReimTravelApprDto dto) {

    }

    @Override
    public void cancelEcsReimTravelApprNew(EcsReimTravelApprDto dto) {
        //更新申请状态为取消
        LambdaUpdateWrapper<EcsReimTravelApprDto> apprWrapper = Wrappers.lambdaUpdate();
        apprWrapper.eq(EcsReimTravelApprDto::getId,dto.getId())
                .set(EcsReimTravelApprDto::getStatus, EcsConst.APPR_STATUS_AUDIT_CANCEL);
        ecsReimTravelApprWriteMapper.update(null,apprWrapper);
        //取消对应流程实例
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                .setProcessId(dto.getProcessInstanceId());
        CommonFeignResult processIns = bpmProcessInstanceFeignApi.cancelRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
        if (!StringUtils.equals(processIns.get("code").toString(),"200")) {
            throw new AppException("取消流程实例异常");
        }
    }

    @Override
    public void cancelEcsReimTravelApprMultiTrip(EcsReimTravelApprDto dto) {
        //更新申请状态为取消
        LambdaUpdateWrapper<EcsReimTravelApprDto> apprWrapper = Wrappers.lambdaUpdate();
        apprWrapper.eq(EcsReimTravelApprDto::getProcessInstanceId,dto.getProcessInstanceId())
                .set(EcsReimTravelApprDto::getStatus, EcsConst.APPR_STATUS_AUDIT_CANCEL);
        ecsReimTravelApprWriteMapper.update(null,apprWrapper);
        //取消对应流程实例
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                .setProcessId(dto.getProcessInstanceId());
        CommonFeignResult processIns = bpmProcessInstanceFeignApi.cancelRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
        if (!StringUtils.equals(processIns.get("code").toString(),"200")) {
            throw new AppException("取消流程实例异常");
        }
    }
}
