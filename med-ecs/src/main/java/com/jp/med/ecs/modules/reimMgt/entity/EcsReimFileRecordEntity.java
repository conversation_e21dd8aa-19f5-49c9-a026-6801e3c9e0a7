package com.jp.med.ecs.modules.reimMgt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 费用报销附件表
 */
@Data
@TableName("ecs_reim_file_record")
public class EcsReimFileRecordEntity {

    /** id **/
    @TableField("id")
    private Integer id;

    /** 附件(minio路径) **/
    @TableField("att")
    private String att;

    /** 附件名称 **/
    @TableField("att_name")
    private String attName;

    /** 业务类型 **/
    @TableField("type")
    private String type;

    /** 对应业务关联主键 模块前缀+随机数 **/
    @TableField("att_code")
    private String attCode;

    /** 删除标志 0:未删除，1：已删除 **/
    @TableField("flag")
    private String flag;
}
