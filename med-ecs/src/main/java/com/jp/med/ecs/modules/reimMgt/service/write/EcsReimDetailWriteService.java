package com.jp.med.ecs.modules.reimMgt.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.dto.SaveMultiTripDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimItemDetail;
import com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 报销明细
 * <AUTHOR>
 * @email -
 * @date 2023-12-04 22:01:14
 */
public interface EcsReimDetailWriteService extends IService<EcsReimDetailDto> {

    /**
     * 保存报销
     * @param dto
     */
    void saveReimDetail(EcsReimDetailDto dto);

    /**
     * 保存报销
     * @param dto
     */
    void saveReimDetailNew(EcsReimDetailDto dto);

    /**
     * OCR识别
     * @param dto
     * @return
     */
    List<EcsInvoRcdVo> ocrIdentify(EcsReimDetailDto dto);

    List<String> ocrIdentifyNew(EcsReimDetailDto dto);

    /**
     * 更改报销财务科目
     * @param dto
     */
    void modifyReimAsst(EcsReimDetailDto dto);

    /**
     * 删除未开始审核报销
     * @param dto
     */
    void deleteNoAudit(EcsReimDetailDto dto);

    void deleteNoAuditNew(EcsReimDetailDto dto);

    /**
     * 上传付款证明文件
     * @param dto
     */
    void uploadPayFiles(EcsReimDetailDto dto);

    /**
     * 更新资金类型
     * @param dto
     */
    void updateFundType(EcsReimDetailDto dto);

    /**
     * 更新差旅、培训项目(补助项目)信息
     * @param dto
     */
    void updateProItems(EcsReimDetailDto dto);

    void cancelEcsReimDetailNew(EcsReimDetailDto dto);

    Map<String,String> generateShareResExcel(EcsReimDetailDto dto);

    void updateContractItems(EcsReimDetailDto dto);

    void saveReimDetailMultiTrip(SaveMultiTripDto dto);

    void deleteNoAuditMultiTrip(EcsReimDetailDto dto);

    void cancelEcsReimDetailMultiTrip(EcsReimDetailDto dto);

    void purcUploadReceipt(EcsReimDetailDto dto);

    List<EcsReimItemDetail> uploadReimDetailFile(MultipartFile file);

    List<EcsReimDetailVo> queryLoanReim(EcsReimDetailDto dto);
}

