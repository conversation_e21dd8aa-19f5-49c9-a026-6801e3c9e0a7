package com.jp.med.ecs.modules.config.utils;

import com.jp.med.common.exception.AppException;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class AccountPeriodUtil {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    /**
     * 获取指定年月字符串的下一个月的年月字符串
     * @param input 输入的年月字符串，预期格式为 yyyy-MM
     * @return 下一个月的年月字符串，格式为 yyyy-MM
     * @throws IllegalArgumentException 如果输入的字符串格式不正确
     */
    public static String getNextMonth(String input) {
        YearMonth yearMonth = parseInput(input);
        YearMonth nextYearMonth = yearMonth.plusMonths(1);
        return nextYearMonth.format(FORMATTER);
    }

    /**
     * 获取指定年月字符串的上一个月的年月字符串
     * @param input 输入的年月字符串，预期格式为 yyyy-MM
     * @return 上一个月的年月字符串，格式为 yyyy-MM
     * @throws IllegalArgumentException 如果输入的字符串格式不正确
     */
    public static String getPreviousMonth(String input) {
        YearMonth yearMonth = parseInput(input);
        YearMonth previousYearMonth = yearMonth.minusMonths(1);
        return previousYearMonth.format(FORMATTER);
    }

    /**
     * 解析输入的年月字符串为 YearMonth 对象
     * @param input 输入的年月字符串，预期格式为 yyyy-MM
     * @return 解析后的 YearMonth 对象
     * @throws IllegalArgumentException 如果输入的字符串格式不正确
     */
    private static YearMonth parseInput(String input) {
        if (input == null || input.trim().isEmpty()) {
            throw new AppException("输入的年月字符串不能为空");
        }
        try {
            return YearMonth.parse(input, FORMATTER);
        } catch (DateTimeParseException e) {
            throw new AppException("输入的年月字符串格式不正确，预期格式为 yyyy-MM", e);
        }
    }
}
