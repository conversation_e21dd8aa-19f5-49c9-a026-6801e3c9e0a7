package com.jp.med.ecs.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 现金流量配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-08 14:03:00
 */
@Data
public class EcsActigAsstCashCfgVo implements BaseTree<String, EcsActigAsstCashCfgVo> {

	/** id */
	private Integer id;

	/** 科目代码 */
	private String subCode;

	/** 科目名称 */
	private String subName;

	/** 流向性质 */
	private String flowDire;

	/** 拼音助记码 */
	private String pinyin;

	/** 备注 */
	private String remarks;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改时间 */
	private String modiTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 启用标志 */
	private String activeFlag;

	/** 父级编码 */
	private String parentSubCode;

	/** 子集 */
	List<EcsActigAsstCashCfgVo> children;

	/**  */
	private String value;

	/**  */
	private String label;

	@Override
	public String getCode() {
		return this.subCode;
	}

	@Override
	public void setCode(String code) {
		this.subCode = code;
	}

	@Override
	public String getPid() {
		return this.parentSubCode;
	}

	@Override
	public void setPid(String pid) {
		this.parentSubCode = pid;
	}

	@Override
	public void addChild(EcsActigAsstCashCfgVo node) {
		if (Objects.isNull(this.children)){
			this.children = new ArrayList<>();
		}
		this.children.add(node);
	}
}
