package com.jp.med.ecs.modules.drugMgt.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatReimDetailDto;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsSatmatReimDetailReadMapper;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsSatmatReimDetailReadService;
import com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatReimDetailVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class EcsSatmatReimDetailReadServiceImpl extends ServiceImpl<EcsSatmatReimDetailReadMapper, EcsSatmatReimDetailDto> implements EcsSatmatReimDetailReadService {

    @Autowired
    private EcsSatmatReimDetailReadMapper ecsSatmatReimDetailReadMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Override
    public List<EcsSatmatReimDetailVo> queryList(EcsSatmatReimDetailDto dto) {
        return ecsSatmatReimDetailReadMapper.queryList(dto);
    }

    @Override
    public List<EcsSatmatReimDetailVo> queryPageList(EcsSatmatReimDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsSatmatReimDetailReadMapper.queryList(dto);
    }

    @Override
    public EcsSatmatReimDetailVo queryAppAuditDetail(EcsSatmatReimDetailDto dto) {
        // 查询审核流程
        dto.setSqlAutowiredHospitalCondition(true);
        List<AuditDetail> details = auditFeignService.getAuditDetails(new AuditDetail(dto.getAuditBchno(), dto.getSysUser().getHrmUser().getEmpCode(), OSSConst.BUCKET_ECS));

        List<AuditDetail> childrenDetails = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getBatchAudit()) && MedConst.TYPE_1.equals(dto.getBatchAudit())) {
            AuditDetail auditDetail = new AuditDetail();
            auditDetail.setAuditBchno(dto.getAuditBchno());
            auditDetail.setSys(OSSConst.BUCKET_ECS);
            childrenDetails = auditFeignService.queryChildren(auditDetail);
        }
        EcsSatmatReimDetailVo satmatReimDetailVo = new EcsSatmatReimDetailVo();
        details.sort(Comparator.comparing(AuditDetail::getChkSeq));
        satmatReimDetailVo.setAuditDetails(details);

        Map<String, AuditDetail> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(childrenDetails)) {
            // 获取所有未审核记录
            Map<String, List<AuditDetail>> collect = childrenDetails.stream().collect(Collectors.groupingBy(AuditDetail::getBchno));
            collect.forEach((s, auditDetails) -> {
                auditDetails.sort(Comparator.comparing(AuditDetail::getChkSeq));
                for (AuditDetail auditDetail : auditDetails) {
                    if (StringUtils.isEmpty(auditDetail.getChkTime()) && MedConst.TYPE_0.equals(auditDetail.getChkState())) {
                        map.put(s, auditDetail);
                        break;
                    }
                }
            });
        }

        dto.setChildrenDetails(childrenDetails);
        // 查询所有子审核对应的审核数据，且审核通过或待审核数据，不查询审核不通过数据
        List<EcsSatmatReimDetailVo> ecsSatmatReimDetailVos = ecsSatmatReimDetailReadMapper.queryAuditData(dto);
        ecsSatmatReimDetailVos.forEach(ecsSatmatReimDetailVo -> {
            AuditDetail auditDetail = map.get(ecsSatmatReimDetailVo.getAuditBchno());
            if (auditDetail != null) {
                ecsSatmatReimDetailVo.setId(auditDetail.getId().intValue());
            }
        });
        // 设置
        satmatReimDetailVo.setTableDetails(ecsSatmatReimDetailVos);
        return satmatReimDetailVo;
    }

    @Override
    public List<EcsSatmatReimDetailVo> queryAuditData(EcsSatmatReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
        return ecsSatmatReimDetailReadMapper.queryAuditCollData(dto);
    }
}
