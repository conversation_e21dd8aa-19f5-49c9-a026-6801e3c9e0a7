package com.jp.med.ecs.modules.drugMgt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 卫材入库单
 * <AUTHOR>
 * @email -
 * @date 2024-07-26 15:06:37
 */
@Data
@TableName("ecs_satmat")
public class EcsSatmatEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 单号 */
	@TableField("stoin_num")
	private Integer stoinNum;

	/** 入库时间 */
	@TableField("stoin_date")
	private String stoinDate;

	/** 卫材编码 */
	@TableField("satmat_code")
	private String satmatCode;

	/** 卫材名称 */
	@TableField("satmat_name")
	private String satmatName;

	/** 规格 */
	@TableField("specs")
	private String specs;

	/** 型号 */
	@TableField("pattern")
	private String pattern;

	/** 单位 */
	@TableField("unit")
	private String unit;

	/** 批号 */
	@TableField("batch_num")
	private String batchNum;

	/** 生产日期 */
	@TableField("prod_date")
	private String prodDate;

	/** 有效期 */
	@TableField("valid_date")
	private String validDate;

	/** 卫材数量 */
	@TableField("satmat_num")
	private Double satmatNum;

	/** 单价 */
	@TableField("unit_price")
	private BigDecimal unitPrice;

	/** 总金额 */
	@TableField("sumamt")
	private BigDecimal sumamt;

	/** 厂家 */
	@TableField("factory")
	private String factory;

	/** 供应商 */
	@TableField("spler")
	private String spler;

	/** 货主 */
	@TableField("consignor")
	private String consignor;

}
