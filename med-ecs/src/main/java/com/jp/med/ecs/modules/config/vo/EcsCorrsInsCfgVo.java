package com.jp.med.ecs.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.util.List;

/**
 * 往来单位配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Data
public class EcsCorrsInsCfgVo {

	/** id */
	private Integer id;

	/** 单位代码 */
	private String insCode;

	/** 单位名称 */
	private String insName;

	/** 单位全称 */
	private String fulname;

	/** 单位类型 */
	private String insType;

	/** 通信地址 */
	private String commAddr;

	/** 邮政编码 */
	private String poscode;

	/** 联系人姓名 */
	private String conerName;

	/** 联系人电话 */
	private String conerTel;

	/** 传真 */
	private String fax;

	/** 邮箱 */
	private String email;

	/** 单位属性 */
	private String insNatu;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改时间 */
	private String modiTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 启用标志 */
	private String activeFlag;

	/**  */
	private String value;

	/**  */
	private String label;

}
