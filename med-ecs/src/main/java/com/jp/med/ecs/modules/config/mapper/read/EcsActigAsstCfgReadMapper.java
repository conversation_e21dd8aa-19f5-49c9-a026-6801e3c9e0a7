package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.ecs.modules.config.dto.EcsActigAsstCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 会计科目辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 15:45:43
 */
@Mapper
public interface EcsActigAsstCfgReadMapper extends BaseMapper<EcsActigAsstCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsActigAsstCfgVo> queryList(EcsActigAsstCfgDto dto);
}
