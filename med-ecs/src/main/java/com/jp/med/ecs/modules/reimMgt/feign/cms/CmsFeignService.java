package com.jp.med.ecs.modules.reimMgt.feign.cms;

import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RefreshScope
@FeignClient(name = "CmsFeignService", url = "${custom.gateway.med-cms-service-uri}")
public interface CmsFeignService {
	/**
	 * 报销完成写入合同付款计划表并更新报销详细信息
	 *
	 * @param paymentId       付款条件ID
	 * @param reimId          报销ID
	 * @param reimbursePerson 报销人
	 * @param reimburseDept   报销部门
	 * @param reimburseTime   报销时间
	 * @param reimburseNo     报销单号
	 * @return
	 */
	@PostMapping("/cmsPaymentTerms/updatePaymentTermsReimId")
	CommonFeignResult updatePaymentTermsReimId(
			@RequestParam("id") Integer paymentId,
			@RequestParam("reimId") Integer reimId,
			@RequestParam(value = "reimbursePerson", required = false) String reimbursePerson,
			@RequestParam(value = "reimburseDept", required = false) String reimburseDept,
			@RequestParam(value = "reimburseTime", required = false) String reimburseTime,
			@RequestParam(value = "reimburseNo", required = false) String reimburseNo);
}
