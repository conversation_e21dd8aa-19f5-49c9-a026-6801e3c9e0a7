package com.jp.med.ecs.modules.drugMgt.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("ecs_stoin_detail" )
public class EcsStoinDetailDto extends CommonQueryDto {

    @TableField("id")
    private Integer id;

    /** 入库单序号 **/
    @TableField("zd_xh")
    private Integer zdXh;

    /** 药品名称 **/
    @TableField("drug_name")
    private String drugName;

    /** 批号 **/
    @TableField("batch_num")
    private String batchNum;

    /** 单位 **/
    @TableField("unit")
    private String unit;

    /** 药品数量 **/
    @TableField("drug_num")
    private Double drugNum;

    /** 购入价（单价） **/
    @TableField("purc_price")
    private BigDecimal purcPrice;

    /** 购入金额 **/
    @TableField("purcpric_amt")
    private BigDecimal purcpricAmt;

    /** 零售价 **/
    @TableField("rtal_price")
    private BigDecimal rtalPrice;

    /** 零售金额 **/
    @TableField("rtalpric_amt")
    private BigDecimal rtalpricAmt;

    /** 是否退货 **/
    @TableField("is_back")
    private String isBack;
}
