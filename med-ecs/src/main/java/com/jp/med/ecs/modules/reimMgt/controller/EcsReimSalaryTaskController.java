package com.jp.med.ecs.modules.reimMgt.controller;

import com.jp.med.common.dto.ecs.EcsReimSalaryTask;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimSalaryTaskReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimSalaryTaskWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Api(value = "应发工资报销任务", tags = "应发工资报销任务")
@RestController
@RequestMapping("ecsReimSalaryTask")
public class EcsReimSalaryTaskController {

    @Autowired
    private EcsReimSalaryTaskReadService ecsReimSalaryTaskReadService;

    @Autowired
    private EcsReimSalaryTaskWriteService ecsReimSalaryTaskWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询应发工资报销任务")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsReimSalaryTask dto){
        return CommonResult.paging(ecsReimSalaryTaskReadService.queryPageList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("分页查询应发工资报销任务（新）")
    @PostMapping("/pageListNew")
    public CommonResult<?> pageListNew(@RequestBody EcsReimSalaryTask dto){
        return CommonResult.paging(ecsReimSalaryTaskReadService.queryPageListNew(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询应发工资报销任务")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimSalaryTask dto){
        return CommonResult.success(ecsReimSalaryTaskReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增应发工资报销任务")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsReimSalaryTask dto){
        ecsReimSalaryTaskWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改应发工资报销任务")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimSalaryTask dto){
        ecsReimSalaryTaskWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除应发工资报销任务")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimSalaryTask dto){
        ecsReimSalaryTaskWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 保存工资任务
     * @param dto
     * @return
     */
    @ApiOperation("保存工资任务")
    @PostMapping("/saveSalaryTask")
    public CommonFeignResult saveSalaryTask(@RequestBody EcsReimSalaryTask dto) {
        ecsReimSalaryTaskWriteService.saveSalaryTask(dto);
        return CommonFeignResult.build();
    }

    /**
     * 查询工资任务明细
     * @param dto
     * @return
     */
    @ApiOperation("查询工资任务明细")
    @PostMapping("/querySalaryTaskDetail")
    public CommonResult<?> querySalaryTaskDetail(@RequestBody EcsReimSalaryTask dto) {
        return CommonResult.success(ecsReimSalaryTaskReadService.querySalaryTaskDetail(dto));
    }

    /**
     * 生成工资金额汇总表
     * @param dto
     * @return
     */
    @ApiOperation("生成费用报销审批表")
    @PostMapping("/getSalarySummaryExcel")
    public CommonResult<?> getSalarySummaryExcel(@RequestBody EcsReimSalaryTask dto) {
        return CommonResult.success(ecsReimSalaryTaskReadService.getSalarySummaryExcel(dto));
    }
}
