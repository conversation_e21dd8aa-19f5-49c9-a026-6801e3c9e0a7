package com.jp.med.ecs.modules.reimMgt.controller;


import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.dto.SaveMultiTripDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimItemDetail;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimDetailReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimDetailWriteService;
import com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;


/**
 * 报销明细
 * <AUTHOR>
 * @email -
 * @date 2023-12-04 22:01:14
 */
@Api(value = "报销明细", tags = "报销明细")
@RestController
@RequestMapping("ecsReimDetail")
public class EcsReimDetailController {

    @Autowired
    private EcsReimDetailReadService ecsReimDetailReadService;

    @Autowired
    private EcsReimDetailWriteService ecsReimDetailWriteService;


    @InitBinder
    protected void initBinder(WebDataBinder binder) {
        binder.setAutoGrowNestedPaths(true);
        binder.setAutoGrowCollectionLimit(Integer.MAX_VALUE);
    }

    /**
     * 列表
     */
    @ApiOperation("查询报销明细")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimDetailDto dto){
        return CommonResult.paging(ecsReimDetailReadService.queryList(dto));
    }
    @ApiOperation("查询报销明细")
    @PostMapping("/listNoPage")
    public CommonResult<?> listNoPage(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.queryNoPageList(dto));
    }

    @ApiOperation("查询报销明细（新）")
    @PostMapping("/listNoPageNew")
    public CommonResult<?> listNoPageNew(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.queryNoPageListNew(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询报销明细")
    @PostMapping("/listNew")
    public CommonResult<?> listNew(@RequestBody EcsReimDetailDto dto){
        return CommonResult.paging(ecsReimDetailReadService.queryListNew(dto));
    }

    @ApiOperation("查询项目详情")
    @PostMapping("/itemDetail")
    public CommonResult<?> itemDetail(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.queryItemDetail(dto));
    }

    /**
     * 只查询itemDetails
     * @param dto
     * @return
     */
    @ApiOperation("查询项目详情")
    @PostMapping("/itemDetail2")
    public CommonResult<?> itemDetail2(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.queryItemDetail2(dto));
    }

    @ApiOperation("查询报销人员详情")
    @PostMapping("/psnDetails")
    public CommonResult<?> psnDetails(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.psnDetails(dto));
    }

    @ApiOperation("查询报销人员详情")
    @PostMapping("/psnDetailsMultiTrip")
    public CommonResult<?> psnDetailsMultiTrip(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.psnDetailsMultiTrip(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增报销明细")
    @PostMapping("/saveMultiTrip")
    public CommonResult<?> saveMultiTrip(SaveMultiTripDto dto){
        ecsReimDetailWriteService.saveReimDetailMultiTrip(dto);
        return CommonResult.success();
    }

    /**
     * 列表
     */
    @ApiOperation("查询报销明细")
    @PostMapping("/listMultiTrip")
    public CommonResult<?> listMultiTrip(@RequestBody EcsReimDetailDto dto){
        return CommonResult.paging(ecsReimDetailReadService.queryListMultiTrip(dto));
    }

    /**
     * 删除未开始审核报销
     * @param dto
     * @return
     */
    @ApiOperation("删除未开始审核报销")
    @DeleteMapping("/deleteNoAuditMultiTrip")
    public CommonResult<?> deleteNoAuditMultiTrip(@RequestBody EcsReimDetailDto dto) {
        ecsReimDetailWriteService.deleteNoAuditMultiTrip(dto);
        return CommonResult.success();
    }

    @ApiOperation("取消未审核过的差旅申请")
    @DeleteMapping("/cancelEcsReimDetailMultiTrip")
    public CommonResult<?> cancelEcsReimDetailMultiTrip(@RequestBody EcsReimDetailDto dto) {
        ecsReimDetailWriteService.cancelEcsReimDetailMultiTrip(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询科室已经报销金额")
    @PostMapping("/queryDeptAmt")
    public CommonResult<?> queryDeptAmt(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.queryDeptAmt(dto));
    }

    @ApiOperation("查询科室租车费已经报销金额")
    @PostMapping("/queryZCFDeptAmt")
    public CommonResult<?> queryZCFDeptAmt(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.queryZCFDeptAmt(dto));
    }

    @ApiOperation("查询APP审核详情")
    @PostMapping("/appAuditDetail")
    public CommonResult<?> queryAppAuditDetail(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.queryAppAuditDetail(dto));
    }

    @ApiOperation("查询APP审核详情-费用报销")
    @PostMapping("/appAuditDetail2")
    public CommonResult<?> queryAppAuditDetail2(@RequestBody EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailReadService.queryAppAuditDetail2(dto));
    }

    @ApiOperation("OCR识别")
    @PostMapping("/ocrIdentify")
    public CommonResult<?> ocrIdentify(EcsReimDetailDto dto){
        List<EcsInvoRcdVo> list = ecsReimDetailWriteService.ocrIdentify(dto);
        return CommonResult.success(list);
    }

    @ApiOperation("OCR识别")
    @PostMapping("/ocrIdentifyNew")
    public CommonResult<?> ocrIdentifyNew(EcsReimDetailDto dto){
        List<String> list = ecsReimDetailWriteService.ocrIdentifyNew(dto);
        return CommonResult.success(list);
    }

    /**
     * 保存
     */
    @ApiOperation("新增报销明细")
    @PostMapping("/save")
    public CommonResult<?> save(EcsReimDetailDto dto){
        ecsReimDetailWriteService.saveReimDetail(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增报销明细")
    @PostMapping("/saveNew")
    public CommonResult<?> saveNew(EcsReimDetailDto dto){
        ecsReimDetailWriteService.saveReimDetailNew(dto);
        return CommonResult.success();
    }

    /**
     * 分摊报销生成分摊明细
     */
    @ApiOperation("新增报销明细")
    @PostMapping("/generateShareResExcel")
    public CommonResult<?> generateShareResExcel(EcsReimDetailDto dto){
        return CommonResult.success(ecsReimDetailWriteService.generateShareResExcel(dto));
    }

    /**
     * 修改
     */
    @ApiOperation("修改报销明细")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimDetailDto dto){
        ecsReimDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除报销明细")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimDetailDto dto){
        ecsReimDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 删除未开始审核报销
     * @param dto
     * @return
     */
    @ApiOperation("删除未开始审核报销")
    @DeleteMapping("/deleteNoAudit")
    public CommonResult<?> deleteNoAudit(@RequestBody EcsReimDetailDto dto) {
        ecsReimDetailWriteService.deleteNoAudit(dto);
        return CommonResult.success();
    }

    /**
     * 删除未开始审核报销
     * @param dto
     * @return
     */
    @ApiOperation("删除未开始审核报销")
    @DeleteMapping("/deleteNoAuditNew")
    public CommonResult<?> deleteNoAuditNew(@RequestBody EcsReimDetailDto dto) {
        ecsReimDetailWriteService.deleteNoAuditNew(dto);
        return CommonResult.success();
    }

    @ApiOperation("取消未审核过的差旅申请")
    @DeleteMapping("/cancelEcsReimDetailNew")
    public CommonResult<?> cancelEcsReimDetailNew(@RequestBody EcsReimDetailDto dto) {
        ecsReimDetailWriteService.cancelEcsReimDetailNew(dto);
        return CommonResult.success();
    }

    @ApiOperation("更改报销财务科目")
    @PostMapping("/modifyReimAsst")
    public CommonResult<?> modifyReimAsst(@RequestBody EcsReimDetailDto dto){
        ecsReimDetailWriteService.modifyReimAsst(dto);
        return CommonResult.success();
    }

    /**
     * 上传付款证明
     * @return
     */
    @ApiOperation("上传付款证明")
    @PostMapping("/uploadPayFiles")
    public CommonResult<?> uploadPayFiles(EcsReimDetailDto dto){
        ecsReimDetailWriteService.uploadPayFiles(dto);
        return CommonResult.success();
    }

    /**
     * 更新资金类型
     * @param dto
     * @return
     */
    @ApiOperation("更新资金类型")
    @PostMapping("/updateFundType")
    public CommonResult<?> updateFundType(@RequestBody EcsReimDetailDto dto){
        ecsReimDetailWriteService.updateFundType(dto);
        return CommonResult.success();
    }

    /**
     * 更新差旅、培训项目(补助项目)信息
     * @param dto
     * @return
     */
    @ApiOperation("更新差旅、培训项目(补助项目)信息")
    @PostMapping("/updateProItems")
    public CommonResult<?> updateProItems(@RequestBody EcsReimDetailDto dto) {
        ecsReimDetailWriteService.updateProItems(dto);
        return CommonResult.success();
    }

    /**
     * 更新合同项目信息
     * @param dto
     * @return
     */
    @ApiOperation("更新合同项目信息")
    @PostMapping("/updateContractItems")
    public CommonResult<?> updateContractItems(@RequestBody EcsReimDetailDto dto) {
        ecsReimDetailWriteService.updateContractItems(dto);
        return CommonResult.success();
    }

    /**
     * 查询费用报销申请审核数量
     * @return
     */
    @ApiOperation("查询费用报销申请审核数量")
    @PostMapping("/queryExpenseAuditWarnNum")
    public CommonResult<?> queryExpenseAuditWarnNum(@RequestBody EcsReimDetailDto dto) {
        return CommonResult.success(ecsReimDetailReadService.queryExpenseAuditWarnNum(dto));
    }

    @PostMapping("/queryTravelPsnInfo")
    @ApiOperation("查询年度差旅(出差或培训))随行人员信息")
    public CommonResult<?> queryTravelPsnInfo(@RequestBody EcsReimDetailDto dto) {
        return CommonResult.success(ecsReimDetailReadService.queryTravelPsnInfo(dto));
    }

    /**
     * 生成费用报销审批表
     * @param params
     * @return
     */
    @ApiOperation("生成费用报销审批表")
    @PostMapping("/queryEcsReimDoc")
    public CommonResult<?> queryEcsReimDoc(@RequestBody Map<String,Object> params) {
        return CommonResult.success(ecsReimDetailReadService.queryEcsReimDoc(params));
    }

    /**
     * 生成费用报销审批表
     * @param params
     * @return
     */
    @ApiOperation("生成费用报销审批表")
    @PostMapping("/queryEcsReimDocMultiTrip")
    public CommonResult<?> queryEcsReimDocMultiTrip(@RequestBody List<Map<String,Object>> params) {
        return CommonResult.success(ecsReimDetailReadService.queryEcsReimDocMultiTrip(params));
    }

    /**
     * 零星/物资采购上传付款单
     * @param dto
     * @return
     */
    @ApiOperation("零星/物资采购上传付款单")
    @PostMapping("/purcUploadReceipt")
    public CommonResult<?> purcUploadReceipt(EcsReimDetailDto dto) {
        ecsReimDetailWriteService.purcUploadReceipt(dto);
        return CommonResult.success();
    }


    /**
     * 上传报销详情文件
     * @param file
     * @return
     */
    @ApiOperation("上传报销详情文件")
    @PostMapping("/uploadReimDetailFile")
    public CommonResult<?> uploadReimDetailFile(MultipartFile file) {
        List<EcsReimItemDetail> ecsReimItemDetails = ecsReimDetailWriteService.uploadReimDetailFile(file);
        return CommonResult.success(ecsReimItemDetails);
    }

    /**
     * 查询冲抵借款报销记录
     * @param dto
     * @return
     */
    @ApiOperation("查询冲抵借款报销记录")
    @PostMapping("/queryLoanReim")
    public CommonResult<?> queryLoanReim(@RequestBody EcsReimDetailDto dto) {
        return CommonResult.success(ecsReimDetailWriteService.queryLoanReim(dto));
    }

}
