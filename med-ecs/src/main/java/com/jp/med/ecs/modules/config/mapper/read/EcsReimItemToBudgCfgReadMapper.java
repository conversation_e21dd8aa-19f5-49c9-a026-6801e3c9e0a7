package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.common.dto.ecs.EcsReimItemToBudgCfgDto;
import com.jp.med.common.vo.ecs.EcsReimItemToBudgCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 报销项目对应预算项目
 * <AUTHOR>
 * @email -
 * @date 2023-12-12 09:43:44
 */
@Mapper
public interface EcsReimItemToBudgCfgReadMapper extends BaseMapper<EcsReimItemToBudgCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimItemToBudgCfgVo> queryList(EcsReimItemToBudgCfgDto dto);
}
