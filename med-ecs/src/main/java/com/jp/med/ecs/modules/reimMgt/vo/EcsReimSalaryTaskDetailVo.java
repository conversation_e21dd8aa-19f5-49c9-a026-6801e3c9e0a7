package com.jp.med.ecs.modules.reimMgt.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 应发工资报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Data
public class EcsReimSalaryTaskDetailVo {

	/** id */
	private Integer id;

	/** 报销任务id */
	private Integer taskId;

	/** 报销科室 */
	private String orgId;

	/** 报销科室名称 */
	private String orgName;

	/** 报销类型 */
	private String reimType;

	/** 报销金额 */
	private BigDecimal reimAmt;

	/** 报销摘要 */
	private String reimDesc;

	/** 类别对应预算编码 **/
	private String budgetCode;

	/** 类型 **/
	private String type;

	/** 报销项目 **/
	private String reimName;

	/** 个人扣减-个人编号 **/
	private String empCode;

	/** 人员类型(在编 招聘 临聘) */
	private String empType;

	/** 人数 **/
	private Integer empCount;

	/** 科目对应预算是否是汇总预算 **/
	private String bgtSummary;

}
