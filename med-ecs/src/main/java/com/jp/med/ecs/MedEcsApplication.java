package com.jp.med.ecs;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@ComponentScan(basePackages = "com.jp.med")
@MapperScan(basePackages = "com.jp.med.ecs.modules.**.mapper")
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.jp.med.**.feign")
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class})
public class    MedEcsApplication {

    public static void main(String[] args) {
        System.setProperty("pagehelper.banner", "false");
        SpringApplication.run(MedEcsApplication.class, args);
    }

}

