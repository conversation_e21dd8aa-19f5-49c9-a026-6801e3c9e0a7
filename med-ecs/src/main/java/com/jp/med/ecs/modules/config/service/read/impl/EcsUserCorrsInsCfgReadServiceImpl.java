package com.jp.med.ecs.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsUserCorrsInsCfgReadMapper;
import com.jp.med.ecs.modules.config.dto.EcsUserCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsUserCorrsInsCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsUserCorrsInsCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsUserCorrsInsCfgReadServiceImpl extends ServiceImpl<EcsUserCorrsInsCfgReadMapper, EcsUserCorrsInsCfgDto> implements EcsUserCorrsInsCfgReadService {

    @Autowired
    private EcsUserCorrsInsCfgReadMapper ecsUserCorrsInsCfgReadMapper;

    @Override
    public List<EcsUserCorrsInsCfgVo> queryList(EcsUserCorrsInsCfgDto dto) {
        return ecsUserCorrsInsCfgReadMapper.queryList(dto);
    }

    @Override
    public List<EcsUserCorrsInsCfgVo> queryPageList(EcsUserCorrsInsCfgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsUserCorrsInsCfgReadMapper.queryList(dto);
    }

}
