package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsActigCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstCfgVo;
import com.jp.med.ecs.modules.config.vo.EcsActigCfgVo;

import java.util.List;

/**
 * 会计科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
public interface EcsActigCfgReadService extends IService<EcsActigCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsActigCfgVo> queryList(EcsActigCfgDto dto);

    /**
     * 查询辅项信息
     * @param dto
     * @return
     */
    List<EcsActigAsstCfgVo> queryAuxItem(EcsActigCfgDto dto);

    List<EcsActigCfgVo> queryListLayFlat(EcsActigCfgDto dto);
}

