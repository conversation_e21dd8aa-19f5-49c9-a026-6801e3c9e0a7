package com.jp.med.ecs.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ecs.modules.config.mapper.write.EcsActigAsstCfgWriteMapper;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstCfgDto;
import com.jp.med.ecs.modules.config.service.write.EcsActigAsstCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会计科目辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 15:45:43
 */
@Service
@Transactional(readOnly = false)
public class EcsActigAsstCfgWriteServiceImpl extends ServiceImpl<EcsActigAsstCfgWriteMapper, EcsActigAsstCfgDto> implements EcsActigAsstCfgWriteService {
}
