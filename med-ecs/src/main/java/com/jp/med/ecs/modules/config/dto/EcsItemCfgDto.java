package com.jp.med.ecs.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 财务科目辅助项目项目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Data
@TableName("ecs_item_cfg" )
public class EcsItemCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 项目代码 */
    @TableField("item_code")
    private String itemCode;

    /** 项目名称 */
    @TableField("item_name")
    private String itemName;

    /** 拼音助记码 */
    @TableField("pinyin")
    private String pinyin;

    /** 开始日期 */
    @TableField("begn_date")
    private String begnDate;

    /** 结束日期 */
    @TableField("end_date")
    private String endDate;

    /** 负责部门 */
    @TableField("dept")
    private String dept;

    /** 负责人 */
    @TableField("resper")
    private String resper;

    /** 立项年度 */
    @TableField("esta_year")
    private String estaYear;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改时间 */
    @TableField("modi_time")
    private String modiTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 年度 **/
    @TableField("year")
    private String year;

    @TableField("parent_item_code")
    private String parentItemCode;

    /** 查询字符串 */
    @TableField(exist = false)
    private String qs;
}
