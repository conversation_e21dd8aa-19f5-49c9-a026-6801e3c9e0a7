package com.jp.med.ecs.modules.reimMgt.enums;

import io.seata.common.util.StringUtils;

/**
 * 分摊类型Excel-相关读取信息
 */
public enum ShareExcelEnum {

    WATER_FEE("1","水费",0,"1","template/分摊费用水费导出模板.xlsx","1", 3, 123),
    ELECTRIC_FEE("2","电费",1, "1","template/分摊费用电费导出模板.xlsx","1",3,123),
    CLEANING_FEE("3","保洁费",2,"2","template/分摊费用保洁费导出模板.xlsx","0",3,121),

    PHONE_FEE("4","电话费",3,"1","template/分摊费用电话费导出模板.xlsx","1",3,124),

    GAS_FEE("5","燃气费",4,"3","template/分摊费用燃气费导出模板.xlsx","1",2,18);


    /** 分摊类型 1:水费 2.电费 3.保洁费 4.电话费 。。。按需增加**/
    private final String type;

    /** 分摊类型名称 **/
    private final String name;

    /** 分摊类型读取的excel模板sheet **/
    private final Integer sheetIdx;

    /** 分摊方式 1：按人数 2：按面积 3：床位 **/
    private final String shareMode;

    /** excel模板路径 **/
    private final String excelPath;

    /** 是否按范围读取模板 **/
    private final String rangeFlag;

    /** 读取excel模板开始行 **/
    private final Integer startRow;

    /** 读取excel模板结束行 **/
    private final Integer endRow;

     ShareExcelEnum(String type,String name,int sheetIdx,String shareMode,String excelPath,String rangeFlag,int startRow,int endRow) {
        this.type= type;
        this.name = name;
        this.sheetIdx = sheetIdx;
        this.shareMode = shareMode;
        this.excelPath = excelPath;
        this.rangeFlag = rangeFlag;
        this.startRow = startRow;
        this.endRow = endRow;
    }

    public String getType() {
         return type;
    }

    public String getName() {
         return name;
    }

    public Integer getSheetIdx(){
         return sheetIdx;
    }

    public String getShareMode(){
         return shareMode;
    }

    public String getExcelPath(){
         return excelPath;
    }

    public String getRangeFlag(){
         return rangeFlag;
    }

    public Integer getStartRow() {
         return startRow;
    }

    public Integer getEndRow() {
         return endRow;
    }

    public static ShareExcelEnum getByType(String type) {
         for (ShareExcelEnum status : ShareExcelEnum.values()) {
             if (StringUtils.equals(status.getType(),type)) {
                 return status;
             }
         }
         return null;
    }
}
