package com.jp.med.ecs.modules.drugMgt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 入库单
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:44
 */
@Data
@TableName("ecs_stoin")
public class EcsStoinEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 入库单号 */
	@TableField("stoin_num")
	private String stoinNum;

	/** 入库日期 */
	@TableField("stoin_date")
	private String stoinDate;

	/** 数量合计 */
	@TableField("totlcnt")
	private Integer totlcnt;

	/** 零售金额 */
	@TableField("rtal_amt")
	private BigDecimal rtalAmt;

	/** 进价金额 */
	@TableField("purcpric_amt")
	private BigDecimal purcpricAmt;

	/** 发票号 */
	@TableField("invono")
	private String invono;

	/** 供货单位 */
	@TableField("spler")
	private String spler;

	/** 报销标识 */
	@TableField("reim_flag")
	private String reimFlag;

	/** 同步日期 */
	@TableField("sync_date")
	private String syncDate;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}
