package com.jp.med.ecs.modules.drugMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatReimDetailDto;
import com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatReimDetailVo;

import java.util.List;

/**
 * 卫材报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-08-02 09:28:49
 */
public interface EcsSatmatReimDetailReadService extends IService<EcsSatmatReimDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsSatmatReimDetailVo> queryList(EcsSatmatReimDetailDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsSatmatReimDetailVo> queryPageList(EcsSatmatReimDetailDto dto);

    EcsSatmatReimDetailVo queryAppAuditDetail(EcsSatmatReimDetailDto dto);

    /**
     * 查询审核数据
     * @param dto
     * @return
     */
    List<EcsSatmatReimDetailVo> queryAuditData(EcsSatmatReimDetailDto dto);
}

