package com.jp.med.ecs.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsActigAsstCfgReadMapper;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsActigAsstCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsActigAsstCfgReadServiceImpl extends ServiceImpl<EcsActigAsstCfgReadMapper, EcsActigAsstCfgDto> implements EcsActigAsstCfgReadService {

    @Autowired
    private EcsActigAsstCfgReadMapper ecsActigAsstCfgReadMapper;

    @Override
    public List<EcsActigAsstCfgVo> queryList(EcsActigAsstCfgDto dto) {
        return ecsActigAsstCfgReadMapper.queryList(dto);
    }

}
