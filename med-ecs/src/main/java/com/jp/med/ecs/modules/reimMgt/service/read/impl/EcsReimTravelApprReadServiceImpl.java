package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aspose.words.Document;
import com.aspose.words.ImportFormatMode;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.PdfUtil;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimPsnDetail;
import com.jp.med.ecs.modules.reimMgt.enums.ApprTemplateEnum;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDetailReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimTravelApprReadMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimTravelApprReadService;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimTravelApprVo;
import org.apache.commons.fileupload.FileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class EcsReimTravelApprReadServiceImpl extends ServiceImpl<EcsReimTravelApprReadMapper, EcsReimTravelApprDto> implements EcsReimTravelApprReadService {

    @Autowired
    private EcsReimTravelApprReadMapper ecsReimTravelApprReadMapper;

    @Autowired
    private EcsReimDetailReadMapper ecsReimDetailReadMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Override
    public List<EcsReimTravelApprVo> queryList(EcsReimTravelApprDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 报销页面查询申请时不分类型，只查未报销的申请
        /*if (StringUtils.isNotEmpty(dto.getReimFlag())) {
            dto.setType(null);
        }*/
        if (!Objects.isNull(dto.getAudit())) {
            if (dto.getAudit()) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        } /*else {
            if (StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        }*/

        return ecsReimTravelApprReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimTravelApprVo> queryListNew(EcsReimTravelApprDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 报销页面查询申请时不分类型，只查未报销的申请
        /*if (StringUtils.isNotEmpty(dto.getReimFlag())) {
            dto.setType(null);
        }*/
        if (!Objects.isNull(dto.getAudit())) {
            if (dto.getAudit()) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        } /*else {
            //audit为空且empCode不为空(即不为admin)，则默查询申请
            if (StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        }*/

        return ecsReimTravelApprReadMapper.queryListNew(dto);
    }

    @Override
    public List<EcsReimTravelApprVo> queryListMultiTrip(EcsReimTravelApprDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        if (!Objects.isNull(dto.getAudit())) {
            if (dto.getAudit()) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        }

        List<EcsReimTravelApprVo> ecsReimTravelApprVos = ecsReimTravelApprReadMapper.queryListNew(dto);
        if (CollectionUtil.isEmpty(ecsReimTravelApprVos) || CollectionUtil.isNotEmpty(dto.getIds()) || !StrUtil.isBlank(dto.getProcessInstanceId())) {
            return ecsReimTravelApprVos;
        }
        Map<String, List<EcsReimTravelApprVo>> collect = ecsReimTravelApprVos.stream().collect(Collectors.groupingBy(EcsReimTravelApprVo::getProcessInstanceId,LinkedHashMap::new,
                Collectors.toList()));
        List<EcsReimTravelApprVo> result = new ArrayList<>();
        collect.forEach((key, value) -> {
            result.add(value.get(0));
        });
        return result;
    }

    @Override
    public EcsReimTravelApprVo queryAppAuditDetail(EcsReimTravelApprDto dto) {
        // 查询审核流程
        dto.setSqlAutowiredHospitalCondition(true);
        List<AuditDetail> details = auditFeignService.getAuditDetails(new AuditDetail(dto.getAuditBchno(), dto.getSysUser().getHrmUser().getEmpCode(), OSSConst.BUCKET_ECS));
        // 查询当前审核记录详情
        List<EcsReimTravelApprVo> ecsReimTravelApprVos = ecsReimTravelApprReadMapper.queryList(dto);
        EcsReimTravelApprVo apprVo = new EcsReimTravelApprVo();
        if (CollectionUtil.isNotEmpty(ecsReimTravelApprVos)) {
            apprVo = ecsReimTravelApprVos.get(0);
            apprVo.setEvectionTime(apprVo.getEvectionBegnTime() + " 至 " + apprVo.getEvectionEndTime());
            details.sort(Comparator.comparing(AuditDetail::getChkSeq));
            apprVo.setAuditDetails(details);

            List<EcsReimPsnDetail> psnDetails = getPsnDetails(apprVo.getId(), MedConst.TYPE_1);
            if (CollectionUtil.isNotEmpty(psnDetails)) {
                apprVo.setTableDetails(psnDetails);
            }
        }
        return apprVo;
    }

    @Override
    public Map<String,Object> queryApprDetail(EcsReimTravelApprDto dto) {
        dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
        //查询当前审核记录
        List<EcsReimTravelApprVo> ecsReimTravelApprVos = ecsReimTravelApprReadMapper.queryList(dto);
        if (CollectionUtil.isEmpty(ecsReimTravelApprVos)) {
            throw new AppException("当前审核记录不存在");
        }

        EcsReimDetailDto item = new EcsReimDetailDto();
        item.setId(dto.getId());
        item.setType(MedConst.TYPE_1);
        List<EcsReimPsnDetail> psnDetails = ecsReimDetailReadMapper.queryPsnDetail(item);
        return new HashMap<>(){
            {
                put("apprVo",ecsReimTravelApprVos.get(0));
                put("psnDetails",psnDetails);
            }
        };
    }

    public List<EcsReimPsnDetail> getPsnDetails(Long id, String type){
        // 查询出差人
        EcsReimDetailDto ecsReimDetailDto = new EcsReimDetailDto();
        ecsReimDetailDto.setId(id);
        // 1为出差申请，2为报销
        ecsReimDetailDto.setType(type);
        List<EcsReimPsnDetail> psnDetails = ecsReimDetailReadMapper.queryPsnDetail(ecsReimDetailDto);
        if (CollectionUtil.isNotEmpty(psnDetails)) {
            psnDetails.forEach(ecsReimPsnDetail -> {
                ecsReimPsnDetail.setDept(ecsReimPsnDetail.getDeptName());
                ecsReimPsnDetail.setTripPsn(ecsReimPsnDetail.getTripPsnName());
            });
        }
        return psnDetails;
    }

    @Override
    public Integer queryTrainingApplyWarnNum(EcsReimTravelApprDto dto) {
        dto.setId(null);
        dto.setAudit(false);
        dto.setPageNum(1);
        dto.setPageSize(99999);
        dto.setApprDeptType(MedConst.TYPE_2);
        dto.setAuditState(Arrays.asList("3"));
        return queryList(dto).size();
    }

    @Override
    public Integer queryTrainingToReimWarnNum(EcsReimTravelApprDto dto) {
        dto.setId(null);
        dto.setAudit(false);
        dto.setPageNum(1);
        dto.setPageSize(99999);
        dto.setApprDeptType(MedConst.TYPE_2);
        dto.setReimFlag(MedConst.TYPE_0);
        return queryList(dto).size();
    }

    @Override
    public Integer queryTravelApplyWarnNum(EcsReimTravelApprDto dto) {
        dto.setId(null);
        dto.setAudit(false);
        dto.setPageNum(1);
        dto.setPageSize(99999);
        dto.setApprDeptType(MedConst.TYPE_1);
        dto.setAuditState(Arrays.asList("3"));
        return queryList(dto).size();
    }

    @Override
    public Integer queryTravelToReimWarnNum(EcsReimTravelApprDto dto) {
        dto.setId(null);
        dto.setAudit(false);
        dto.setPageNum(1);
        dto.setPageSize(99999);
        dto.setApprDeptType(MedConst.TYPE_1);
        dto.setReimFlag(MedConst.TYPE_0);
        return queryList(dto).size();
    }

    @Override
    public Integer queryTrainingAuditWarnNum(EcsReimTravelApprDto dto) {
        dto.setId(null);
        dto.setAudit(true);
        dto.setAuditFlag(MedConst.TYPE_1);
        dto.setPageNum(1);
        dto.setPageSize(99999);
        dto.setAuditState(Arrays.asList("3"));
        return queryList(dto).size();
    }

    @Override
    public Map<String, String> queryTravelApprDoc(Map<String, Object> params) {
        //获取审核批次号
//        String auditNo = params.get("auditBatchNo").toString();
        //文件路径
        Map<String,String> fileInfo = new HashMap<>();
        //处理审核流程中数据
        List<Object> list = (List<Object>) params.get("list");
        List<Object> newList = new ArrayList<>();
        list.forEach(item -> {
            Map<String, Object> signObj = BeanUtil.beanToMap(item);
            if (!Objects.isNull(signObj.get("sign"))) {
                //前端传递的sign就为签名图片的外链，不需要再生成外链
                String signUrl = signObj.get("sign").toString();
                String signuri = signUrl.split("\\?")[0];
//                String signStr = OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_ECS,sign);
                if (signUrl.contains("hrp.zjxrmyy.cn:18090")) {
                    signUrl = signUrl.replace("https","http");
                    signUrl = signUrl.replace("hrp.zjxrmyy.cn:18090","***********:4556");
                    //去掉oss路径
                    int ossIdx = signUrl.indexOf("oss");
                    if (ossIdx != -1) {
                        StringBuilder sb = new StringBuilder(signUrl);
                        sb.delete(ossIdx,ossIdx+4);
                        signUrl = sb.toString();
                    }
                }

                PictureRenderData picRender = Pictures.ofUrl(signUrl, PictureType.suggestFileType(signuri))
                        .size(70, 30).create();
                signObj.put("sign",picRender);
            }
            Object o = BeanUtil.mapToBean(signObj, Object.class, true);
            newList.add(o);
        });

        params.put("list",newList);
        // 用行循环插件

        HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy(true);
        ConfigureBuilder builder = Configure.builder();
        Configure config = builder.bind("list", policy)
                .build();

        String templateName = ApprTemplateEnum.getByType(params.get("apprType").toString()).getTemplatePath();
        try{
            //获取申请表模板  出差/培训
            String filePath = PdfUtil.readOssRender(OSSConst.BUCKET_ECS,templateName,params,config);
            String fileName = "培训申请审批表";
            fileInfo.put("filePath",filePath);
            fileInfo.put("fileName",fileName);
        } catch (Exception e) {
            log.error("生成申请审批表失败",e);
            throw new AppException("生成申请审批表失败");
        }
        return fileInfo;
    }

    @Override
    public Map<String, String> queryTravelApprDocMultiTrip(List<Map<String, Object>> params) {

        //文件路径
        Map<String,String> fileInfo = new HashMap<>();

        List<ByteArrayOutputStream> wordStreams = new ArrayList<>();
        params.forEach(param -> {

            //处理审核流程中数据
            List<Object> list = (List<Object>) param.get("list");
            List<Object> newList = new ArrayList<>();
            list.forEach(item -> {
                Map<String, Object> signObj = BeanUtil.beanToMap(item);
                if (!Objects.isNull(signObj.get("sign"))) {
                    //前端传递的sign就为签名图片的外链，不需要再生成外链
                    String signUrl = signObj.get("sign").toString();
                    String signuri = signUrl.split("\\?")[0];
                    if (signUrl.contains("hrp.zjxrmyy.cn:18090")) {
                        signUrl = signUrl.replace("https","http");
                        signUrl = signUrl.replace("hrp.zjxrmyy.cn:18090","***********:4556");
                        //去掉oss路径
                        int ossIdx = signUrl.indexOf("oss");
                        if (ossIdx != -1) {
                            StringBuilder sb = new StringBuilder(signUrl);
                            sb.delete(ossIdx,ossIdx+4);
                            signUrl = sb.toString();
                        }
                    }
                    PictureRenderData picRender = Pictures.ofUrl(signUrl, PictureType.suggestFileType(signuri))
                            .size(70, 30).create();
                    signObj.put("sign",picRender);
                }
                Object o = BeanUtil.mapToBean(signObj, Object.class, true);
                newList.add(o);
            });
            param.put("list",newList);

            // 用行循环插件

            HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy(true);
            ConfigureBuilder builder = Configure.builder();
            Configure config = builder.bind("list", policy)
                    .build();

            String templateName = ApprTemplateEnum.getByType(param.get("apprType").toString()).getTemplatePath();

            try {
                // 验证License
                if (!PdfUtil.isWordLicense()) {
                    return;
                }
                InputStream inputStream = OSSUtil.getObject(OSSConst.BUCKET_ECS, templateName);
                XWPFTemplate xwpfTemplate = XWPFTemplate.compile(inputStream,config);
                xwpfTemplate.render(param);
                // 文件输出流
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                // 选入后的数据写入流
                xwpfTemplate.write(bos);
                wordStreams.add(bos);
            }catch (Exception e) {
                log.error("合并多行程失败",e);
                throw new AppException("合并多行程失败");
            }
        });

        try {
            Document finalDoc = null;
            for (ByteArrayOutputStream wordStream : wordStreams) {
                Document doc = new Document(new ByteArrayInputStream(wordStream.toByteArray()));
                if(ObjectUtil.isEmpty(finalDoc)){
                    finalDoc = doc;
                }else{
                    finalDoc.appendDocument(doc, ImportFormatMode.USE_DESTINATION_STYLES);
                }
            }
            // 3. 转换为 PDF
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            finalDoc.save(pdfOutputStream, com.aspose.words.SaveFormat.PDF);

            // 4. 上传 PDF 到 OSS
            FileItem fileItem = PdfUtil.streamToMultipartFile(pdfOutputStream, "application/pdf", "merged.pdf");
            MultipartFile file = new CommonsMultipartFile(fileItem);
            String filePath = OSSUtil.uploadFile("temp", "", file);
            String fileName = "培训申请审批表";
            fileInfo.put("filePath",filePath);
            fileInfo.put("fileName",fileName);
        }catch (Exception e) {
            log.error("生成申请审批表失败",e);
            throw new AppException("生成申请审批表失败");
        }
        return fileInfo;
    }
}
