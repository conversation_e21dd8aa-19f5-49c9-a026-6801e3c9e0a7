package com.jp.med.ecs.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 会计科目科室辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2024-02-01 16:36:57
 */
@Data
@TableName("ecs_actig_asst_dept_cfg" )
public class EcsActigAsstDeptCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 科室编码 */
    @TableField("dept_code")
    private String deptCode;

    /** 科室名称 */
    @TableField("dept_name")
    private String deptName;

    /** 拼音助记码 */
    @TableField("pinyin")
    private String pinyin;

    /** 备注 */
    @TableField("remarks")
    private String remarks;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改时间 */
    @TableField("modi_time")
    private String modiTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 上级科室编码 */
    @TableField("parent_dept_code")
    private String parentDeptCode;

    /** 查询字符串 */
    @TableField(exist = false)
    private String qs;
}
