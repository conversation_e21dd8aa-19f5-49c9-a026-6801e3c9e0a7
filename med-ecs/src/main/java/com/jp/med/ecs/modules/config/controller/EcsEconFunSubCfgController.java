package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.util.ChineseCharToEnUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.ecs.modules.config.dto.EcsEconFunSubCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsEconFunSubCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsEconFunSubCfgWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 经济和功能科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Api(value = "经济和功能科目配置", tags = "经济和功能科目配置")
@RestController
@RequestMapping("ecsEconFunSubCfg")
public class EcsEconFunSubCfgController {

    @Autowired
    private EcsEconFunSubCfgReadService ecsEconFunSubCfgReadService;

    @Autowired
    private EcsEconFunSubCfgWriteService ecsEconFunSubCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询经济和功能科目配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsEconFunSubCfgDto dto){
        return CommonResult.success(ecsEconFunSubCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增经济和功能科目配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsEconFunSubCfgDto dto){
        if (StringUtils.isNotEmpty(dto.getStatus())) {
            dto.setSubType(dto.getStatus());
        }
        dto.setCrter(dto.getSysUser().getUsername());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setPinyin(ChineseCharToEnUtil.getAllFirstLetter(dto.getSubName()).toUpperCase());
        ecsEconFunSubCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改经济和功能科目配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsEconFunSubCfgDto dto){
        dto.setModiTime(DateUtil.getCurrentTime(null));
        ecsEconFunSubCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除经济和功能科目配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsEconFunSubCfgDto dto){
        ecsEconFunSubCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 同步
     */
    @ApiOperation("查询经济和功能科目配置")
    @PostMapping("/sync")
    public CommonResult<?> sync(@RequestBody EcsEconFunSubCfgDto dto){
        ecsEconFunSubCfgWriteService.sync(dto);
        return CommonResult.success();
    }
}
