package com.jp.med.ecs.modules.reimMgt.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class EcsReimApprManage {

    /** 部门code **/
    private String deptCode;

    /** 部门名称 **/
    private String deptName;

    /** 随行人员code **/
    private String psnCode;

    /** 随行人员名称 **/
    private String psnName;

    /** 性别  **/
    private String sex;

    /** 年龄 **/
    private Integer age;

    /** 职称 **/
    private String engageLevel;

    /** 行政职务 **/
    private String adminLevel;

    /** 出差地点 **/
    private String evectionAddr;

    /** 出差详细地址 **/
    private String evectionDetlAddr;

    /** 出差事由 **/
    private String evectionRea;

    /** 出差开始时间 **/
    private String evectionBeginTime;

    /** 出差结束时间 **/
    private String evectionEndTime;

    /** 期限(天) **/
    private String timePeriod;

    /** 申请id **/
    private String apprId;

    /** 报销id **/
    private String reimId;

    /** 报销费用 **/
    private BigDecimal reimAmt;

    /** 业务状态 **/
    private String busstas;

}
