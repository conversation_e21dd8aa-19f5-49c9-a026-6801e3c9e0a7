package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDto;
import com.jp.med.common.vo.EcsReimFixedAsstVo;

import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
public interface EcsReimFixedAsstReadService extends IService<EcsReimFixedAsstDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimFixedAsstVo> queryList(EcsReimFixedAsstDto dto);

    /**
     * 查询固定项上级
     * @param dto
     * @return
     */
    List<EcsReimFixedAsstVo> queryFixedTree(EcsReimFixedAsstDto dto);
}

