package com.jp.med.ecs.modules.reimMgt.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimPsnDetail;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 报销差旅审批
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 14:02:10
 */
@Data
@TableName("ecs_reim_travel_appr" )
public class EcsReimTravelApprDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Long id;

    /** 申请时间 */
    @TableField("appyer_time")
    private String appyerTime;

    /** 申请人 */
    @TableField("appyer")
    private String appyer;

    /** 申请科室 **/
    @TableField("appr_dept")
    private String apprDept;

    /** 出差事由 */
    @TableField("evection_rea")
    private String evectionRea;

    /** 出差开始时间 */
    @TableField("evection_begn_time")
    private String evectionBegnTime;

    /** 出差结束时间 */
    @TableField("evection_end_time")
    private String evectionEndTime;

    /** 出差地点 */
    @TableField("evection_addr")
    private Integer evectionAddr;

    /** 出差地点详情 */
    @TableField("evection_detl_addr")
    private String evectionDetlAddr;

    /** 是否绕道 */
    @TableField("detour_or_not")
    private String detourOrNot;

    /** 公里数 */
    @TableField("kil")
    private BigDecimal kil;

    /** 交通方式 */
    @TableField("trnp")
    private String trnp;

    /** 交通方式号码（如汽车为车牌号） */
    @TableField("trnp_num")
    private String trnpNum;

    /** 是否安排伙食 */
    @TableField("food")
    private String food;

    /** 是否安排住宿 */
    @TableField("stay")
    private String stay;

    /** 承诺 */
    @TableField("prse")
    private String prse;

    /** 预计差旅金额 */
    @TableField("plan_amt")
    private BigDecimal planAmt;

    /** 预计培训金额 **/
    @TableField("plan_amt2")
    private BigDecimal planAmt2;

    /** 预计租车费金额 **/
    @TableField("plan_amt3")
    private BigDecimal planAmt3;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 文件 */
    @TableField("att")
    private String att;

    /** 文件名称 */
    @TableField("att_name")
    private String attName;

    /** 类型 */
    @TableField("type")
    private String type;

    /** 审核批次号 */
    @TableField("audit_bchno")
    private String auditBchno;

    /** 页面图片 */
    @TableField("page_image")
    private String pageImage;

    /** 报销标识 */
    @TableField("reim_flag")
    private String reimFlag;

    /** 出差性质 */
    @TableField("bus_met")
    private String busMet;

    /** 1:职能科室 2:临床、医技**/
    @TableField("appr_dept_type")
    private String apprDeptType;

    /** 审核流程id **/
    @TableField("chker_flow")
    private Integer chkerFlow;

    /** 出差范围 1：省内 2：省外 **/
    @TableField("travel_range")
    private String travelRange;

    /** 申请状态 **/
    @TableField("status")
    private String status;

    /** 自驾事由 **/
    @TableField("self_drive_rea")
    private String selfDriveRea;


    /** 是否审核 */
    @TableField(exist = false)
    private Boolean audit;

    /** tab类型 */
    @TableField(exist = false)
    private String auditFlag;

    /** 审核状态 */
    @TableField(exist = false)
    private List<String> auditState;

    /** 页面图片文件 */
    @TableField(exist = false)
    private MultipartFile pageImageFile;

    /** 文件 */
    @TableField(exist = false)
    private List<MultipartFile> attFiles;

    /** 出差时间 */
    @TableField(exist = false)
    private String evectionTime;

    /** 出差时间 */
    @TableField(exist = false)
    private String evectionAddrName;

    /** 出差人 */
    @TableField(exist = false)
    private List<EcsReimPsnDetail> psnDetails;

    //------BPM流程需要参数START--------
    @TableField(exist = false)
    private Map<String,String> bpmParams;

    /**
     * 发起人自选审批人 Map
     * <p>
     * key：taskKey 任务编码
     * value：审批人的数组
     * 例如：{ taskKey1 :[1, 2] }，则表示 taskKey1 这个任务，提前设定了，由 use    rId 为 1,2 的用户进行审批
     */
    @TableField(exist = false)
    private Map<String, List<String>> startUserSelectAssignees;

    /**
     * 对应的流程编号
     * <p>
     * 关联 ProcessInstance 的 id 属性
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    //------BPM流程需要参数-END--------

    /**
     * id集合
     */
    @TableField(exist = false)
    private List<Long> ids;
}
