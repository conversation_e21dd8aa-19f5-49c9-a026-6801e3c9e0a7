package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.dto.ecs.EcsDrugAccountPeriodDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.config.service.read.EcsDrugAccountPeriodReadService;
import com.jp.med.ecs.modules.config.service.write.EcsDrugAccountPeriodWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 药品账期
 * <AUTHOR>
 * @email -
 * @date 2025-02-21 10:36:30
 */
@Api(value = "药品账期", tags = "药品账期")
@RestController
@RequestMapping("ecsDrugAccountPeriod")
public class EcsDrugAccountPeriodController {

    @Autowired
    private EcsDrugAccountPeriodReadService ecsDrugAccountPeriodReadService;

    @Autowired
    private EcsDrugAccountPeriodWriteService ecsDrugAccountPeriodWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询药品账期")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsDrugAccountPeriodDto dto){
        return CommonResult.paging(ecsDrugAccountPeriodReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询药品账期")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsDrugAccountPeriodDto dto){
        return CommonResult.success(ecsDrugAccountPeriodReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增药品账期")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsDrugAccountPeriodDto dto){
        ecsDrugAccountPeriodWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改药品账期")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsDrugAccountPeriodDto dto){
        ecsDrugAccountPeriodWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除药品账期")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsDrugAccountPeriodDto dto){
        ecsDrugAccountPeriodWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 药品账期退账
     * @param dto
     * @return
     */
    @ApiOperation("药品账期退账")
    @PostMapping("/reverseAccount")
    public CommonResult<?> reverseAccount(@RequestBody EcsDrugAccountPeriodDto dto) {
        return CommonResult.success(ecsDrugAccountPeriodWriteService.reverseAccount(dto));
    }

    /**
     * 药品账期扎帐
     * @param dto
     * @return
     */
    @ApiOperation("药品账期退账")
    @PostMapping("/closeAccount")
    public CommonResult<?> closeAccount(@RequestBody EcsDrugAccountPeriodDto dto) {
        ecsDrugAccountPeriodWriteService.closeAccount(dto);
        return CommonResult.success();
    }

}
