package com.jp.med.ecs.modules.reimMgt.service.write.impl;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.ULIDUtil;
import com.jp.med.ecs.modules.common.service.write.EcsAuditWriteService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsFundApplyWriteMapper;
import com.jp.med.ecs.modules.reimMgt.dto.EcsFundApplyDto;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsFundApplyWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 经费申请
 * <AUTHOR>
 * @email -
 * @date 2023-12-28 17:58:39
 */
@Service
@Transactional(readOnly = false)
public class EcsFundApplyWriteServiceImpl extends ServiceImpl<EcsFundApplyWriteMapper, EcsFundApplyDto> implements EcsFundApplyWriteService {

    @Autowired
    private EcsFundApplyWriteMapper ecsFundApplyWriteMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Override
    public void saveFundApply(EcsFundApplyDto dto) {
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() :dto.getSysUser().getUsername();
        dto.setCrter(hrmUser.getEmpCode());
        dto.setAppyer(hrmUser.getEmpCode());
        dto.setAppyerDept(hrmUser.getHrmOrgId());
        dto.setAppyerTime(DateUtil.getCurrentTime(null));
        String batchNum = ULIDUtil.generate();
        dto.setAuditBchno(batchNum);
        ecsFundApplyWriteMapper.insert(dto);

        // 审核流程
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            AppMsgSup appMsgSup = new AppMsgSup();
            appMsgSup.setTitle("经费申请");
            String appyerName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName() :dto.getSysUser().getNickname();
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);
            appMsgSup.setContent("申请事由[" + dto.getAppyerRea() + "],申请金额:[" + dto.getSum() + "]");
            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(batchNum);
            auditPayload.setDetailUrl("/ecs/ecsFundApply/appAuditDetail");
            Map<String,Object> map = new HashMap<>();
            map.put("appyerRea", "申请事由");
            map.put("sum", "申请金额");
            auditPayload.setDisplayItem(map);
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(new AuditDetail(batchNum, dto.getAuditDetails(), appMsgSup, auditPayload, OSSConst.BUCKET_ECS)));
        }
    }
}
