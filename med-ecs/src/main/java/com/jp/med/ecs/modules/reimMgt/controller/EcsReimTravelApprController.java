package com.jp.med.ecs.modules.reimMgt.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.dto.SaveMultiTripDto;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimTravelApprReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimTravelApprWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 报销差旅审批
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 13:48:41
 */
@Api(value = "报销差旅审批", tags = "报销差旅审批")
@RestController
@RequestMapping("ecsReimTravelAppr")
public class EcsReimTravelApprController {

    @Autowired
    private EcsReimTravelApprReadService ecsReimTravelApprReadService;

    @Autowired
    private EcsReimTravelApprWriteService ecsReimTravelApprWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询报销差旅审批")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimTravelApprDto dto){
        return CommonResult.paging(ecsReimTravelApprReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询报销差旅审批")
    @PostMapping("/listNew")
    public CommonResult<?> listNew(@RequestBody EcsReimTravelApprDto dto){
        return CommonResult.paging(ecsReimTravelApprReadService.queryListNew(dto));
    }

    @ApiOperation("查询app审核详情数据")
    @PostMapping("/appAuditDetail")
    public CommonResult<?> queryAppAuditDetail(@RequestBody EcsReimTravelApprDto dto){
        return CommonResult.success(ecsReimTravelApprReadService.queryAppAuditDetail(dto));
    }

    /**
     * 查询已审核申请信息
     * @param dto
     * @return
     */
    @ApiOperation("查询已审核申请信息")
    @PostMapping("/apprAuditDetail")
    public CommonResult<?> queryApprAuditDetail(@RequestBody EcsReimTravelApprDto dto) {
        return CommonResult.success(ecsReimTravelApprReadService.queryApprDetail(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增报销差旅审批")
    @PostMapping("/save")
    public CommonResult<?> save(EcsReimTravelApprDto dto){
        ecsReimTravelApprWriteService.saveReimTravelAppr(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增报销差旅审批")
    @PostMapping("/saveNew")
    public CommonResult<?> saveNew(EcsReimTravelApprDto dto){
        ecsReimTravelApprWriteService.saveReimTravelApprNew(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增报销差旅审批（多行程）")
    @PostMapping("/saveMultiTrip")
    public CommonResult<?> saveMultiTrip(SaveMultiTripDto dto){
        ecsReimTravelApprWriteService.saveReimTravelApprMultiTrip(dto);
        return CommonResult.success();
    }

    /**
     * 列表
     */
    @ApiOperation("查询报销差旅审批")
    @PostMapping("/listMultiTrip")
    public CommonResult<?> listMultiTrip(@RequestBody EcsReimTravelApprDto dto){
        return CommonResult.paging(ecsReimTravelApprReadService.queryListMultiTrip(dto));
    }

    @ApiOperation("删除未审核过的差旅申请")
    @DeleteMapping("/deleteNoAuditMultiTrip")
    public CommonResult<?> deleteNoAuditMultiTrip(@RequestBody EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteService.deleteNoAuditMultiTrip(dto);
        return CommonResult.success();
    }

    @ApiOperation("取消未审核过的差旅申请")
    @DeleteMapping("/cancelEcsReimTravelApprMultiTrip")
    public CommonResult<?> cancelEcsReimTravelApprMultiTrip(@RequestBody EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteService.cancelEcsReimTravelApprMultiTrip(dto);
        return CommonResult.success();
    }

    /**
     * 生成申请审批表
     * @param params
     * @return
     */
    @ApiOperation("生成申请审批表")
    @PostMapping("/generateTravelApprDocMultiTrip")
    public CommonResult<?> generateTravelApprDocMultiTrip(@RequestBody List<Map<String,Object>> params) {
        return CommonResult.success(ecsReimTravelApprReadService.queryTravelApprDocMultiTrip(params));
    }

    /**
     * 修改
     */
    @ApiOperation("修改报销差旅审批")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimTravelApprDto dto){
        ecsReimTravelApprWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除报销差旅审批")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimTravelApprDto dto){
        ecsReimTravelApprWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除未审核过的差旅申请")
    @DeleteMapping("/deleteNoAudit")
    public CommonResult<?> deleteNoAudit(@RequestBody EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteService.deleteNoAudit(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除未审核过的差旅申请")
    @DeleteMapping("/deleteNoAuditNew")
    public CommonResult<?> deleteNoAuditNew(@RequestBody EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteService.deleteNoAuditNew(dto);
        return CommonResult.success();
    }

    @ApiOperation("取消未审核过的差旅申请")
    @DeleteMapping("/cancelEcsReimTravelApprNew")
    public CommonResult<?> cancelEcsReimTravelApprNew(@RequestBody EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteService.cancelEcsReimTravelApprNew(dto);
        return CommonResult.success();
    }

    /**
     * 更新公里数和页面图片信息
     * @param dto
     * @return
     */
    @ApiOperation("更新公里数和页面图片信息")
    @PostMapping("/updatePageImage")
    public CommonResult<?> updatePageImage(EcsReimTravelApprDto dto){
        ecsReimTravelApprWriteService.updatePageImage(dto);
        return CommonResult.success();
    }

    /**
     * 更新公里数
     * @param dto
     * @return
     */
    @ApiOperation("更新公里数")
    @PostMapping("/updateKil")
    public CommonResult<?> updateKil(@RequestBody EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteService.updateKil(dto);
        return CommonResult.success();
    }

    /**
     * 查询职能科室差旅、培训申请数量
     * @return
     */
    @ApiOperation("查询职能科室差旅、培训申请数量")
    @PostMapping("/queryTravelApplyWarnNum")
    public CommonResult<?> queryTravelApplyWarnNum(@RequestBody EcsReimTravelApprDto dto) {
        return CommonResult.success(ecsReimTravelApprReadService.queryTravelApplyWarnNum(dto));
    }

    /**
     * 查询职能科室差旅、培训报销数量
     * @return
     */
    @ApiOperation("查询职能科室差旅、培训待报销数量")
    @PostMapping("/queryTravelToReimWarnNum")
    public CommonResult<?> queryTravelToReimWarnNum(@RequestBody EcsReimTravelApprDto dto) {
        return CommonResult.success(ecsReimTravelApprReadService.queryTravelToReimWarnNum(dto));
    }

    /**
     * 查询临床医技科室差旅、培训申请数量
     * @return
     */
    @ApiOperation("查询临床医技差旅、培训申请数量")
    @PostMapping("/queryTrainingApplyWarnNum")
    public CommonResult<?> queryTrainingApplyWarnNum(@RequestBody EcsReimTravelApprDto dto) {
        return CommonResult.success(ecsReimTravelApprReadService.queryTrainingApplyWarnNum(dto));
    }

    /**
     * 查询临床医技科室差旅、培训报销数量
     * @return
     */
    @ApiOperation("查询临床医技科室差旅、培训待报销数量")
    @PostMapping("/queryTrainingToReimWarnNum")
    public CommonResult<?> queryTrainingToReimWarnNum(@RequestBody EcsReimTravelApprDto dto) {
        return CommonResult.success(ecsReimTravelApprReadService.queryTrainingToReimWarnNum(dto));
    }

    /**
     * 查询差旅申请审核数量
     * @return
     */
    @ApiOperation("查询差旅申请审核数量")
    @PostMapping("/queryTrainingAuditWarnNum")
    public CommonResult<?> queryTrainingAuditWarnNum(@RequestBody EcsReimTravelApprDto dto) {
        return CommonResult.success(ecsReimTravelApprReadService.queryTrainingAuditWarnNum(dto));
    }

    /**
     * 生成申请审批表
     * @param params
     * @return
     */
    @ApiOperation("生成申请审批表")
    @PostMapping("/generateTravelApprDoc")
    public CommonResult<?> generateTravelApprDoc(@RequestBody Map<String,Object> params) {
        return CommonResult.success(ecsReimTravelApprReadService.queryTravelApprDoc(params));
    }

    @ApiOperation("差旅申请审核收回")
    @PostMapping("/apprAuditTakeBack")
    public CommonResult<?> apprAuditTakeBack(@RequestBody EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteService.apprAuditTakeBack(dto);
        return CommonResult.success();
    }

    @ApiOperation("差旅申请审核退回")
    @PostMapping("/apprAuditSendBack")
    public CommonResult<?> apprAuditSendBack(@RequestBody EcsReimTravelApprDto dto) {
        ecsReimTravelApprWriteService.apprAuditSendBack(dto);
        return CommonResult.success();
    }
}
