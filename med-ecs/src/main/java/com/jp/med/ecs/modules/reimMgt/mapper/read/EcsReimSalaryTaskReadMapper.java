package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimSalaryTask;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Mapper
public interface EcsReimSalaryTaskReadMapper extends BaseMapper<EcsReimSalaryTask> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimSalaryTaskVo> queryList(EcsReimSalaryTask dto);

    List<EcsReimSalaryTaskDetailVo> querySalaryTaskDetail(EcsReimSalaryTask dto);

    List<EcsReimSalaryTaskVo> queryListNew(EcsReimSalaryTask dto);
}
