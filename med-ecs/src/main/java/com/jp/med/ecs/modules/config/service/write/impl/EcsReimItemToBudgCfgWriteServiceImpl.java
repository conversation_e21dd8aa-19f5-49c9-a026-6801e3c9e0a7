package com.jp.med.ecs.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ecs.modules.config.mapper.write.EcsReimItemToBudgCfgWriteMapper;
import com.jp.med.common.dto.ecs.EcsReimItemToBudgCfgDto;
import com.jp.med.ecs.modules.config.service.write.EcsReimItemToBudgCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报销项目对应预算项目
 * <AUTHOR>
 * @email -
 * @date 2023-12-12 09:43:44
 */
@Service
@Transactional(readOnly = false)
public class EcsReimItemToBudgCfgWriteServiceImpl extends ServiceImpl<EcsReimItemToBudgCfgWriteMapper, EcsReimItemToBudgCfgDto> implements EcsReimItemToBudgCfgWriteService {
}
