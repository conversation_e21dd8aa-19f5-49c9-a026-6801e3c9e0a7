package com.jp.med.ecs.modules.config.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.dto.ecs.EcsDrugAccountPeriodDto;
import com.jp.med.common.vo.EcsDrugAccountPeriodVo;
import com.jp.med.ecs.modules.config.mapper.read.EcsDrugAccountPeriodReadMapper;
import com.jp.med.ecs.modules.config.service.read.EcsDrugAccountPeriodReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsDrugAccountPeriodReadServiceImpl extends ServiceImpl<EcsDrugAccountPeriodReadMapper, EcsDrugAccountPeriodDto> implements EcsDrugAccountPeriodReadService {

    @Autowired
    private EcsDrugAccountPeriodReadMapper ecsDrugAccountPeriodReadMapper;

    @Override
    public List<EcsDrugAccountPeriodVo> queryList(EcsDrugAccountPeriodDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsDrugAccountPeriodReadMapper.queryList(dto);
    }

    @Override
    public List<EcsDrugAccountPeriodVo> queryPageList(EcsDrugAccountPeriodDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsDrugAccountPeriodReadMapper.queryList(dto);
    }

}
