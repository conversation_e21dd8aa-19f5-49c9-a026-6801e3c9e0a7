package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.dto.ecs.EcsResearchFundingTaskDto;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsResearchFundingTaskReadMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsResearchFundingTaskReadService;
import com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsResearchFundingTaskReadServiceImpl extends ServiceImpl<EcsResearchFundingTaskReadMapper, EcsResearchFundingTaskDto> implements EcsResearchFundingTaskReadService {

    @Autowired
    private EcsResearchFundingTaskReadMapper ecsResearchFundingTaskReadMapper;

    @Override
    public List<EcsResearchFundingTaskVo> queryList(EcsResearchFundingTaskDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsResearchFundingTaskReadMapper.queryList(dto);
    }

    @Override
    public List<EcsResearchFundingTaskVo> queryPageList(EcsResearchFundingTaskDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsResearchFundingTaskReadMapper.queryList(dto);
    }

    @Override
    public List<EcsResearchFundingTaskDetailVo> queryResearchTaskDetail(EcsResearchFundingTaskDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsResearchFundingTaskReadMapper.queryResearchTaskDetail(dto);
    }

    @Override
    public List<EcsResearchFundingTaskVo> queryResearchFundingBudget(EcsResearchFundingTaskDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsResearchFundingTaskReadMapper.queryResearchFundingBudget(dto);
    }
}
