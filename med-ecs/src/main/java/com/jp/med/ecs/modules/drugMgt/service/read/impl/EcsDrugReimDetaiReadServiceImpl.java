package com.jp.med.ecs.modules.drugMgt.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsStoinReadMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugReimDetaiReadMapper;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsDrugReimDetaiReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class EcsDrugReimDetaiReadServiceImpl extends ServiceImpl<EcsDrugReimDetaiReadMapper, EcsDrugReimDetaiDto> implements EcsDrugReimDetaiReadService {

    @Autowired
    private EcsDrugReimDetaiReadMapper ecsDrugReimDetaiReadMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Autowired
    private EcsStoinReadMapper ecsStoinReadMapper;

    @Override
    public List<EcsDrugReimDetaiVo> queryList(EcsDrugReimDetaiDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsDrugReimDetaiReadMapper.queryList(dto);
    }

    @Override
    public List<EcsDrugReimDetaiVo> queryListNoPage(EcsDrugReimDetaiDto dto) {
        return ecsDrugReimDetaiReadMapper.queryList(dto);
    }

    @Override
    public EcsDrugReimDetaiVo queryAppAuditDetail(EcsDrugReimDetaiDto dto) {
        // 查询审核流程
        dto.setSqlAutowiredHospitalCondition(true);
        List<AuditDetail> details = auditFeignService.getAuditDetails(new AuditDetail(dto.getAuditBchno(), dto.getSysUser().getHrmUser().getEmpCode(), OSSConst.BUCKET_ECS));

        List<AuditDetail> childrenDetails = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getBatchAudit()) && MedConst.TYPE_1.equals(dto.getBatchAudit())) {
            AuditDetail auditDetail = new AuditDetail();
            auditDetail.setAuditBchno(dto.getAuditBchno());
            auditDetail.setSys(OSSConst.BUCKET_ECS);
            childrenDetails = auditFeignService.queryChildren(auditDetail);
        }
        EcsDrugReimDetaiVo drugReimDetaiVo = new EcsDrugReimDetaiVo();
        details.sort(Comparator.comparing(AuditDetail::getChkSeq));
        drugReimDetaiVo.setAuditDetails(details);

        Map<String, AuditDetail> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(childrenDetails)) {
            // 获取所有未审核记录
            Map<String, List<AuditDetail>> collect = childrenDetails.stream().collect(Collectors.groupingBy(AuditDetail::getBchno));
            collect.forEach((s, auditDetails) -> {
                auditDetails.sort(Comparator.comparing(AuditDetail::getChkSeq));
                for (AuditDetail auditDetail : auditDetails) {
                    if (StringUtils.isEmpty(auditDetail.getChkTime()) && MedConst.TYPE_0.equals(auditDetail.getChkState())) {
                        map.put(s, auditDetail);
                        break;
                    }
                }
            });
        }

        dto.setChildrenDetails(childrenDetails);
        // 查询所有子审核对应的审核数据，且审核通过或待审核数据，不查询审核不通过数据
        List<EcsDrugReimDetaiVo> ecsDrugReimDetaiVos = ecsDrugReimDetaiReadMapper.queryAuditData(dto);
        ecsDrugReimDetaiVos.forEach(ecsDrugReimDetaiVo -> {
            AuditDetail auditDetail = map.get(ecsDrugReimDetaiVo.getAuditBchno());
            if (auditDetail != null) {
                ecsDrugReimDetaiVo.setId(auditDetail.getId());
            }
        });
        // 设置
        drugReimDetaiVo.setTableDetails(ecsDrugReimDetaiVos);
        return drugReimDetaiVo;
    }

    @Override
    public List<EcsDrugReimDetaiVo> queryAuditData(EcsDrugReimDetaiDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        dto.setSqlAutowiredHospitalCondition(true);
        dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
        return ecsDrugReimDetaiReadMapper.queryAuditCollData(dto);
    }
}
