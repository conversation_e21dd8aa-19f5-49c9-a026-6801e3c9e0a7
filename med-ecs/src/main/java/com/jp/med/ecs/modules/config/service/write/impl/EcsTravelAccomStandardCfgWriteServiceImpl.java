package com.jp.med.ecs.modules.config.service.write.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jp.med.common.exception.AppException;
import com.jp.med.ecs.modules.config.mapper.read.EcsTravelAccomStandardCfgReadMapper;
import com.jp.med.ecs.modules.config.vo.EcsTravelAccomStandardCfgVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ecs.modules.config.mapper.write.EcsTravelAccomStandardCfgWriteMapper;
import com.jp.med.ecs.modules.config.dto.EcsTravelAccomStandardCfgDto;
import com.jp.med.ecs.modules.config.service.write.EcsTravelAccomStandardCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 差旅住宿费标准
 * <AUTHOR>
 * @email -
 * @date 2024-05-23 11:12:00
 */
@Service
@Transactional(readOnly = false)
public class EcsTravelAccomStandardCfgWriteServiceImpl extends ServiceImpl<EcsTravelAccomStandardCfgWriteMapper, EcsTravelAccomStandardCfgDto> implements EcsTravelAccomStandardCfgWriteService {

    @Autowired
    private EcsTravelAccomStandardCfgWriteMapper ecsTravelAccomStandardCfgWriteMapper;

    @Autowired
    private EcsTravelAccomStandardCfgReadMapper ecsTravelAccomStandardCfgReadMapper;

    /**
     * 保存差旅住宿费标准
     * @param dto
     */
    @Override
    public void saveTravelAccomStandard(EcsTravelAccomStandardCfgDto dto) {
        //判断是否有重复数据
        LambdaQueryWrapper<EcsTravelAccomStandardCfgDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EcsTravelAccomStandardCfgDto::getEvectionAddr,dto.getEvectionAddr());

        EcsTravelAccomStandardCfgDto cfgDto = ecsTravelAccomStandardCfgReadMapper.selectOne(queryWrapper);

        if (!Objects.isNull(cfgDto)){
            throw new AppException("当前地区差旅费标准已存在");
        }

        ecsTravelAccomStandardCfgWriteMapper.insert(dto);
    }
}
