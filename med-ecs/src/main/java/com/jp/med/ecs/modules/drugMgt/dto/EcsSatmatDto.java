package com.jp.med.ecs.modules.drugMgt.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 卫材入库单
 * <AUTHOR>
 * @email -
 * @date 2024-07-26 15:06:37
 */
@Data
@TableName("ecs_satmat" )
public class EcsSatmatDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 单号 */
    @TableField("stoin_num")
    private Integer stoinNum;

    /** 入库时间 */
    @TableField("stoin_date")
    private String stoinDate;

    /** 卫材编码 */
    @TableField("satmat_code")
    private String satmatCode;

    /** 卫材名称 */
    @TableField("satmat_name")
    private String satmatName;

    /** 规格 */
    @TableField("specs")
    private String specs;

    /** 型号 */
    @TableField("pattern")
    private String pattern;

    /** 单位 */
    @TableField("unit")
    private String unit;

    /** 批号 */
    @TableField("batch_num")
    private String batchNum;

    /** 生产日期 */
    @TableField("prod_date")
    private String prodDate;

    /** 有效期 */
    @TableField("valid_date")
    private String validDate;

    /** 卫材数量 */
    @TableField("satmat_num")
    private Double satmatNum;

    /** 单价 */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /** 总金额 */
    @TableField("sumamt")
    private BigDecimal sumamt;

    /** 厂家 */
    @TableField("factory")
    private String factory;

    /** 供应商 */
    @TableField("spler")
    private String spler;

    /** 货主 */
    @TableField("consignor")
    private String consignor;

    /** 同步日期 **/
    @TableField("sync_date")
    private String syncDate;

    /** 报销id **/
    @TableField("satmat_reim_detail_id")
    private Integer satmatReimDetailId;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 文件 */
    @TableField("att")
    private String att;

    /** 文件名称 */
    @TableField("att_name")
    private String attName;

    /** 报销标志 **/
    @TableField("reim_flag")
    private String reimFlag;

    /** issue */
    @TableField(exist = false)
    private String issue;

    /** 年份 */
    @TableField(exist = false)
    private String year;

    /** 是否分页 */
    @TableField(exist = false)
    private Boolean paging;

}
