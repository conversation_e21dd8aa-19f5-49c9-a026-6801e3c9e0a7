package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsInvoChkRcdDetailEntity;
import com.jp.med.ecs.modules.reimMgt.entity.EcsInvoChkRcdEntity;
import com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
@Mapper
public interface EcsInvoRcdReadMapper extends BaseMapper<EcsInvoRcdDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsInvoRcdVo> queryList(EcsInvoRcdDto dto);

    /**
     * 查询已经存在的记录
     * @param tempRcds
     * @return
     */
    List<EcsInvoRcdVo> queryExistsRcd(List<EcsInvoRcdDto> tempRcds);

    /**
     * 通过哈希值查询已经存在的记录
     * @param tempRcds
     * @return
     */
    List<EcsInvoRcdVo> queryExistsRcdByHash(List<EcsInvoRcdDto> tempRcds);

    /**
     * 查询是否已经报销
     * @param ids
     * @return
     */
    int queryAlreadyReim(List<Long> ids);

    /**
     * 通过发票id查询校验记录
     * @param dto
     * @return
     */
    List<EcsInvoChkRcdEntity> queryChkRcdByInvoId(EcsInvoRcdDto dto);

    /**
     * 通过发票id查询校验记录明细
     * @param dto
     * @return
     */
    List<EcsInvoChkRcdDetailEntity> queryChkRcdDetailByInvoId(EcsInvoRcdDto dto);

    /**
     * 查询校正发票文件是否已经存在
     * @param ids
     * @return
     */
    int queryAlreadyAmendRcd(List<Integer> ids);

    /**
     * 查询发票记录通过文件唯一标志
     * @param fileIdentifier
     * @return
     */
    EcsInvoRcdVo queryEcsInvoRcdByFileIdentifier(@Param("fileIdentifier") String fileIdentifier);
}
