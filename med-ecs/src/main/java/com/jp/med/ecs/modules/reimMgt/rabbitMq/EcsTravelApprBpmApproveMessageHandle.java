package com.jp.med.ecs.modules.reimMgt.rabbitMq;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimTravelApprWriteMapper;
import com.rabbitmq.client.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Getter
@Setter
public class EcsTravelApprBpmApproveMessageHandle extends AbstractBpmApproveMessageHandle {

    // @Override
    // public String[] getProcessIdentifier() {
    // return new String[] { "oa_leave" };
    // }

    @Autowired
    private EcsReimTravelApprWriteMapper ecsReimTravelApprWriteMapper;

    public String[] processIdentifier = {"ECS_DUTY_TRIP_APPR_TRIP","ECS_CLINICAL_TRIP_APPR_TRIP","ECS_FUN_DEPT_TRAVEL_APPR","ECS_KJ_SHORT_TRAIN_APPR"};

    @Override
    @RabbitListener(queues = {"ECS_DUTY_TRIP_APPR_TRIP","ECS_CLINICAL_TRIP_APPR_TRIP","ECS_FUN_DEPT_TRAVEL_APPR","ECS_KJ_SHORT_TRAIN_APPR"})
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        receiveMessage0(msg);

        // try {
        // // 处理消息
        //
        // System.out.println(Arrays.toString(message.getBody()));
        // System.out.println(msg);
        // receiveMessage0(msg);
        //
        // // 手动确认消息
        // channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        //// throw new Exception();
        // } catch (Exception e) {
        // log.info("异常{}:{}",msg.getProcessDefinitionKey(),msg.getBusinessKey());
        // // 处理异常，选择是否重试或拒绝消息
        // channel.basicNack(message.getMessageProperties().getDeliveryTag(), false,
        // true);
        // }
    }

    /**
     * 处理创建的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {

    }

    // @RabbitListener(queues = processIdentifier)
    // public void receiveMessage(BpmProcessInstanceStatus message) {
    //
    // receiveMessage0(message);
    //
    // }

    /**
     * 处理审批通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {
        //更新当前状态为已通过
        LambdaUpdateWrapper<EcsReimTravelApprDto> updateWrapper = Wrappers.lambdaUpdate();
        String businessKey = message.getBusinessKey();
        String[] split = businessKey.split(",");
        List<Long> collect = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
        updateWrapper.set(EcsReimTravelApprDto::getStatus, EcsConst.APPR_STATUS_AUDIT_SUCCESS)
                .in(EcsReimTravelApprDto::getId,collect);
        ecsReimTravelApprWriteMapper.update(null,updateWrapper);
    }

    /**
     * 处理审批不通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        //更新当前状态为拒绝
        LambdaUpdateWrapper<EcsReimTravelApprDto> updateWrapper = Wrappers.lambdaUpdate();
        String businessKey = message.getBusinessKey();
        String[] split = businessKey.split(",");
        List<Long> collect = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
        updateWrapper.set(EcsReimTravelApprDto::getStatus, EcsConst.APPR_STATUS_AUDIT_FAILD)
                .in(EcsReimTravelApprDto::getId,collect);
        ecsReimTravelApprWriteMapper.update(null,updateWrapper);
    }

    /**
     * 处理审批中的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {

    }

    /**
     * 处理已取消的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {

    }

    // /**
    // * 处理已退回的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleReturned(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理委派中的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleDelegated(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理审批通过中的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleApproving(BpmProcessInstanceStatus message) {
    //
    // }
    //
    // /**
    // * 处理待审批的逻辑。
    // *
    // * @param message 包含 BPM 审批实例状态的消息对象
    // */
    // @Override
    // protected void handleWaiting(BpmProcessInstanceStatus message) {
    //
    // }

}
