package com.jp.med.ecs.modules.reimMgt.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.dto.ecs.EcsReimSalaryTask;
import com.jp.med.common.dto.ecs.EcsReimSalaryTaskDetail;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimSalaryTaskReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimSalaryTaskWriteMapper;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimSalaryTaskWriteService;
import io.seata.common.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Service
@Transactional(readOnly = false)
public class EcsReimSalaryTaskWriteServiceImpl extends ServiceImpl<EcsReimSalaryTaskWriteMapper, EcsReimSalaryTask> implements EcsReimSalaryTaskWriteService {

    @Autowired
    private EcsReimSalaryTaskWriteMapper ecsReimSalaryTaskWriteMapper;

    @Autowired
    private EcsReimSalaryTaskReadMapper ecsReimSalaryTaskReadMapper;


    @Override
    public void saveSalaryTask(EcsReimSalaryTask dto) {
        List<EcsReimSalaryTaskDetail> details = dto.getDetails();
        if (Objects.isNull(details) || CollectionUtils.isEmpty(details)) {
            throw new AppException(EcsConst.SALARY_TASK_DETAILS_NOT_EXIST);
        }
        //保存工资任务
        dto.setId(null);
        ecsReimSalaryTaskWriteMapper.insert(dto);
        //保存工资任务明细
        details.stream().forEach(item -> {
            item.setTaskId(dto.getId());
        });
        BatchUtil.batch("insertSalaryTaskDetail",details, EcsReimSalaryTaskWriteMapper.class);
    }


}
