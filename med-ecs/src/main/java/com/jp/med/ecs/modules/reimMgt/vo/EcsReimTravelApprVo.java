package com.jp.med.ecs.modules.reimMgt.vo;

import com.jp.med.common.entity.audit.AuditCommonRes;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报销差旅审批
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 14:02:10
 */
@Data
public class EcsReimTravelApprVo extends AuditCommonRes {

	/** id */
	private Long id;

	/** 申请时间 */
	private String appyerTime;

	/** 申请人 */
	private String appyer;

	/** 申请人 */
	private String appyerName;

	/** 出差事由 */
	private String evectionRea;

	/** 出差开始时间 */
	private String evectionBegnTime;

	/** 出差结束时间 */
	private String evectionEndTime;

	/** 出差时间 */
	private String evectionTime;;

	/** 出差地点 */
	private Integer evectionAddr;

	/** 出差地点详情 */
	private String evectionDetlAddr;

	/** 是否绕道 */
	private String detourOrNot;

	/** 公里数 */
	private BigDecimal kil;

	/** 交通方式 */
	private String trnp;

	/** 交通方式号码（如汽车为车牌号） */
	private String trnpNum;

	/** 是否安排伙食 */
	private String food;

	/** 是否安排住宿 */
	private String stay;

	/** 承诺 */
	private String prse;

	/** 预计差旅金额 */
	private BigDecimal planAmt;

	/** 预计培训金额 **/
	private BigDecimal planAmt2;

	/** 预计租车费金额 **/
	private BigDecimal planAmt3;

	/** 医疗机构id */
	private String hospitalId;

	/** 类型 */
	private String type;

	/** 文件名称 */
	private String attName;

	/** 文件 */
	private String att;

	/** 审核批次号 */
	private String auditBchno;

	/** 审核批次号 */
	private String pageImage;

	/** 报销标志 */
	private String reimFlag;

	/** 出差性质 */
	private String busMet;

	/** 1:职能科室 2:临床、医技 **/
	private String apprDeptType;

	/** 审核流程id **/
	private Integer chkerFlow;

	/** 审核标志,1:标识当前记录审核，0：标识当前记录不审核 */
	private String auditFlag;

	/** 申请科室 **/
	private String apprDept;

	/** 审核状态 1:成功，2：失败，:3：审核中 **/
	private String auditState;

	/** 出差范围 1：省内 2：省外 **/
	private String travelRange;

	/** 申请状态 **/
	private String status;

	/** 聘任职称 **/
	private String engageRank;

	/**
	 * 对应的流程编号
	 * <p>
	 * 关联 ProcessInstance 的 id 属性
	 */
	private String processInstanceId;

	/** 自驾事由 **/
	private String selfDriveRea;
}
