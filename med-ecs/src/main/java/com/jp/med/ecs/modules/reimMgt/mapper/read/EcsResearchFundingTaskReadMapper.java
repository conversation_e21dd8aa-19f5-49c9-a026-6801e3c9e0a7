package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsResearchFundingTaskDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 科研经费报销任务表
 * <AUTHOR>
 * @email -
 * @date 2024-11-26 05:30:48
 */
@Mapper
public interface EcsResearchFundingTaskReadMapper extends BaseMapper<EcsResearchFundingTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsResearchFundingTaskVo> queryList(EcsResearchFundingTaskDto dto);

    List<EcsResearchFundingTaskDetailVo> queryResearchTaskDetail(EcsResearchFundingTaskDto dto);

    List<EcsResearchFundingTaskVo> queryResearchFundingBudget(EcsResearchFundingTaskDto dto);
}
