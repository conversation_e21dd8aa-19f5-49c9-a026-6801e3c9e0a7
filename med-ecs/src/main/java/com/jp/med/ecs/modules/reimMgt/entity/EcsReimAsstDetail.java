package com.jp.med.ecs.modules.reimMgt.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1 20:41
 * @description:
 */
@Data
public class EcsReimAsstDetail {

    /** 报销详情id */
    private Long reimDetailId;

    /** 支付类型代码 */
    private String payTypeCode;

    /** 支付类型名称 */
    private String payTypeName;

    /** 会计科目代码 */
    private String actigSubCode;

    /** 会计科目名称 */
    private String actigSubName;

    /** 科室代码 */
    private String deptCode;

    /** 科室名称 */
    private String deptName;

    /** 往来单位代码 */
    private String relCoCode;

    /** 往来单位名称 */
    private String relCoName;

    /** 功能科目代码 */
    private String funSubCode;

    /** 功能科目名称 */
    private String funSubName;

    /** 经济科目代码 */
    private String econSubCode;

    /** 经济科目名称 */
    private String econSubName;

    /** 项目代码 */
    private String projCode;

    /** 项目名称 */
    private String projName;

    /** 现金流量代码 */
    private String cashFlowCode;

    /** 现金流量名称 */
    private String cashFlowName;

    /** 金额类型 */
    private String actigAmtType;

    /** 金额 */
    private BigDecimal actigAmt;

    /** 创建人 */
    private String crter;

    /** 创建时间 */
    private String createTime;

    /** 医疗机构id */
    private String hospitalId;

    /** 会计体系 */
    private String actigSys;

    /** 摘要 */
    private String abst;

    /** 上级类型 **/
    private String supType;
}
