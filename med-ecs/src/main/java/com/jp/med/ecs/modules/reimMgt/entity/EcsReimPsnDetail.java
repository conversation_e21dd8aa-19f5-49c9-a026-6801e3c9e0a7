package com.jp.med.ecs.modules.reimMgt.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/11 20:31
 * @description: 报销人员详情
 */
@Data
public class EcsReimPsnDetail {

    /** id */
    private Long id;

    /** 报销详情id */
    private Long reimDetailId;

    /** 科室 */
    private String dept;

    /** 出差人员 */
    private String tripPsn;

    /** 出差金额 */
    private BigDecimal reimAmt;

    /** 类型(1:差旅申请,2:报销) */
    private String type;

    /** 科室名称 */
    private String deptName;

    /** 出差人员名称 */
    private String tripPsnName;

    /** 出差人员性别 **/
    private String sex;
}
