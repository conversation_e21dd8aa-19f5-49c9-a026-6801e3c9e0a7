package com.jp.med.ecs.modules.reimMgt.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.ecs.modules.reimMgt.entity.*;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 报销明细
 * <AUTHOR>
 * @email -
 * @date 2023-12-04 22:01:14
 */
@Data
@TableName("ecs_reim_detail" )
public class EcsReimDetailDto extends CommonQueryDto {

    /** id */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 申请人 */
    @TableField("appyer")
    private String appyer;

    /** 申请科室 */
    @TableField("appyer_dept")
    private String appyerDept;

    /** 出差时间 */
    @TableField(exist = false)
    private String evectionTime;

    /** 出差开始时间 */
    @TableField("evection_begn_time")
    private String evectionBegnTime;

    /** 出差结束时间 */
    @TableField("evection_end_time")
    private String evectionEndTime;

    /** 申请时间 */
    @TableField("appyer_time")
    private String appyerTime;

    /** 预算控制 */
    @TableField("budg_ctrl")
    private String budgCtrl;

    /** 出差地点 */
    @TableField("evection_addr")
    private String evectionAddr;

    /** 出差事由 */
    @TableField("evection_rea")
    private String evectionRea;

    /** 自驾事由 **/
    @TableField("self_drive_rea")
    private String selfDriveRea;

    /** 开户银行 */
    @TableField("bank")
    private String bank;

    /** 户名 */
    @TableField("acctname")
    private String acctname;

    /** 银行账(卡)号 */
    @TableField("bankcode")
    private String bankcode;

    /** 收款乙方名称 **/
    @TableField("opposite_name")
    private String oppositeName;

    /** 合计金额小写(元) */
    @TableField("sum")
    private BigDecimal sum;

    /** 合计金额大写 */
    @TableField("cap_sum")
    private String capSum;

    /** 审核批次号 */
    @TableField("audit_bchno")
    private String auditBchno;

    /** 业务状态 */
    @TableField("busstas")
    private String busstas;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 类型 1：差率费，2：科研，3费用报销 4.分摊*/
    @TableField("type")
    private String type;

    /** 分摊类型 1.水费 2.电费 */
    @TableField("share_type")
    private String shareType;

    /** 出差性质 */
    @TableField("bus_met")
    private String busMet;

    /** 差旅申请app */
    @TableField("travel_appr_id")
    private Long travelApprId;

    /** 科研项目id **/
    @TableField("project_id")
    private Integer projectId;

    /** 经费蕾姐id **/
    @TableField("funding_id")
    private Integer fundingId;

    /** 付款方式 **/
    @TableField("pay_method")
    private String payMethod;

    /** 工资申请任务id 对应人力资源系统的任务id  唯一**/
    @TableField(exist = false)
    private Integer salaryId;

    /** 合同编码 对应合同模块的编码 唯一 **/
    @TableField(exist = false)
    private String ctCode;

    /** 零星采购项目编码 采购模块项目编码 唯一 **/
    @TableField(exist = false)
    private List<String> itemNos;

    /** 零星采购任务id 报销模块的任务表id  多个以逗号分割 **/
    @TableField(exist = false)
    private String purcDetailIds;

    /** 审核流程id **/
    @TableField("chker_flow")
    private Integer chkerFlow;

    /**
     * 分摊总额
     */
    @TableField("share_amt")
    private BigDecimal shareAmt;

    /**
     * 分摊月份
     */
    @TableField("share_date")
    private String shareDate;

    /**
     * 文件记录编码: 模块编码+随机字符串
     */
    @TableField("att_code")
    private String attCode;

    /**
     * 资金类型
     */
    @TableField("fund_type")
    private String fundType;

    /** 页面图片 */
    @TableField("page_image")
    private String pageImage;

    /** 页面图片文件 */
    @TableField(exist = false)
    private MultipartFile pageImageFile;

    /** 是否审核 */
    @TableField(exist = false)
    private Boolean audit;

    /** tab类型 */
    @TableField(exist = false)
    private String auditFlag;

    /** 出差地址 */
    @TableField(exist = false)
    private String evectionAddrName;

    /** 附件 */
    @TableField(exist = false)
    private List<MultipartFile> attFiles;

    /** 项目详情 */
    @TableField(exist = false)
    private List<EcsReimItemDetail> itemDetails;

    /** 补助项目详情 */
    @TableField(exist = false)
    private List<EcsReimSubsItemDetail> subsItemDetails;

    /** 出差人 */
    @TableField(exist = false)
    private List<EcsReimPsnDetail> psnDetails;

    /** 往来单位信息 */
    @TableField(exist = false)
    private List<EcsReimRelCoDetail> relCoDetails;

    /** 文件对应发票id的map */
    @TableField(exist = false)
    private Map<String, Long> fileIdentifierMap;

    /** 报销辅项信息 */
    @TableField(exist = false)
    private List<EcsReimAsstDetail> reimAsstDetails;

    /** 审核状态 */
    @TableField(exist = false)
    private List<String> auditState;

    /** 申请部门类型 **/
    @TableField(exist = false)
    private String apprDeptType;

    /** 付款期数id **/
    @TableField(exist = false)
    private Integer paymentId;

    /** 分摊发票 **/
    @TableField(exist = false)
    private List<MultipartFile> shareInvos;

    /** 附件 **/
    @TableField("att")
    private String att;

    /** 附件名称 **/
    @TableField("att_name")
    private String attName;

    /** 发票记录表id **/
    @TableField("invo_id")
    private String invoId;

    /** 年 **/
    @TableField(exist = false)
    private String year;

    /** 月 **/
    @TableField(exist = false)
    private String month;

    @TableField(exist = false)
    private List<Integer> ids;

    /** 申请id **/
    @TableField(exist = false)
    private Integer apprId;

    /** 报销id **/
    @TableField(exist = false)
    private Integer reimId;

    /** 是否了临时保存 **/
    @TableField(exist = false)
    private String tempSave;

    /**
     * 报销状态 对应EcsConst报销状态
     */
    @TableField(exist = false)
    private String reimStatus;

    /**
     * 是否冲抵借款(冲抵借款不需要上传付款证明文件)
     */
    @TableField("is_loan")
    private String isLoan;

    /**
     * 冲抵借款报销id
     */
    @TableField("loan_reim_id")
    private Integer loanReimId;

    /**
     * 冲抵借款金额
     */
    @TableField("loan_amt")
    private BigDecimal loanAmt;

    /** 付款证明文件id ，用于多个报销对应相同付款文件 **/
    @TableField("pay_rcpt_id")
    private Integer payRcptId;

    /**
     * 是否有凭证
     */
    @TableField("has_pz")
    private String hasPz;

    /**
     * 发票来源  1:hrp 2:供应商 3：卫材
     */
    @TableField(exist = false)
    private String invoFrom;

    /** 付款证明文件 */
    @TableField(exist = false)
    private List<MultipartFile> payRcptFiles;

    //------BPM流程需要参数START--------
    @TableField(exist = false)
    private Map<String,String> bpmParams;

    /**
     * 发起人自选审批人 Map
     * <p>
     * key：taskKey 任务编码
     * value：审批人的数组
     * 例如：{ taskKey1 :[1, 2] }，则表示 taskKey1 这个任务，提前设定了，由 use    rId 为 1,2 的用户进行审批
     */
    @TableField(exist = false)
    private Map<String, List<String>> startUserSelectAssignees;

    /**
     * 对应的流程编号
     * <p>
     * 关联 ProcessInstance 的 id 属性
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    //------BPM流程需要参数-END--------


    @TableField(exist = false)
    private List<Long> ecsReimTravelApprIds;

    /** 项目明细关键字查询 **/
    @TableField(exist = false)
    private String itemKeyWord;

}
