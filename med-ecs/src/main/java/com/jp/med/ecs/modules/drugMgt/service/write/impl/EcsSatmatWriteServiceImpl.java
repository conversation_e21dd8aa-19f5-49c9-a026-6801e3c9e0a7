package com.jp.med.ecs.modules.drugMgt.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatDto;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsSatmatReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsSatmatWriteMapper;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsSatmatWriteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Reader;
import java.util.List;

/**
 * 卫材入库单
 * <AUTHOR>
 * @email -
 * @date 2024-07-26 15:06:37
 */
@Service
@Transactional(readOnly = false)
@Slf4j
public class EcsSatmatWriteServiceImpl extends ServiceImpl<EcsSatmatWriteMapper, EcsSatmatDto> implements EcsSatmatWriteService {

    @Autowired
    private EcsSatmatReadMapper ecsSatmatReadMapper;

    @Autowired
    private EcsSatmatWriteMapper ecsSatmatWriteMapper;

    private static final int BATCH_SIZE = 1000; // 每次批处理的大小
    /**
     * 同步卫材入库单
     * @param dto
     */
    @Override
    public void sanMatSync(EcsSatmatDto dto) {

        //获取当前入库单最大单号
        int floor = ecsSatmatReadMapper.queryFloor();
        log.info("--------当前floor-------"+ floor);
        String resource = "winning-mybatis-config.xml";
        Reader reader;

        SqlSessionFactory sqlSessionFactorySource = null;
        try{
            reader = Resources.getResourceAsReader(resource);
            SqlSessionFactoryBuilder builder = new SqlSessionFactoryBuilder();
            sqlSessionFactorySource = builder.build(reader,"sanMat");
        } catch(Exception e) {
            log.error("读取数据失败");
            throw new AppException("创建连接失败");
        }

        try(SqlSession sessionSource = sqlSessionFactorySource.openSession()){

            EcsSatmatReadMapper sourceMapper = sessionSource.getMapper(EcsSatmatReadMapper.class);

            int startRow = 1;
            int endRow = BATCH_SIZE;
            List<EcsSatmatDto> satmats;

            do{
                satmats = sourceMapper.listSatMats(floor,startRow,endRow);

                if (!satmats.isEmpty()) {
                    //插入
                    BatchUtil.batch("saveSatmat",satmats,EcsSatmatWriteMapper.class);

                    startRow += BATCH_SIZE;
                    endRow += BATCH_SIZE;
                }

            }while(!satmats.isEmpty());
        } catch (Exception e) {
            log.error("卫材同步数据失败",e);
            throw new AppException("卫材同步数据失败");
        }
    }
}
