package com.jp.med.ecs.modules.config.mapper.write;

import com.jp.med.ecs.modules.config.dto.EcsActigCfgDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 会计科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Mapper
public interface EcsActigCfgWriteMapper extends BaseMapper<EcsActigCfgDto> {
    void saveActigCfg(List<EcsActigCfgDto> efcDtos);
}
