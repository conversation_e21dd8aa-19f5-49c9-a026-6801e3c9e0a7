package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimPurcTask;
import com.jp.med.common.vo.EcsReimPurcTaskDetailVo;
import com.jp.med.common.vo.EcsReimPurcTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 零星采购报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Mapper
public interface EcsReimPurcTaskReadMapper extends BaseMapper<EcsReimPurcTask> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimPurcTaskVo> queryList(EcsReimPurcTask dto);

    List<EcsReimPurcTaskDetailVo> queryPurcTaskDetail(EcsReimPurcTask dto);

    List<EcsReimPurcTaskDetailVo> queryPurcTaskDetailByReimId(Integer reimId);

    /**
     * 查询明细
     * @param taskIds
     * @return
     */
    List<EcsReimPurcTaskDetailVo> queryPurcTaskDetails(@Param("taskIds") List<Integer> taskIds,@Param("dto") EcsReimPurcTask dto);

    /**
     * 根据明细ID查询明细
     * @param purcDetailIds
     * @return
     */
    List<EcsReimPurcTaskDetailVo> selectListByDetailIds(@Param("purcDetailIds") List<Integer> purcDetailIds);

    List<EcsReimPurcTaskVo> queryPurcTaskByReimId(EcsReimPurcTask dto);
}
