package com.jp.med.ecs.modules.config.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.util.TreeNewUtil;
import com.jp.med.common.util.TreeUtil;
//import de.odysseus.el.tree.Tree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsEconFunSubCfgReadMapper;
import com.jp.med.ecs.modules.config.dto.EcsEconFunSubCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsEconFunSubCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsEconFunSubCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsEconFunSubCfgReadServiceImpl extends ServiceImpl<EcsEconFunSubCfgReadMapper, EcsEconFunSubCfgDto> implements EcsEconFunSubCfgReadService {

    @Autowired
    private EcsEconFunSubCfgReadMapper ecsEconFunSubCfgReadMapper;

    @Override
    public List<EcsEconFunSubCfgVo> queryList(EcsEconFunSubCfgDto dto) {
        TreeNewUtil<String, EcsEconFunSubCfgVo> treeNewUtil = new TreeNewUtil<>();
        return treeNewUtil.buildTree(ecsEconFunSubCfgReadMapper.queryList(dto));
    }

}
