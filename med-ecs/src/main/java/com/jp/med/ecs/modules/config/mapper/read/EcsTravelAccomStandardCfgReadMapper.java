package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.ecs.modules.config.dto.EcsTravelAccomStandardCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsTravelAccomStandardCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 差旅住宿费标准
 * <AUTHOR>
 * @email -
 * @date 2024-05-23 11:12:00
 */
@Mapper
public interface EcsTravelAccomStandardCfgReadMapper extends BaseMapper<EcsTravelAccomStandardCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsTravelAccomStandardCfgVo> queryList(EcsTravelAccomStandardCfgDto dto);
}
