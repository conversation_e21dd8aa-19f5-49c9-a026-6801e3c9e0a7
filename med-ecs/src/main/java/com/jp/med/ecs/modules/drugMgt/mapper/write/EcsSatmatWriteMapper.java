package com.jp.med.ecs.modules.drugMgt.mapper.write;

import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatReimDetailDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * 卫材入库单
 * <AUTHOR>
 * @email -
 * @date 2024-07-26 15:06:37
 */
@Mapper
public interface EcsSatmatWriteMapper extends BaseMapper<EcsSatmatDto> {

    void saveSatmat(EcsSatmatDto dto);

    void updateReimState(EcsSatmatReimDetailDto ecsSatmatReimDetaiDto);
}
