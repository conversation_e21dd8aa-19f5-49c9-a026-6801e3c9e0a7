package com.jp.med.ecs.modules.config.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 差旅住宿费标准
 * <AUTHOR>
 * @email -
 * @date 2024-05-23 11:12:00
 */
@Data
@TableName("ecs_travel_accom_standard_cfg" )
public class EcsTravelAccomStandardCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 出差地区 */
    @TableField("evection_addr")
    private Integer evectionAddr;

    /** 住宿费标准价格 */
    @TableField("standard_price")
    private BigDecimal standardPrice;

    /** 旺季期间 */
    @TableField(value = "peak_season",updateStrategy= FieldStrategy.IGNORED)
    private String peakSeason;

    /** 旺季期间价格 */
    @TableField(value = "peak_season_price" ,updateStrategy= FieldStrategy.IGNORED)
    private BigDecimal peakSeasonPrice;

}
