package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.ChineseCharToEnUtil;
import com.jp.med.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ecs.modules.config.dto.EcsActigAsstCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsActigAsstCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsActigAsstCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 会计科目辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 15:45:43
 */
@Api(value = "会计科目辅助信息配置", tags = "会计科目辅助信息配置")
@RestController
@RequestMapping("ecsActigAsstCfg")
public class EcsActigAsstCfgController {

    @Autowired
    private EcsActigAsstCfgReadService ecsActigAsstCfgReadService;

    @Autowired
    private EcsActigAsstCfgWriteService ecsActigAsstCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询会计科目辅助信息配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsActigAsstCfgDto dto){
        return CommonResult.success(ecsActigAsstCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增会计科目辅助信息配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsActigAsstCfgDto dto){
        dto.setCrter(dto.getSysUser().getUsername());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        if (StringUtils.isEmpty(dto.getPinyin())) {
            dto.setPinyin(ChineseCharToEnUtil.getAllFirstLetter(dto.getAsstName()).toUpperCase());
        }
        ecsActigAsstCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改会计科目辅助信息配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsActigAsstCfgDto dto){
        dto.setModiTime(DateUtil.getCurrentTime(null));
        ecsActigAsstCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除会计科目辅助信息配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsActigAsstCfgDto dto){
        ecsActigAsstCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
