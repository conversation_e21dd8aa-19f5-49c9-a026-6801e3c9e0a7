package com.jp.med.ecs.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ecs.modules.config.mapper.write.EcsActigAsstDeptCfgWriteMapper;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstDeptCfgDto;
import com.jp.med.ecs.modules.config.service.write.EcsActigAsstDeptCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会计科目科室辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2024-02-01 16:36:57
 */
@Service
@Transactional(readOnly = false)
public class EcsActigAsstDeptCfgWriteServiceImpl extends ServiceImpl<EcsActigAsstDeptCfgWriteMapper, EcsActigAsstDeptCfgDto> implements EcsActigAsstDeptCfgWriteService {
}
