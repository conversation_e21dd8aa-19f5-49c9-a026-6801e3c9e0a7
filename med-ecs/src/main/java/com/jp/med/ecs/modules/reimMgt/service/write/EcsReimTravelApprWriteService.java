package com.jp.med.ecs.modules.reimMgt.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.dto.SaveMultiTripDto;

/**
 * 报销差旅审批
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 13:48:41
 */
public interface EcsReimTravelApprWriteService extends IService<EcsReimTravelApprDto> {

    /**
     * 新增报销差旅审批
     * @param dto
     */
    void saveReimTravelAppr(EcsReimTravelApprDto dto);

    /**
     * 新增报销差旅审批
     * @param dto
     */
    void saveReimTravelApprNew(EcsReimTravelApprDto dto);

    /**
     * 更新公里数和页面图片信息
     * @param dto
     * @return
     */
    void updatePageImage(EcsReimTravelApprDto dto);

    /**
     * 更新公里数
     * @param dto
     */
    void updateKil(EcsReimTravelApprDto dto);

    /**
     * 删除未开始审核的差旅申请
     * @param dto
     */
    void deleteNoAudit(EcsReimTravelApprDto dto);

    void deleteNoAuditNew(EcsReimTravelApprDto dto);

    void apprAuditTakeBack(EcsReimTravelApprDto dto);

    void apprAuditSendBack(EcsReimTravelApprDto dto);

    void cancelEcsReimTravelApprNew(EcsReimTravelApprDto dto);

    /**
     * 保存 出差申请 多行程
     *
     * @param dto DTO
     */
    void saveReimTravelApprMultiTrip(SaveMultiTripDto dto);

    void deleteNoAuditMultiTrip(EcsReimTravelApprDto dto);

    void cancelEcsReimTravelApprMultiTrip(EcsReimTravelApprDto dto);
}

