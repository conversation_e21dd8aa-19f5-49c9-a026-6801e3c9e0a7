package com.jp.med.ecs.modules.drugMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo;

import java.util.List;

/**
 * 药品报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:45
 */
public interface EcsDrugReimDetaiReadService extends IService<EcsDrugReimDetaiDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsDrugReimDetaiVo> queryList(EcsDrugReimDetaiDto dto);

    /**
     * 查询列表-不分页
     * @param dto
     * @return
     */
    List<EcsDrugReimDetaiVo> queryListNoPage(EcsDrugReimDetaiDto dto);

    /**
     * 查询APP审核详情
     * @param dto
     * @return
     */
    EcsDrugReimDetaiVo queryAppAuditDetail(EcsDrugReimDetaiDto dto);

    /**
     * 查询审核数据
     * @param dto
     * @return
     */
    List<EcsDrugReimDetaiVo> queryAuditData(EcsDrugReimDetaiDto dto);
}

