package com.jp.med.ecs.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.common.util.TreeNewUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsItemCfgReadMapper;
import com.jp.med.ecs.modules.config.dto.EcsItemCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsItemCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsItemCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsItemCfgReadServiceImpl extends ServiceImpl<EcsItemCfgReadMapper, EcsItemCfgDto> implements EcsItemCfgReadService {

    @Autowired
    private EcsItemCfgReadMapper ecsItemCfgReadMapper;

    @Override
    public List<EcsItemCfgVo> queryList(EcsItemCfgDto dto) {
        TreeNewUtil<String, EcsItemCfgVo> treeNewUtil = new TreeNewUtil<>();
        return treeNewUtil.buildTree(ecsItemCfgReadMapper.queryList(dto));
    }

}
