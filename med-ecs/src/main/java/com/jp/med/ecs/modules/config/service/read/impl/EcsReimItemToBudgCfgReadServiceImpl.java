package com.jp.med.ecs.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsReimItemToBudgCfgReadMapper;
import com.jp.med.common.dto.ecs.EcsReimItemToBudgCfgDto;
import com.jp.med.common.vo.ecs.EcsReimItemToBudgCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsReimItemToBudgCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Objects;

@Transactional(readOnly = true)
@Service
public class EcsReimItemToBudgCfgReadServiceImpl extends ServiceImpl<EcsReimItemToBudgCfgReadMapper, EcsReimItemToBudgCfgDto> implements EcsReimItemToBudgCfgReadService {

    @Autowired
    private EcsReimItemToBudgCfgReadMapper ecsReimItemToBudgCfgReadMapper;

    @Override
    public List<EcsReimItemToBudgCfgVo> queryList(EcsReimItemToBudgCfgDto dto) {
        if (!Objects.isNull(dto.getPageNum()) &&  !Objects.isNull(dto.getPageSize())) {
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        return ecsReimItemToBudgCfgReadMapper.queryList(dto);
    }

}
