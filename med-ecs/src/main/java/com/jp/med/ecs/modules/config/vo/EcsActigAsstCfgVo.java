package com.jp.med.ecs.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

import java.util.List;

/**
 * 会计科目辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 15:45:43
 */
@Data
public class EcsActigAsstCfgVo {

	/** id */
	private Integer id;

	/** 辅助信息代码 */
	private String asstCode;

	/** 辅助信息名称 */
	private String asstName;

	/** 拼音助记码 */
	private String pinyin;

	/** 备注 */
	private String remarks;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改时间 */
	private String modiTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 启用标志 */
	private String activeFlag;

	/** 数据来源 */
	private String dataSouc;

	/** 辅项信息数据 */
	private List<?> auxData;
}
