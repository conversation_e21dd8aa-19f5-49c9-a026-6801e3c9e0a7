package com.jp.med.ecs.modules.config.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EcsReimShareGasExcelEntity {

    /**
     * id
     */
    private Integer id;

    /**
     * 报销明细id
     */
    private Integer reimDetailId;

    /**
     * 分摊类型
     */
    private String shareType;

    /** 部门名称 **/
    @ExcelProperty(index = 0)
    private String deptName;

    /** 基准 **/
    @ExcelProperty(index = 1)
    private Integer base;

    /** 部门编码 **/
    @ExcelProperty(index = 2)
    private String deptCode;

    /** 金额 **/
    @ExcelProperty(index = 3)
    private BigDecimal amt;

    /**
     * 摘要
     */
    private String abs;
}
