package com.jp.med.ecs.modules.drugMgt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 药品报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:45
 */
@Data
@TableName("ecs_drug_reim_detai")
public class EcsDrugReimDetaiEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 供货单位 */
	@TableField("spler")
	private String spler;

	/** 合计金额小写(元) */
	@TableField("sum")
	private BigDecimal sum;

	/** 合计金额大写 */
	@TableField("cap_sum")
	private BigDecimal capSum;

	/** 付款说明 */
	@TableField("pay_istr")
	private String payIstr;

	/** 审核批次号 */
	@TableField("audit_bchno")
	private String auditBchno;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("craete_time")
	private String craeteTime;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}
