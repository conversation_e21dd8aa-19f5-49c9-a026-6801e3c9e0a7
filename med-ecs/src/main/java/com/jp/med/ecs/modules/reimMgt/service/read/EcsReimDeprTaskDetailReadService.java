package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDetailDto;
import com.jp.med.common.vo.EcsReimDeprTaskDetailVo;

import java.util.List;

/**
 * 折旧任务明细
 * <AUTHOR>
 * @email -
 * @date 2025-02-19 15:12:46
 */
public interface EcsReimDeprTaskDetailReadService extends IService<EcsReimDeprTaskDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimDeprTaskDetailVo> queryList(EcsReimDeprTaskDetailDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsReimDeprTaskDetailVo> queryPageList(EcsReimDeprTaskDetailDto dto);
}

