package com.jp.med.ecs.modules.reimMgt.constant;

/**
 * ECS模块专用常量
 */
public interface EcsConst {

    /** 附件类型-普通 **/
    String FILE_TYPE_GENERAL = "1";

    /** 附件类型-付款证明 **/
    String FILE_TYPE_PAY = "2";

    /** 附件类型-发票 **/
    String FILE_TYPE_INVO = "3";

    /** 报销不存在 **/
    String REIM_ITEM_NOT_EXSIST = "当前报销不存在";

    /** 工资任务明细不存在 **/
    String SALARY_TASK_DETAILS_NOT_EXIST = "工资任务明细不存在";

    /** 零星采购任务明细不存在 **/
    String PURC_TASK_DETAILS_NOT_EXIST = "零星采购任务明细不存在";

    /** 合同任务明细不存在 **/
    String CONTRACT_TASK_DETAIL_NOT_EXIST = "合同任务明细不存在";

    /** 折旧任务明细不存在 **/
    String DEPR_TASK_DETAILS_NOT_EXIST = "折旧任务明细不存在";

    //-------------------------发票ocr识别状态start------------------

    /** 可报销 **/
    String CAN_REIM = "1";

    /** 已报销 **/
    String HAS_REIMED = "2";

    /** 重复发票 **/
    String REPEAT_INVO = "3";

    /** 发票报销中 **/
    String INVO_IN_AUDIT = "4";

    /** 发票信息不规范 **/
    String INVO_MSG_ERROR = "5";

    /** 人工审核未通过 **/
    String MANUAL_AUDIT_NO_PASS = "6";
    /** 识别失败 **/
    String OCR_ERROR = "7";

    /** 核验失败 **/
    String VERIFY_ERROR = "8";

    /** 核验作废 **/
    String VERIFY_INVALID = "9";

    //-------------------------发票ocr识别状态end--------------------


    //----------------------------发票作废标志start-----------------------

    /** 未作废 **/
    String INVOICE_INVALID_MARK_N =  "N";

    /** 已作废 **/
    String INVOICE_INVALID_MARK_Y =  "Y";
    /** 冲红 **/
    String INVOICE_INVALID_MARK_H =  "H";
    /** 部分冲红 **/
    String INVOICE_INVALID_MARK_7 =  "7";
    /** 全额冲红 **/
    String INVOICE_INVALID_MARK_8 =  "8";




    //----------------------------发票作废标志end-------------------------


    //-------------------------发票核验识别状态start--------------------
    /** 核验成功 **/
    String INVO_CHECK_STATE_VALID = "1";

    /** 核验失败 **/
    String INVO_CHECK_STATE_INVALID = "0";
    //-------------------------发票核验识别状态end----------------------

    //-----------------------报销业务状态start------------------------------
    /** 报销，已付款 **/
    String REIM_BUSSTAS_PAID = "1";

    /** 报销，未提交 **/
    String REIM_BUSSTAS_UNSUBMIT = "2";

    /** 报销，审核中 **/
    String REIM_BUSSTAS_AUDITING = "3";

    /** 报销，已驳回 **/
    String REIM_BUSSTAS_REJECTED = "4";

    /** 报销，取消 **/
    String REIM_BUSSTAS_CANCEL = "5";

    /** 报销，审核通过 **/
    String REIM_BUSSTAS_PASS = "6";
    //-----------------------报销业务状态end------------------------------


    //------------------------申请业务状态start---------------------------

    /** 审核中 **/
    String APPR_STATUS_AUDITING = "1";
    /** 审核成功 **/
    String APPR_STATUS_AUDIT_SUCCESS = "2";
    /** 审核失败 **/

    String APPR_STATUS_AUDIT_FAILD = "3";

    /** 审核取消 **/
    String APPR_STATUS_AUDIT_CANCEL = "4";
    //------------------------申请业务状态end-----------------------------

    //------------------------药品报销业务状态start-----------------------------
    //1：审核中 2：审核成功 3：审核失败 4：待付款 5：已付款
    //审核中
    String DRUG_REIM_STATUS_AUDITING = "1";

    //审核成功
    String DRUG_REIM_STATUS_SUCCESS = "2";
    //审核失败
    String DRUG_REIM_STATUS_FAILED = "3";
    //代付款
    String DRUG_REIM_STATUS_TOPAY = "4";
    //已付款
    String DRUG_REIM_STATUS_PAID = "5";
    //------------------------药品报销业务状态end-------------------------------


    //-----------------------------发票来源start-----------------------------
    /** hrp上传 **/
    String INVOS_FROM_HRP = "1";
    /** 供应商上传 **/
    String INVOS_FROM_SUPPLIER = "2";
    /** 卫材上传 **/
    String INVOS_FROM_SATMAT = "3";

    //-----------------------------发票来源end-------------------------------



    //-----------------------------发票类型start-------------------------------
    String INVOICE = "增值税发票";

    String CARINVOICE = "机动车销售统一发票";

    String QUOTAINVOICE = "定额发票";

    String AIRITINERARY = "机票行程单";

    String TRAINTICKET = "火车票";

    String TOLLINVOICE = "过路过桥费发票";

    String ROLLTICKET = "增值税发票卷票";

    String BANKACCEPTANCE = "银行承兑汇票";

    String BUSSHIPTICKET = "客运车船票";

    String NONTAXINVOICE = "非税收入票据";

    String COMMONPRINTEDINVOICE = "通用机打发票";

    String TAXIINVOICE = "出租车票";

    String INVOOTHER = "其他";
    //-----------------------------发票类型end---------------------------------


    //----------------应发工资start----------------
    //岗位工资
    String POST_SALARY = "postSalary";
    // 薪级工资
    String SAL_GRADE_SALARY = "salGradeSalary";
    // 护士 10%
    String NURSE_SALARY = "nurseSalary";
    // 地区附加津贴
    String AREA_SALARY = "areaSalary";
    // 护龄津贴
    String AGE_SALARY = "ageSalary";
    // 基础性绩效
    String BASIC_PERF = "basicPerf";
    // 通讯费补贴
    String COMMUNICATION_FEE_ALLOWANCE = "communicationFeeAllowance";
    // 生活补贴
    String LIFE_SALARY = "lifeSalary";
    // 人力临时增加
    String TEMPORARY_ADD_SALARY = "temporaryAddSalary";
    // 财务临时增加
    String TEMPORARY_ADD_SALARY2 = "temporaryAddSalary2";

    //----------------应发工资end------------------


    //-----------------代扣代缴start---------------

    // 养老保险
    String PENSION_INSURANCE = "pensionInsurance";
    // 医疗保险
    String MEDICAL_INSURANCE = "medicalInsurance";
    // 失业保险
    String UNEMPLOYMENT_INSURANCE = "unemploymentInsurance";
    // 住房基金
    String HOUSING_FUND = "housingFund";
    // 执业年金
    String OCCUPATION_ANNUITY = "occupationalAnnuity";
    //个人所得税
    String PERSON_TAX = "personalIncomeTaxDeduction";

    //房租费
    String RENT = "rent";
    //水费
    String WATER_CHARGE = "waterCharge";
    // 人力临时扣款
    String TEMPORARY_REDUCE_SALARY = "temporaryReduceSalary";
    // 财务临时扣款
    String TEMPORARY_REDUCE_SALARY2 = "temporaryReduceSalary2";

    //银行存款 不是工资明细中的项，但是工资凭证会生成此项贷方，所以此项用于salaryConfig的配置
    String BANK_DEPOSIT = "bankDeposit";

    /** 工资总额-应付职工薪酬-基本工资（含离退休费） 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String SALARY_TOTAL_BASE = "salaryTotalBase";

    /** 工资总额-应付职工薪酬-国家统一规定的津贴补贴 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String SALARY_TOTAL_ALLOWANCE = "salaryTotalAllowance";

    /** 工资总额-应付职工薪酬-规范津贴补贴（绩效工资） 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置 **/
    String SALARY_TOTAL_PERFORM_SAL = "salaryTotalPerformSal";

    //工会会费
    String LABOR_UNION = "laborUnion";

    //-----------------代扣代缴end-----------------


    //------------------企业缴纳start--------------
    // 机关事业单位基本养老保险缴费
    String PENSION_INSURANCE_ENTP = "pensionInsuranceEntp";
    // 职工企业基本养老保险缴费
    String PENSION_INSURANCE_ENTP2 = "pensionInsuranceEntp2";
    // 职工基本医疗保险缴费
    String MEDICAL_INSURANCE_ENTP = "medicalInsuranceEntp";
    // 职工失业保险缴
    String UNEMPLOYMENT_INSURANCE_ENTP = "unemploymentInsuranceEntp";
    // 住房公积金
    String HOUSING_FUND_ENTP = "housingFundEntp";
    // 职业年金缴费
    String OCCUPATION_ANNUITY_ENTP = "occupationalAnnuityEntp";
    // 职工工伤保险缴费
    String INJR_INSU_ENTP = "injrInsuEntp";
    // 工费支出
    String PUB_FEE_ENTP = "pubFeeEntp";
    //------------------企业缴纳end----------------

    //-----------------------工资凭证人员类型start--------------------------
    //在编
    String[] ESTAB_STR_ARR = new String[]{"在编","血防占编"};

    //招聘
    String[] HIRE_STR_ARR = new String[]{"编外-医技","编外-护理","编外-辅助岗位","编外-医技-见习","编外-护理-见习","编外-辅助岗位-见习"};

    //临聘
    String[] TEMP_HIRE_STR_ARR = new String[]{"编外-其他专技","编外-后勤","编外-其他专技-见习","编外-后勤-见习"};

    //借调
    String[] SECONDMENT_STR_ARR = new String[]{"借调"};

    //返聘
    String[] REHIRE_STR_ARR = new String[]{"返聘"};
    //-----------------------工资凭证人员类型end----------------------------


    //------------------------药品库房类型-start---------------------------------
    //中药库
    String STOIN_TYPE_ZY = "1099";
    //西药库
    String STOIN_TYPE_XY = "1094";
    //消毒用品
    String STOIN_TYPE_XD = "1241";
    //------------------------药品库房类型-end-----------------------------------
}
