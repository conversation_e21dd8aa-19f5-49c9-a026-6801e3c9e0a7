package com.jp.med.ecs.modules.config.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ecs.modules.config.dto.EcsCorrsInsCfgDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 往来单位配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Mapper
public interface EcsCorrsInsCfgWriteMapper extends BaseMapper<EcsCorrsInsCfgDto> {
    void saveCorrsInsCfg(List<EcsCorrsInsCfgDto> efcDtos);
}
