package com.jp.med.ecs.modules.reimMgt.controller;

import com.jp.med.common.dto.ecs.EcsReimPurcTask;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimPurcTaskReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimPurcTaskWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 零星采购报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Api(value = "零星采购报销任务", tags = "零星采购报销任务")
@RestController
@RequestMapping("ecsReimPurcTask")
public class EcsReimPurcTaskController {

    @Autowired
    private EcsReimPurcTaskReadService ecsReimPurcTaskReadService;

    @Autowired
    private EcsReimPurcTaskWriteService ecsReimPurcTaskWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询零星采购报销任务")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsReimPurcTask dto){
        return CommonResult.paging(ecsReimPurcTaskReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询零星采购报销任务")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimPurcTask dto){
        return CommonResult.success(ecsReimPurcTaskReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增零星采购报销任务")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsReimPurcTask dto){
        ecsReimPurcTaskWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改零星采购报销任务")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimPurcTask dto){
        ecsReimPurcTaskWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除零星采购报销任务")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimPurcTask dto){
        ecsReimPurcTaskWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 保存工资任务
     * @param dto
     * @return
     */
    @ApiOperation("保存零星采购任务")
    @PostMapping("/savePurcTask")
    public CommonFeignResult savePurcTask(@RequestBody EcsReimPurcTask dto) {
        ecsReimPurcTaskWriteService.savePurcTask(dto);
        return CommonFeignResult.build();
    }

    /**
     * 查询零星采购任务明细
     * @param dto
     * @return
     */
    @ApiOperation("查询零星采购任务明细")
    @PostMapping("/queryPurcTaskDetail")
    public CommonResult<?> queryPurcTaskDetail(@RequestBody EcsReimPurcTask dto) {
        return CommonResult.success(ecsReimPurcTaskReadService.queryPurcTaskDetail(dto));
    }

    /**
     * 根据报销单ID查询零星采购任务
     * @param dto
     * @return
     */
    @ApiOperation("根据报销单ID查询零星采购任务")
    @PostMapping("/queryPurcTaskByReimId")
    public CommonResult<?> queryPurcTaskByReimId(@RequestBody EcsReimPurcTask dto) {
        return CommonResult.success(ecsReimPurcTaskReadService.queryPurcTaskByReimId(dto));
    }
}
