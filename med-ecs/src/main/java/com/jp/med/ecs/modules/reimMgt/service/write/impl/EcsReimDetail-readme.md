# 报销明细模块调用链文档 📝

## 模块概览

报销明细模块主要由三个核心组件构成:

1. `EcsReimDetailController`: 控制器层，处理前端请求
2. `EcsReimDetailReadService`: 读服务，处理查询操作
3. `EcsReimDetailWriteService`: 写服务，处理数据修改操作

## 功能清单

### 查询功能 🔍

- 报销明细列表查询
- 不分页列表查询
- 项目详情查询
- 科室报销金额查询
- 报销人员详情查询
- APP审核详情查询

### 操作功能 ✏️

- 新增报销明细
- 修改报销明细
- 删除未审核报销
- 取消报销申请
- 更新资金类型
- 更新项目信息
- 上传付款证明

### OCR识别功能 📸

- 发票OCR识别
- 新版OCR识别

## 调用链分析

### 报销明细查询流程

```mermaid
sequenceDiagram
    前端->>EcsReimDetailController: 发送查询请求 /list 或 /listNew
    EcsReimDetailController->>EcsReimDetailReadService: queryList/queryListNew(dto)
    EcsReimDetailReadService->>EcsReimDetailReadMapper: 查询数据库
    EcsReimDetailReadMapper-->>EcsReimDetailReadService: 返回数据
    EcsReimDetailReadService-->>EcsReimDetailController: 返回处理后数据
    EcsReimDetailController-->>前端: 返回包装结果(CommonResult)
```

### 报销明细保存流程

```mermaid
sequenceDiagram
    前端->>EcsReimDetailController: 发送保存请求 /save 或 /saveNew
    EcsReimDetailController->>EcsReimDetailWriteService: saveReimDetail/saveReimDetailNew(dto)
    EcsReimDetailWriteService->>EcsReimDetailWriteMapper: 插入数据库
    alt 需要审核
        EcsReimDetailWriteService->>AuditFeignService: 调用审核服务
    end
    EcsReimDetailWriteMapper-->>EcsReimDetailWriteService: 返回结果
    EcsReimDetailWriteService-->>EcsReimDetailController: 保存完成
    EcsReimDetailController-->>前端: 返回结果(CommonResult)
```

### OCR识别流程

```mermaid
flowchart TD
    A[前端] -->|上传发票| B[EcsReimDetailController]
    B -->|ocrIdentify| C[EcsReimDetailWriteService]
    C -->|请求识别| D[OCR接口服务]
    D -->|返回识别结果| C
    C -->|处理数据| E[EcsInvoRcdMapper]
    E -->|保存识别记录| F[数据库]
    C -->|返回处理后结果| B
    B -->|返回识别数据| A
```

### 报销审批流程

```mermaid
stateDiagram-v2
    [*] --> 创建报销申请
    创建报销申请 --> 提交审核
    提交审核 --> 审核中
    审核中 --> 审核通过: 全部审核通过
    审核中 --> 审核驳回: 任一环节驳回
    审核通过 --> 完成
    审核驳回 --> 修改后重新提交
    修改后重新提交 --> 审核中
    完成 --> [*]
```

## 技术实现细节

### 控制器层设计

- 使用`@RestController`和`@RequestMapping`定义API路径
- 应用Swagger注解`@Api`和`@ApiOperation`描述接口
- 通过`CommonResult<?>`统一响应格式

### 服务层实现

#### 读服务

- 基于`ServiceImpl<EcsReimDetailReadMapper, EcsReimDetailDto>`
- 使用`@Transactional(readOnly = true)`确保只读事务
- 使用`PageHelper`实现分页

#### 写服务

- 基于`ServiceImpl<EcsReimDetailWriteMapper, EcsReimDetailDto>`
- 使用`@Transactional(readOnly = false)`确保读写事务
- 集成多种外部服务（审核、OCR等）

### 数据处理模式

```mermaid
classDiagram
    class EcsReimDetailDto {
        +Integer id
        +String type
        +String appyer
        +Boolean audit
        +String chker
    }
    
    class EcsReimDetailVo {
        +Integer id
        +String type
        +String appyer
        +List~String~ evectionTime
        +String evectionBegnTime
        +String evectionEndTime
    }
    
    EcsReimDetailDto --> EcsReimDetailVo: 转换
```

## 关键功能点

1. **查询报销明细**：支持分页和非分页查询，根据审核状态过滤
2. **报销项目详情**：查询项目信息、辅助项目、人员及附件
3. **OCR识别**：支持发票OCR识别和验证
4. **审批流程**：与审核服务集成，支持多级审批流程
5. **文档生成**：支持生成费用报销审批表

# 报销明细模块类型详解 📊

## 1. 报销类型 (reimType)

```mermaid
pie
    title "报销类型分布"
    "差旅报销" : 1
    "培训报销" : 2
    "费用报销" : 3
    "工资报销" : 4
    "合同报销" : 5
    "零星采购" : 8
    "科研经费" : 9
```

### 详细说明

| 类型代码   | 类型名称 | 说明                    |
|--------|------|-----------------------|
| TYPE_1 | 差旅报销 | 员工出差产生的交通费、住宿费、餐饮费等报销 |
| TYPE_2 | 培训报销 | 员工参加培训产生的费用报销         |
| TYPE_3 | 费用报销 | 一般性办公、行政等费用报销         |
| TYPE_4 | 工资报销 | 与薪资相关的费用报销            |
| TYPE_5 | 合同报销 | 与合同执行相关的费用报销          |
| TYPE_8 | 零星采购 | 小额物品采购的费用报销           |
| TYPE_9 | 科研经费 | 科研项目相关的经费报销           |

## 2. 审核状态 (auditState)

# 报销明细模块 - 审核状态修正 🛠️

## 审核状态 (auditState)

让我修正状态图的语法错误：

```mermaid
stateDiagram-v2
    [*] --> 未审核
    state "未审核 (0)" as 未审核
    state "审核中 (1)" as 审核中
    state "审核通过 (2)" as 审核通过
    state "审核退回 (3)" as 审核退回
    state "付款完成 (4)" as 付款完成
    
    未审核 --> 审核中
    审核中 --> 审核通过
    审核中 --> 审核退回
    审核通过 --> 付款完成
    审核退回 --> 未审核: 修改后重新提交
```

## 审核状态详细说明

| 状态代码 | 状态名称 | 说明               |
|------|------|------------------|
| 0    | 未审核  | 报销单已创建但尚未提交审核流程  |
| 1    | 审核中  | 报销单已提交，正在各级审核流程中 |
| 2    | 审核通过 | 所有审核环节都已通过，等待付款  |
| 3    | 审核退回 | 审核被驳回，需要修改后重新提交  |
| 4    | 付款完成 | 审核通过并已完成付款操作     |

## 审核流程示例

```mermaid
flowchart TD
    A[创建报销申请] --> B{提交审核?}
    B -->|是| C[状态变更为审核中]
    B -->|否| J[保存为草稿]
    C --> D[一级审核]
    D -->|通过| E[二级审核]
    D -->|退回| I[状态变更为审核退回]
    E -->|通过| F[财务审核]
    E -->|退回| I
    F -->|通过| G[状态变更为审核通过]
    F -->|退回| I
    G --> H[付款完成]
    I --> A
    J --> A
```

希望这个修正版的状态图能够正确显示审核状态的流转关系。如果还有其他需要修正的图表，请告诉我！👨‍💻

### 详细说明

| 状态代码 | 状态名称 | 说明             |
|------|------|----------------|
| 0    | 未审核  | 报销单已创建但尚未提交审核  |
| 1    | 审核中  | 报销单已提交，正在审核流程中 |
| 2    | 审核通过 | 所有审核环节都已通过     |
| 3    | 审核退回 | 审核被驳回需要修改      |
| 4    | 付款完成 | 审核通过并完成付款      |

## 3. 辅助项类型 (supType)

```mermaid
flowchart TD
    A[辅助项类型] --> B[TYPE_1: 财务科目]
    A --> C[TYPE_2: 成本科目]
    A --> D[TYPE_3: 预算科目]
```

## 4. OCR识别类型

```mermaid
flowchart LR
    A[OCR识别方式] --> B[alyOcr: 阿里云OCR识别]
    A --> C[customOcr: 自定义OCR识别]
    B --> D[发票识别结果]
    C --> D
```

## 5. 附件类型

```mermaid
pie
    title "附件类型分布"
    "报销凭证" : 6
    "发票文件" : 3
    "付款证明" : 1
    "其他附件" : 2
```

## 6. 分摊类型

```mermaid
flowchart TD
    A[分摊类型] --> B[医院分摊]
    A --> C[科室分摊]
    A --> D[人员分摊]
    B --> E[分摊结果Excel]
    C --> E
    D --> E
```

## 7. 申请/审核标识

| 标志代码        | 标志名称 | 说明            |
|-------------|------|---------------|
| audit=true  | 审核模式 | 查询待审核的报销单     |
| audit=false | 申请模式 | 查询用户申请的报销单    |
| auditFlag=1 | 需要审核 | 标记报销需要进入审核流程  |
| auditFlag=0 | 不需审核 | 标记报销不需要审核直接通过 |

## 8. 功能区分

| 功能类型  | 新版接口             | 旧版接口          | 说明          |
|-------|------------------|---------------|-------------|
| 查询列表  | listNew          | list          | 新版支持更多过滤条件  |
| 保存报销  | saveNew          | save          | 新版支持更多报销类型  |
| OCR识别 | ocrIdentifyNew   | ocrIdentify   | 新版支持PDF多页识别 |
| 删除报销  | deleteNoAuditNew | deleteNoAudit | 新版支持级联删除    |

## 9. 项目关联类型

```mermaid
flowchart TB
    A[项目关联类型] --> B[差旅/培训项目]
    A --> C[合同项目]
    A --> D[科研项目]
    B --> E[updateProItems]
    C --> F[updateContractItems]
```

以上是报销明细模块中的主要类型分类，这些类型共同构成了完整的报销业务流程。🌟

# 报销明细模块代码方法映射文档 🧩

## 控制器层方法 `EcsReimDetailController.java`

```mermaid
classDiagram
    class EcsReimDetailController {
        +list(EcsReimDetailDto) : CommonResult~?~ [L46-49]
        +listNoPage(EcsReimDetailDto) : CommonResult~?~ [L50-53]
        +listNew(EcsReimDetailDto) : CommonResult~?~ [L63-66]
        +ocrIdentify(EcsReimDetailDto) : CommonResult~?~ [L101-105]
        +saveNew(EcsReimDetailDto) : CommonResult~?~ [L137-140]
        +deleteNoAuditNew(EcsReimDetailDto) : CommonResult~?~ [L186-189]
    }
```

### API端点映射表

| 接口路径                | 方法名              | 行数      | 功能描述      |
|---------------------|------------------|---------|-----------|
| `/list`             | list             | 46-49   | 分页查询报销明细  |
| `/listNoPage`       | listNoPage       | 50-53   | 不分页查询报销明细 |
| `/listNoPageNew`    | listNoPageNew    | 54-57   | 新版不分页查询   |
| `/listNew`          | listNew          | 63-66   | 新版分页查询    |
| `/itemDetail`       | itemDetail       | 68-71   | 查询项目详情    |
| `/psnDetails`       | psnDetails       | 73-76   | 查询报销人员详情  |
| `/ocrIdentify`      | ocrIdentify      | 101-105 | OCR识别     |
| `/ocrIdentifyNew`   | ocrIdentifyNew   | 107-110 | 新版OCR识别   |
| `/save`             | save             | 117-121 | 保存报销明细    |
| `/saveNew`          | saveNew          | 137-140 | 新版保存报销明细  |
| `/update`           | update           | 146-150 | 修改报销明细    |
| `/delete`           | delete           | 156-160 | 删除报销明细    |
| `/deleteNoAudit`    | deleteNoAudit    | 168-172 | 删除未审核报销   |
| `/deleteNoAuditNew` | deleteNoAuditNew | 186-189 | 新版删除未审核报销 |
| `/updateFundType`   | updateFundType   | 210-214 | 更新资金类型    |

## 读服务实现方法 `EcsReimDetailReadServiceImpl.java`

```mermaid
flowchart TD
    A[EcsReimDetailReadServiceImpl] --> B[queryList L46-65]
    A --> C[queryNoPageList L67-89]
    A --> D[queryListNew L104-130]
    A --> E[queryItemDetail L132-160]
    A --> F[queryAppAuditDetail L186-212]
    A --> G[queryEcsReimDoc L329-364]
```

### 读服务核心方法表

| 方法名                      | 行数      | 功能描述           |
|--------------------------|---------|----------------|
| queryList                | 46-65   | 分页查询报销明细列表     |
| queryNoPageList          | 67-89   | 不分页查询报销明细      |
| queryNoPageListNew       | 91-102  | 新版不分页查询报销明细    |
| queryListNew             | 104-130 | 新版分页查询报销明细     |
| queryItemDetail          | 132-160 | 查询项目详情         |
| queryDeptAmt             | 162-164 | 查询科室已报销金额      |
| psnDetails               | 166-169 | 查询报销人员详情       |
| queryAppAuditDetail      | 171-212 | 查询APP审核详情      |
| queryAppAuditDetail2     | 214-232 | 查询APP审核详情-费用报销 |
| queryExpenseAuditWarnNum | 234-241 | 查询费用报销申请审核数量   |
| queryTravelPsnInfo       | 243-246 | 查询年度差旅随行人员信息   |
| queryEcsReimDoc          | 329-364 | 生成费用报销审批表      |

## 写服务实现方法 `EcsReimDetailWriteServiceImpl.java`

```mermaid
flowchart LR
    A[EcsReimDetailWriteServiceImpl] --> B[saveReimDetail L188-391]
    A --> C[saveReimDetailNew L517-777]
    A --> D[ocrIdentify L922-1141]
    A --> E[ocrIdentifyNew L2040-2341]
    A --> F[deleteNoAudit L1242-1261]
    A --> G[deleteNoAuditNew L1344-1370]
```

### 写服务核心方法表

| 方法名                    | 行数        | 功能描述        |
|------------------------|-----------|-------------|
| saveReimDetail         | 188-391   | 保存报销明细      |
| savePurcReimDetail     | 439-516   | 保存采购报销明细    |
| saveReimDetailNew      | 517-777   | 新版保存报销明细    |
| getProcessName         | 778-828   | 获取流程名称      |
| push                   | 862-896   | 推送审核        |
| ocrIdentify            | 922-1141  | OCR识别       |
| modifyReimAsst         | 1230-1241 | 修改报销财务科目    |
| deleteNoAudit          | 1242-1261 | 删除未审核报销     |
| doDeleteNoAudit        | 1262-1343 | 执行删除未审核报销   |
| deleteNoAuditNew       | 1344-1370 | 新版删除未审核报销   |
| uploadPayFiles         | 1371-1407 | 上传付款证明      |
| updateFundType         | 1408-1418 | 更新资金类型      |
| customOcr              | 1419-1448 | 自定义OCR识别    |
| alyOcr                 | 1449-1519 | 阿里云OCR识别    |
| generateShareResExcel  | 1642-1681 | 生成分摊结果Excel |
| updateProItems         | 1994-2011 | 更新项目信息      |
| updateContractItems    | 2012-2017 | 更新合同项目信息    |
| cancelEcsReimDetailNew | 2018-2039 | 取消报销申请      |
| ocrIdentifyNew         | 2040-2341 | 新版OCR识别     |
| alyOcrNew              | 2342-2431 | 新版阿里云OCR识别  |

## 类关系图

```mermaid
classDiagram
    class EcsReimDetailController {
        +list()
        +saveNew()
        +deleteNoAuditNew()
    }
    
    class EcsReimDetailReadService {
        <<interface>>
        +queryList()
        +queryItemDetail()
    }
    
    class EcsReimDetailWriteService {
        <<interface>>
        +saveReimDetailNew()
        +ocrIdentifyNew()
    }
    
    class EcsReimDetailReadServiceImpl {
        +queryList() L46-65
        +queryItemDetail() L132-160
    }
    
    class EcsReimDetailWriteServiceImpl {
        +saveReimDetailNew() L517-777
        +ocrIdentifyNew() L2040-2341
    }
    
    EcsReimDetailController --> EcsReimDetailReadService
    EcsReimDetailController --> EcsReimDetailWriteService
    EcsReimDetailReadService <|.. EcsReimDetailReadServiceImpl
    EcsReimDetailWriteService <|.. EcsReimDetailWriteServiceImpl
```

## 主要业务流程代码执行路径

### 1. 报销申请流程

```mermaid
sequenceDiagram
    前端->>+EcsReimDetailController: saveNew() L137-140
    EcsReimDetailController->>+EcsReimDetailWriteServiceImpl: saveReimDetailNew() L517-777
    EcsReimDetailWriteServiceImpl->>EcsReimDetailWriteServiceImpl: 设置报销单数据 L520-600
    EcsReimDetailWriteServiceImpl->>EcsReimDetailWriteMapper: 插入数据库 L650-680
    EcsReimDetailWriteServiceImpl->>EcsReimDetailWriteServiceImpl: push() L862-896
    EcsReimDetailWriteServiceImpl->>AuditFeignService: 调用审核服务 L880-895
    EcsReimDetailWriteServiceImpl-->>-EcsReimDetailController: 返回结果
    EcsReimDetailController-->>-前端: 返回CommonResult
```

### 2. OCR识别流程

```mermaid
flowchart TD
    A[EcsReimDetailController.ocrIdentifyNew L107-110] --> B[EcsReimDetailWriteServiceImpl.ocrIdentifyNew L2040-2341]
    B --> C{isPDF?}
    C -->|是| D[processPdf L2439-2459]
    C -->|否| E[alyOcrNew L2342-2431]
    D --> F[处理多页PDF]
    E --> G[识别单页图片]
    F --> H[handleOcrStrToDto L2480-2519]
    G --> H
    H --> I[返回识别结果]
```

## 模块依赖图

```mermaid
flowchart TD
    A[EcsReimDetailController] --> B[EcsReimDetailReadService]
    A --> C[EcsReimDetailWriteService]
    B --> D[EcsReimDetailReadMapper]
    C --> E[EcsReimDetailWriteMapper]
    C --> F[AuditFeignService]
    C --> G[EcsReimFileRecordWriteMapper]
    C --> H[HrmFeignService]
    C --> I[OCR识别接口]
    D --> J[(数据库)]
    E --> J
```

这个文档详细列出了报销明细模块的Java代码方法及其行数，可以帮助开发者更快地定位和理解代码的功能和结构。🔍
