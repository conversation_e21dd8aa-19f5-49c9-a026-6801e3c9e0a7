package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimPurcTask;
import com.jp.med.common.vo.EcsReimPurcTaskDetailVo;
import com.jp.med.common.vo.EcsReimPurcTaskVo;

import java.util.List;

/**
 * 零星采购报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
public interface EcsReimPurcTaskReadService extends IService<EcsReimPurcTask> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimPurcTaskVo> queryList(EcsReimPurcTask dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsReimPurcTaskVo> queryPageList(EcsReimPurcTask dto);

    List<EcsReimPurcTaskDetailVo> queryPurcTaskDetail(EcsReimPurcTask dto);

    List<EcsReimPurcTaskVo> queryPurcTaskByReimId(EcsReimPurcTask dto);
}

