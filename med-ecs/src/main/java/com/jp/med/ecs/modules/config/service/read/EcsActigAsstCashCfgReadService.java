package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstCashCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstCashCfgVo;

import java.util.List;

/**
 * 现金流量配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-08 14:03:00
 */
public interface EcsActigAsstCashCfgReadService extends IService<EcsActigAsstCashCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsActigAsstCashCfgVo> queryList(EcsActigAsstCashCfgDto dto);
}

