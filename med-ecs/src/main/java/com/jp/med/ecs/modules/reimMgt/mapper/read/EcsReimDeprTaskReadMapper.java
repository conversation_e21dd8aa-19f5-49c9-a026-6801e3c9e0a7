package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.vo.EcsReimDeprTaskVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 费用报销-折旧凭证
 * <AUTHOR>
 * @email -
 * @date 2025-02-07 16:08:24
 */
@Mapper
public interface EcsReimDeprTaskReadMapper extends BaseMapper<EcsReimDeprTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimDeprTaskVo> queryList(EcsReimDeprTaskDto dto);
}
