package com.jp.med.ecs.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ecs.modules.config.dto.EcsUserCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsUserCorrsInsCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsUserCorrsInsCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 费用报销用户-往来单位映射配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-30 10:36:18
 */
@Api(value = "费用报销用户-往来单位映射配置表", tags = "费用报销用户-往来单位映射配置表")
@RestController
@RequestMapping("ecsUserCorrsInsCfg")
public class EcsUserCorrsInsCfgController {

    @Autowired
    private EcsUserCorrsInsCfgReadService ecsUserCorrsInsCfgReadService;

    @Autowired
    private EcsUserCorrsInsCfgWriteService ecsUserCorrsInsCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询费用报销用户-往来单位映射配置表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsUserCorrsInsCfgDto dto){
        return CommonResult.paging(ecsUserCorrsInsCfgReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询费用报销用户-往来单位映射配置表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsUserCorrsInsCfgDto dto){
        return CommonResult.success(ecsUserCorrsInsCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增费用报销用户-往来单位映射配置表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsUserCorrsInsCfgDto dto){
        ecsUserCorrsInsCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改费用报销用户-往来单位映射配置表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsUserCorrsInsCfgDto dto){
        ecsUserCorrsInsCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除费用报销用户-往来单位映射配置表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsUserCorrsInsCfgDto dto){
        ecsUserCorrsInsCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
