package com.jp.med.ecs.modules.common.controller;

import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.ecs.modules.common.service.write.EcsAuditWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 19:13
 * @description:
 */
@Api(value = "审核控制器", tags = "审核控制器")
@RestController
    @RequestMapping("ecsAudit")
public class EcsAuditController {

    @Autowired
    private EcsAuditWriteService ecsAuditWriteService;

    @ApiOperation("审核结果通知")
    @PostMapping("/complete")
    public CommonFeignResult complete(@RequestBody AuditDetail dto){
        ecsAuditWriteService.complete(dto);
        return CommonFeignResult.build();
    }
}
