package com.jp.med.ecs.modules.reimMgt.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimContractTask;
import com.jp.med.common.dto.ecs.EcsReimContractTaskDetail;
import org.apache.ibatis.annotations.Mapper;

/**
 * 合同报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
@Mapper
public interface EcsReimContractTaskWriteMapper extends BaseMapper<EcsReimContractTask> {

    /**
     * 插入合同任务明细
     * @param detail
     */
    void insertContractTaskDetail(EcsReimContractTaskDetail detail);
}
