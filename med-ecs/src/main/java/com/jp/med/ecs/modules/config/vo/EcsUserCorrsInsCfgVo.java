package com.jp.med.ecs.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 费用报销用户-往来单位映射配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-30 10:36:18
 */
@Data
public class EcsUserCorrsInsCfgVo {

	/** id */
	private Integer id;

	/** 员工编号 */
	private String empCode;

	/** 往来单位 */
	private String insCode;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 年度 */
	private String year;

	/** 医疗机构 */
	private String hospitalId;

	/** 启用标志 1：启用 0：弃用 */
	private String activeFlag;

}
