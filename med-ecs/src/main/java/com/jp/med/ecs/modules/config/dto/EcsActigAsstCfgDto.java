package com.jp.med.ecs.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 会计科目辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 15:45:43
 */
@Data
@TableName("ecs_actig_asst_cfg" )
public class EcsActigAsstCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 辅助信息代码 */
    @TableField("asst_code")
    private String asstCode;

    /** 辅助信息名称 */
    @TableField("asst_name")
    private String asstName;

    /** 拼音助记码 */
    @TableField("pinyin")
    private String pinyin;

    /** 备注 */
    @TableField("remarks")
    private String remarks;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改时间 */
    @TableField("modi_time")
    private String modiTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 数据来源 */
    @TableField("data_souc")
    private String dataSouc;

    /** 辅助信息 */
    @TableField(exist = false)
    private String asst;
}
