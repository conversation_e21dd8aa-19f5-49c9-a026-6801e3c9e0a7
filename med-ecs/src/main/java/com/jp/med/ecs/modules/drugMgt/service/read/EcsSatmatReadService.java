package com.jp.med.ecs.modules.drugMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatDto;
import com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatVo;

import java.util.List;
import java.util.Map;

/**
 * 卫材入库单
 * <AUTHOR>
 * @email -
 * @date 2024-07-26 15:06:37
 */
public interface EcsSatmatReadService extends IService<EcsSatmatDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsSatmatVo> queryList(EcsSatmatDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsSatmatVo> queryPageList(EcsSatmatDto dto);

    List<Map<String, Integer>> monthNum(EcsSatmatDto dto);
}

