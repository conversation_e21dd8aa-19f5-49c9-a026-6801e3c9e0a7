package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.dto.ecs.EcsDrugAccountPeriodDto;
import com.jp.med.ecs.modules.config.mapper.write.EcsDrugAccountPeriodWriteMapper;
import com.jp.med.ecs.modules.config.service.write.EcsDrugAccountPeriodWriteService;
import com.jp.med.ecs.modules.config.utils.AccountPeriodUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 药品账期
 * <AUTHOR>
 * @email -
 * @date 2025-02-21 10:36:30
 */
@Service
@Transactional(readOnly = false)
public class EcsDrugAccountPeriodWriteServiceImpl extends ServiceImpl<EcsDrugAccountPeriodWriteMapper, EcsDrugAccountPeriodDto> implements EcsDrugAccountPeriodWriteService {


    @Autowired
    private EcsDrugAccountPeriodWriteMapper ecsDrugAccountPeriodWriteMapper;

    @Override
    public String reverseAccount(EcsDrugAccountPeriodDto dto) {
        //查询最新的账期
        LambdaQueryWrapper<EcsDrugAccountPeriodDto> query = Wrappers.lambdaQuery(EcsDrugAccountPeriodDto.class);
        query.orderByDesc(EcsDrugAccountPeriodDto::getId).last("LIMIT 1");
        EcsDrugAccountPeriodDto account = ecsDrugAccountPeriodWriteMapper.selectOne(query);
        //获取上月账期
        String previousMonth = AccountPeriodUtil.getPreviousMonth(account.getAccountPeriod());
        //新增账期数据
        EcsDrugAccountPeriodDto newAccount = new EcsDrugAccountPeriodDto();
        newAccount.setAccountPeriod(previousMonth);
        newAccount.setCrter(dto.getSysUser().getUsername());
        newAccount.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        //保存
        save(newAccount);
        return previousMonth;
    }

    @Override
    public void closeAccount(EcsDrugAccountPeriodDto dto) {
        //查询最新的账期
        LambdaQueryWrapper<EcsDrugAccountPeriodDto> query = Wrappers.lambdaQuery(EcsDrugAccountPeriodDto.class);
        query.orderByDesc(EcsDrugAccountPeriodDto::getId).last("LIMIT 1");
        EcsDrugAccountPeriodDto account = ecsDrugAccountPeriodWriteMapper.selectOne(query);
        //获取下月账期
        String nextMonth = AccountPeriodUtil.getNextMonth(account.getAccountPeriod());
        //新增账期数据
        EcsDrugAccountPeriodDto newAccount = new EcsDrugAccountPeriodDto();
        newAccount.setAccountPeriod(nextMonth);
        newAccount.setCrter(dto.getSysUser().getUsername());
        newAccount.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        //保存
        save(newAccount);
    }
}
