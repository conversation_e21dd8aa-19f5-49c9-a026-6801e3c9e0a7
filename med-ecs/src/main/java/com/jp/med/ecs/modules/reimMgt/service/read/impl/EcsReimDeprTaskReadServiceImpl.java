package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDeprTaskReadMapper;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.vo.EcsReimDeprTaskVo;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimDeprTaskReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsReimDeprTaskReadServiceImpl extends ServiceImpl<EcsReimDeprTaskReadMapper, EcsReimDeprTaskDto> implements EcsReimDeprTaskReadService {

    @Autowired
    private EcsReimDeprTaskReadMapper ecsReimDeprTaskReadMapper;

    @Override
    public List<EcsReimDeprTaskVo> queryList(EcsReimDeprTaskDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimDeprTaskReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimDeprTaskVo> queryPageList(EcsReimDeprTaskDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsReimDeprTaskReadMapper.queryList(dto);
    }

}
