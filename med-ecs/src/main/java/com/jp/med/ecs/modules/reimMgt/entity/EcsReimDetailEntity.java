package com.jp.med.ecs.modules.reimMgt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报销明细
 * <AUTHOR>
 * @email -
 * @date 2023-12-04 22:01:14
 */
@Data
@TableName("ecs_reim_detail")
public class EcsReimDetailEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 申请人 */
	@TableField("appyer")
	private String appyer;

	/** 出差时间 */
	@TableField("evection_time")
	private String evectionTime;

	/** 申请时间 */
	@TableField("appyer_time")
	private String appyerTime;

	/** 预算控制 */
	@TableField("budg_ctrl")
	private String budgCtrl;

	/** 出差地点 */
	@TableField("evection_addr")
	private String evectionAddr;

	/** 出差事由 */
	@TableField("evection_rea")
	private String evectionRea;

	/** 开户银行 */
	@TableField("bank")
	private String bank;

	/** 户名 */
	@TableField("acctname")
	private String acctname;

	/** 银行账(卡)号 */
	@TableField("bankcode")
	private String bankcode;

	/** 合计金额小写(元) */
	@TableField("sum")
	private BigDecimal sum;

	/** 合计金额大写 */
	@TableField("cap_sum")
	private BigDecimal capSum;

	/** 审核批次号 */
	@TableField("audit_bchno")
	private String auditBchno;

	/** 业务状态 */
	@TableField("busstas")
	private String busstas;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}
