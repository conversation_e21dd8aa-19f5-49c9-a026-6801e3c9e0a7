package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo;

import java.util.List;
import java.util.Map;

/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
public interface EcsInvoRcdReadService extends IService<EcsInvoRcdDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsInvoRcdVo> queryList(EcsInvoRcdDto dto);

    /**
     * 查询发票对应校验记录及明细
     * @param dto
     * @return
     */
    Map<String,Object> queryEcsInvoRcdChk(EcsInvoRcdDto dto);


    Integer queryInvoAuditWarnNum(EcsInvoRcdDto dto);

    List<EcsInvoRcdVo> queryEcsInvoRcdRecogn(EcsInvoRcdDto dto);
}

