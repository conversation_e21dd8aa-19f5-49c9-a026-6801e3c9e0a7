package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.ecs.EcsReimSalaryTask;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.hrm.HrmEmployeeSalaryFeignService;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.ULIDUtil;
import com.jp.med.common.vo.EmployeeReallySalaryVo;
import com.jp.med.common.vo.HrpSalaryTask;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.entity.*;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimSalaryTaskReadMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimSalaryTaskReadService;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskVo;
import io.seata.common.util.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class EcsReimSalaryTaskReadServiceImpl extends ServiceImpl<EcsReimSalaryTaskReadMapper, EcsReimSalaryTask> implements EcsReimSalaryTaskReadService {

    @Autowired
    private EcsReimSalaryTaskReadMapper ecsReimSalaryTaskReadMapper;

    @Autowired
    private HrmEmployeeSalaryFeignService hrmEmployeeSalaryFeignService;

    @Override
    public List<EcsReimSalaryTaskVo> queryList(EcsReimSalaryTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimSalaryTaskReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimSalaryTaskVo> queryPageList(EcsReimSalaryTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsReimSalaryTaskReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimSalaryTaskVo> queryPageListNew(EcsReimSalaryTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsReimSalaryTaskReadMapper.queryListNew(dto);
    }

    /**
     * 查询工资任务明细及对应类别预算项
     * @param dto
     * @return
     */
    @Override
    public List<EcsReimSalaryTaskDetailVo> querySalaryTaskDetail(EcsReimSalaryTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimSalaryTaskReadMapper.querySalaryTaskDetail(dto);
    }

    /**
     * 生成工资汇总表格
     * @param dto
     * @return
     */
    @Override
    public Map<String, String> getSalarySummaryExcel(EcsReimSalaryTask dto) {
        //查询工资
        EcsReimSalaryTask salaryTask = ecsReimSalaryTaskReadMapper.selectById(dto.getId());
        //查询salaryId的任务信息
        LambdaQueryWrapper<EcsReimSalaryTask> taskWrapper = Wrappers.lambdaQuery(EcsReimSalaryTask.class);
        taskWrapper.eq(EcsReimSalaryTask::getSalaryId,salaryTask.getSalaryId());
        List<EcsReimSalaryTask> tasks = ecsReimSalaryTaskReadMapper.selectList(taskWrapper);
        List<Integer> tasksIds = tasks.stream().map(EcsReimSalaryTask::getId).collect(Collectors.toList());
        EcsReimSalaryTask param = new EcsReimSalaryTask();
        param.setIds(tasksIds);
        //查询工资明细 （查询基本工资和个人扣缴类型明细）
        List<EcsReimSalaryTaskDetailVo> taskDetails = ecsReimSalaryTaskReadMapper.querySalaryTaskDetail(param);
        //查询在编人员工资明细
        List<EcsReimSalaryTaskDetailVo> estabLists = taskDetails.stream()
                .filter(e -> Arrays.stream(EcsConst.ESTAB_STR_ARR).anyMatch(et -> StringUtils.equals(et, e.getEmpType())))
                .collect(Collectors.toList());
        //查询招聘人员工资明细
        List<EcsReimSalaryTaskDetailVo> hireLists = taskDetails.stream().filter(e -> Arrays.stream(EcsConst.HIRE_STR_ARR).anyMatch(et -> StringUtils.equals(et, e.getEmpType())))
                .collect(Collectors.toList());
        //查询临聘人员工资明细
        List<EcsReimSalaryTaskDetailVo> tempHireLists = taskDetails.stream().filter(e -> Arrays.stream(EcsConst.TEMP_HIRE_STR_ARR).anyMatch(et -> StringUtils.equals(et, e.getEmpType())))
                .collect(Collectors.toList());
        //查询借调人员工资明细
        List<EcsReimSalaryTaskDetailVo> secondmentLists = taskDetails.stream().filter(e -> Arrays.stream(EcsConst.SECONDMENT_STR_ARR).anyMatch(et -> StringUtils.equals(et, e.getEmpType())))
                .collect(Collectors.toList());
        //查询返聘人员工资明细
        List<EcsReimSalaryTaskDetailVo> rehireLists = taskDetails.stream().filter(e -> Arrays.stream(EcsConst.REHIRE_STR_ARR).anyMatch(et -> StringUtils.equals(et, e.getEmpType())))
                .collect(Collectors.toList());
        //①构造标题
        Map<String,String> title = new HashMap<>();
        title.put("year",salaryTask.getFfMth().substring(0,4));         //工资年
        title.put("month",salaryTask.getFfMth().substring(5,7));        //工资月
        title.put("createDate", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));     //编制年月

        //②构造三种人员工资excel展示行
        List<EcsSalarySummaryExcel> datas = new ArrayList<>();
        /*//1.在编人员工资项金额   在编：【在线，血防占编】
        for (int i = 0; i < EcsConst.ESTAB_STR_ARR.length; i++) {
            String s = EcsConst.ESTAB_STR_ARR[i];
            EcsSalarySummaryExcel estabRow = getSalarySummaryRow(estabLists,s);
            datas.add(estabRow);
        }
        //2.招聘人员工资项金额   招聘：【辅助岗位、聘用护理、聘用医技、聘用其他】
//        List<EcsSalarySummaryExcel> hireDatas = new ArrayList<>();
        for (int i = 0; i < EcsConst.HIRE_STR_ARR.length; i++) {
            String s = EcsConst.HIRE_STR_ARR[i];
            EcsSalarySummaryExcel hireRow = getSalarySummaryRow(hireLists, s);
            datas.add(hireRow);
        }

        //3.临聘人员工资项金额   临聘：【聘用后勤、聘用其他-见习、聘用后勤-见习、聘用医技-见习、聘用护理-见习、辅助岗位=见习】
//        List<EcsSalarySummaryExcel> tempHireDatas = new ArrayList<>();
        for (int i = 0; i < EcsConst.TEMP_HIRE_STR_ARR.length; i++) {
            String s = EcsConst.TEMP_HIRE_STR_ARR[i];
            EcsSalarySummaryExcel tempHireRow = getSalarySummaryRow(tempHireLists, s);
            datas.add(tempHireRow);
        }
        //4.借调人员
        for (int i = 0; i < EcsConst.SECONDMENT_STR_ARR.length; i++) {
            String s = EcsConst.SECONDMENT_STR_ARR[i];
            EcsSalarySummaryExcel secondmentRow = getSalarySummaryRow(secondmentLists, s);
            datas.add(secondmentRow);
        }
        //5.返聘人员
        for (int i = 0; i < EcsConst.REHIRE_STR_ARR.length; i++) {
            String s = EcsConst.REHIRE_STR_ARR[i];
            EcsSalarySummaryExcel rehireRow = getSalarySummaryRow(rehireLists, s);
            datas.add(rehireRow);
        }*/

        //生成合计数据
        Integer peoNumSum = 0;
        BigDecimal postSalarySum = BigDecimal.ZERO;
        BigDecimal nurseSalarySum = BigDecimal.ZERO;
        BigDecimal salGradeSalarySum = BigDecimal.ZERO;
        BigDecimal basicPerfSum = BigDecimal.ZERO;
        BigDecimal ageSalarySum = BigDecimal.ZERO;
//        BigDecimal driverSalarySum = BigDecimal.ZERO;
//        BigDecimal communicationFeeAllowanceSum = BigDecimal.ZERO;
        BigDecimal lifeSalarySum = BigDecimal.ZERO;
        BigDecimal areaSalarySum = BigDecimal.ZERO;
        BigDecimal temporaryAddSalarySum = BigDecimal.ZERO;
        BigDecimal temporaryAddSalary2Sum = BigDecimal.ZERO;
        BigDecimal totalPayableSum = BigDecimal.ZERO;
        BigDecimal pensionInsuranceSum = BigDecimal.ZERO;
        BigDecimal medicalInsuranceSum = BigDecimal.ZERO;
        BigDecimal unemploymentInsuranceSum = BigDecimal.ZERO;
        BigDecimal housingFundSum = BigDecimal.ZERO;
        BigDecimal occupationalAnnuitySum = BigDecimal.ZERO;
        BigDecimal laborUnionSum = BigDecimal.ZERO;
        BigDecimal personTaxSum = BigDecimal.ZERO;
//        BigDecimal rentSum = BigDecimal.ZERO;
//        BigDecimal waterChargeSum = BigDecimal.ZERO;
        BigDecimal temporaryReduceSalarySum = BigDecimal.ZERO;
        BigDecimal temporaryReduceSalary2Sum = BigDecimal.ZERO;
        BigDecimal reduceSalaryTotalSum = BigDecimal.ZERO;
        BigDecimal totalPaidSum = BigDecimal.ZERO;
        for (int i = 0; i < datas.size(); i++) {
            EcsSalarySummaryExcel row = datas.get(i);
            peoNumSum += row.getPeoNum();
            postSalarySum = postSalarySum.add(row.getLifeSalary() == null ? BigDecimal.ZERO : row.getLifeSalary());
            nurseSalarySum = nurseSalarySum.add(row.getNurseSalary() == null ? BigDecimal.ZERO : row.getNurseSalary());
            salGradeSalarySum = salGradeSalarySum.add(row.getSalGradeSalary() == null ? BigDecimal.ZERO : row.getSalGradeSalary());
            basicPerfSum = basicPerfSum.add(row.getBasicPerf() == null ? BigDecimal.ZERO : row.getBasicPerf());
            ageSalarySum = ageSalarySum.add(row.getAgeSalary() == null ? BigDecimal.ZERO : row.getAgeSalary());
            lifeSalarySum = lifeSalarySum.add(row.getLifeSalary() == null ? BigDecimal.ZERO : row.getLifeSalary());
            areaSalarySum = areaSalarySum.add(row.getAreaSalary() == null ? BigDecimal.ZERO : row.getAreaSalary());
            temporaryAddSalarySum = temporaryAddSalarySum.add(row.getTemporaryAddSalary() == null ? BigDecimal.ZERO : row.getTemporaryAddSalary());
            temporaryAddSalary2Sum = temporaryAddSalary2Sum.add(row.getTemporaryAddSalary2() == null ? BigDecimal.ZERO : row.getTemporaryAddSalary2());
            totalPayableSum = totalPayableSum.add(row.getTotalPayable() == null ? BigDecimal.ZERO : row.getTotalPayable());
            pensionInsuranceSum = pensionInsuranceSum.add(row.getPensionInsurance() == null ? BigDecimal.ZERO : row.getPensionInsurance());
            medicalInsuranceSum = medicalInsuranceSum.add(row.getMedicalInsurance() == null ? BigDecimal.ZERO : row.getMedicalInsurance());
            unemploymentInsuranceSum = unemploymentInsuranceSum.add(row.getUnemploymentInsurance() == null ? BigDecimal.ZERO : row.getUnemploymentInsurance());
            housingFundSum = housingFundSum.add(row.getHousingFund() == null ? BigDecimal.ZERO : row.getHousingFund());
            occupationalAnnuitySum = occupationalAnnuitySum.add(row.getOccupationalAnnuity() == null ? BigDecimal.ZERO : row.getOccupationalAnnuity());
            laborUnionSum = laborUnionSum.add(row.getLaborUnion() == null ? BigDecimal.ZERO : row.getLaborUnion());
            personTaxSum = personTaxSum.add(row.getPersonTax() == null ? BigDecimal.ZERO : row.getPersonTax());
            temporaryReduceSalarySum = temporaryReduceSalarySum.add(row.getTemporaryReduceSalary() == null ? BigDecimal.ZERO : row.getTemporaryReduceSalary());
            temporaryReduceSalary2Sum = temporaryReduceSalary2Sum.add(row.getTemporaryReduceSalary2() == null ? BigDecimal.ZERO : row.getTemporaryReduceSalary2());
            reduceSalaryTotalSum = reduceSalaryTotalSum.add(row.getReduceSalaryTotal() == null ? BigDecimal.ZERO : row.getReduceSalaryTotal());
            totalPaidSum = totalPaidSum.add(row.getTotalPaid() == null ? BigDecimal.ZERO : row.getTotalPaid());
        }

        EcsSalarySummaryExcel summaryRow = new EcsSalarySummaryExcel();
        summaryRow.setPeoNum(peoNumSum);
        summaryRow.setEmpType("合计");
        summaryRow.setPostSalary(postSalarySum);
        summaryRow.setNurseSalary(nurseSalarySum);
        summaryRow.setSalGradeSalary(salGradeSalarySum);
        summaryRow.setBasicPerf(basicPerfSum);
        summaryRow.setAgeSalary(ageSalarySum);
        summaryRow.setLifeSalary(lifeSalarySum);
        summaryRow.setAreaSalary(areaSalarySum);
        summaryRow.setTemporaryAddSalary(temporaryAddSalarySum);
        summaryRow.setTemporaryAddSalary2(temporaryAddSalary2Sum);
        summaryRow.setTotalPayable(totalPayableSum);
        summaryRow.setPensionInsurance(pensionInsuranceSum);
        summaryRow.setMedicalInsurance(medicalInsuranceSum);
        summaryRow.setUnemploymentInsurance(unemploymentInsuranceSum);
        summaryRow.setHousingFund(housingFundSum);
        summaryRow.setOccupationalAnnuity(occupationalAnnuitySum);
        summaryRow.setLaborUnion(laborUnionSum);
        summaryRow.setPersonTax(personTaxSum);
        summaryRow.setTemporaryReduceSalary(temporaryReduceSalarySum);
        summaryRow.setTemporaryReduceSalary2(temporaryReduceSalary2Sum);
        summaryRow.setReduceSalaryTotal(reduceSalaryTotalSum);
        summaryRow.setTotalPaid(totalPaidSum);
        datas.add(summaryRow);
        //不由推送的salaryTaskDetail来计算，由在职工资表计算后的数据再计算
        datas = new ArrayList<>();

        //③构造个人代扣备注说明
        List<String> absts = taskDetails.stream()
                .filter(e -> StringUtils.equals(e.getReimName(),EcsConst.TEMPORARY_REDUCE_SALARY)
                        && StringUtils.isNotEmpty(e.getEmpCode()))
                .map(EcsReimSalaryTaskDetailVo::getReimDesc).collect(Collectors.toList());
//        List<String> left = new ArrayList<>();
//        List<String> right = new ArrayList<>();
        List<EcsSalarySummaryAbstExcel> abstDatas = new ArrayList<>();
        for (int i = 0; i < absts.size(); i +=2) {
            String left = absts.get(i);
            //处理最后一个元素可能没有配对的right 的情况
            String right = (i + 1 < absts.size()) ? absts.get(i + 1) : null;
            abstDatas.add(new EcsSalarySummaryAbstExcel(left, right));
            /*if (i % 2 == 0 ) {
                //左侧备注
                left.add(absts.get(i));
            } else {
                //右侧备注
                right.add(absts.get(i));
            }*/
        }

        //二。在编招聘工资统计表
        List<EcsSalarySummary2Excel> zbzpDatas = new ArrayList<>();
        //查询在编招聘人员数据
        List<EcsReimSalaryTaskDetailVo> zbzpDetails = taskDetails.stream().filter(e -> Arrays.stream(EcsConst.ESTAB_STR_ARR).anyMatch(et -> StringUtils.equals(et, e.getEmpType()))
                        || Arrays.stream(EcsConst.HIRE_STR_ARR).anyMatch(et -> StringUtils.equals(et, e.getEmpType())))
                .collect(Collectors.toList());
        Map<String, List<EcsReimSalaryTaskDetailVo>> zbzpGroupByOrg = zbzpDetails.stream().filter(e -> StringUtils.isNotEmpty(e.getOrgId())).collect(Collectors.groupingBy(EcsReimSalaryTaskDetailVo::getOrgName));

        zbzpGroupByOrg.forEach((orgNameKey, orgIdValue) -> {
            EcsSalarySummary2Excel salarySummaryRow2 = getSalarySummaryRow2(orgIdValue, orgNameKey, salaryTask.getFfMth().substring(0, 7));
            zbzpDatas.add(salarySummaryRow2);
        });

        //三。在职职工工资表
        HrpSalaryTask salaryParam = new HrpSalaryTask();
        salaryParam.setId(salaryTask.getSalaryId());
        CommonResult<List<EmployeeReallySalaryVo>> result = hrmEmployeeSalaryFeignService.queryReallySalaryDetail(salaryParam);

        if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
            throw new AppException("在职职工工资明细数据不存在");
        }

        List<EcsSalarySummary3Excel> employeeSalaryDetails = new ArrayList<>();
        List<EmployeeReallySalaryVo> data = result.getData();
        data.stream().forEach(e -> {
            EcsSalarySummary3Excel row = new EcsSalarySummary3Excel();
            row.setEmpName(e.getEmpName());
            row.setSalaryNum(e.getEmpCode());
            row.setIcdCard(e.getIcdCard());
            row.setEmpType(e.getEmpType());
            row.setPostSalary(e.getPostSalary());
            row.setNurseSalary(e.getNurseSalary());
            row.setSalGradeSalary(e.getSalGradeSalary());
            row.setBasicPerf(e.getBasicPerf());
            row.setAgeSalary(e.getAgeSalary());
//            row.setDriverSalary(e.getDriverSalary());         暂无
//            row.setCommunicationFeeAllowance(StringUtils.isNotBlank(e.getCommunicationFeeAllowance())
//                    ? new BigDecimal(e.getCommunicationFeeAllowance())
//                    : null);
            row.setLifeSalary(e.getLifeSalary());
            row.setAreaSalary(e.getAreaSalary());
            row.setTemporaryAddSalary(e.getTemporaryAddSalary());
            row.setTemporaryAddSalary2(e.getTemporaryAddSalary2());
            //应发合计
            row.setTotalPayable(e.getShouldPayTotal());
            row.setPensionInsurance(e.getPensionInsurance());
            row.setMedicalInsurance(e.getMedicalInsurance());
            row.setUnemploymentInsurance(e.getUnemploymentInsurance());
            row.setHousingFund(e.getHousingFund());
            row.setOccupationalAnnuity(e.getOccupationalAnnuity());
            row.setLaborUnion(e.getLaborUnion());
            row.setPersonTax(e.getPersonalIncomeTaxDeduction());
            //人力、财务 临时扣减计算逻辑
            //实发temp  应发-五险一金-个人所得
            BigDecimal realPayTemp = Optional.ofNullable(e.getShouldPayTotal()).orElse(BigDecimal.ZERO)
                    .subtract(e.getPensionInsurance()!=null?e.getPensionInsurance():BigDecimal.ZERO)
                    .subtract(e.getMedicalInsurance()!=null?e.getMedicalInsurance():BigDecimal.ZERO)
                    .subtract(e.getUnemploymentInsurance()!=null?e.getUnemploymentInsurance():BigDecimal.ZERO)
                    .subtract(e.getHousingFund()!=null?e.getHousingFund():BigDecimal.ZERO)
                    .subtract(e.getOccupationalAnnuity()!=null?e.getOccupationalAnnuity():BigDecimal.ZERO)
                    .subtract(e.getPersonalIncomeTaxDeduction()!=null?e.getPersonalIncomeTaxDeduction():BigDecimal.ZERO);
            //人力扣减  M = 实发temp-人力扣减 ,如果M<0,则人力扣减=0，如果M>0, 人力扣减值不变
            BigDecimal M = realPayTemp.subtract(Optional.ofNullable(e.getTemporaryReduceSalary()).orElse(BigDecimal.ZERO));
            if (M.compareTo(BigDecimal.ZERO) < 0) {
                row.setTemporaryReduceSalary(BigDecimal.ZERO);
            } else {
                row.setTemporaryReduceSalary(Optional.ofNullable(e.getTemporaryReduceSalary()).orElse(BigDecimal.ZERO));
            }
            //财务扣减 N = M - 财务扣减 ,如果N<0,则财务扣减=0，如果N>0, 财务扣减值不变
            BigDecimal N = M.subtract(Optional.ofNullable(e.getTemporaryReduceSalary2()).orElse(BigDecimal.ZERO));
            if (N.compareTo(BigDecimal.ZERO) < 0) {
                row.setTemporaryReduceSalary2(BigDecimal.ZERO);
            } else {
                row.setTemporaryReduceSalary2(Optional.ofNullable(e.getTemporaryReduceSalary2()).orElse(BigDecimal.ZERO));
            }
            //本月未扣 P = N - 本月未扣 ，如果P<0,则本月未扣不变，如果P>0, 本月未扣= P  本月未扣的值累计到财务扣减上
            BigDecimal P = N.subtract(Optional.ofNullable(e.getNextMonthReduce()).orElse(BigDecimal.ZERO));
            if (P.compareTo(BigDecimal.ZERO) < 0) {
                row.setTemporaryReduceSalary2(row.getTemporaryReduceSalary2().add(Optional.ofNullable(e.getNextMonthReduce()).orElse(BigDecimal.ZERO)));
            } else {
                row.setTemporaryReduceSalary2(row.getTemporaryReduceSalary2().add(Optional.ofNullable(e.getNextMonthReduce()).orElse(BigDecimal.ZERO)));
            }

//            row.setTemporaryReduceSalary(e.getTemporaryReduceSalary());
//            row.setTemporaryReduceSalary2(e.getLastMonthReduce().subtract(e.getNextMonthReduce()));       //上月未扣-本月未扣
            //扣款合计
            row.setReduceSalaryTotal(e.getReduceSalaryTotal());
            //实发合计
            row.setTotalPaid(e.getRealPayTotal());
            employeeSalaryDetails.add(row);
        });

        //1.工资汇总表
        //1.在编人员工资项金额   在编：【在线，血防占编】
        for (int i = 0; i < EcsConst.ESTAB_STR_ARR.length; i++) {
            String s = EcsConst.ESTAB_STR_ARR[i];
            EcsSalarySummaryExcel estabRow = getSalarySummaryRow3(employeeSalaryDetails,s);
            datas.add(estabRow);
        }
        //2.招聘人员工资项金额   招聘：【辅助岗位、聘用护理、聘用医技、聘用其他】
//        List<EcsSalarySummaryExcel> hireDatas = new ArrayList<>();
        for (int i = 0; i < EcsConst.HIRE_STR_ARR.length; i++) {
            String s = EcsConst.HIRE_STR_ARR[i];
            EcsSalarySummaryExcel hireRow = getSalarySummaryRow3(employeeSalaryDetails, s);
            datas.add(hireRow);
        }

        //3.临聘人员工资项金额   临聘：【聘用后勤、聘用其他-见习、聘用后勤-见习、聘用医技-见习、聘用护理-见习、辅助岗位=见习】
//        List<EcsSalarySummaryExcel> tempHireDatas = new ArrayList<>();
        for (int i = 0; i < EcsConst.TEMP_HIRE_STR_ARR.length; i++) {
            String s = EcsConst.TEMP_HIRE_STR_ARR[i];
            EcsSalarySummaryExcel tempHireRow = getSalarySummaryRow3(employeeSalaryDetails, s);
            datas.add(tempHireRow);
        }
        //4.借调人员
        for (int i = 0; i < EcsConst.SECONDMENT_STR_ARR.length; i++) {
            String s = EcsConst.SECONDMENT_STR_ARR[i];
            EcsSalarySummaryExcel secondmentRow = getSalarySummaryRow3(employeeSalaryDetails, s);
            datas.add(secondmentRow);
        }
        //5.返聘人员
        for (int i = 0; i < EcsConst.REHIRE_STR_ARR.length; i++) {
            String s = EcsConst.REHIRE_STR_ARR[i];
            EcsSalarySummaryExcel rehireRow = getSalarySummaryRow3(employeeSalaryDetails, s);
            datas.add(rehireRow);
        }

        //四。职工工资条  模板8列人员数据
        List<List<EcsSalaryPaySlipExcel>> paySlipDatas = new ArrayList<>(8);
        for (int i = 0; i < 8; i++) {
            paySlipDatas.add(new ArrayList<>());
        }
        for (int i = 0; i < employeeSalaryDetails.size(); i++) {
            //构造Obejct
            EcsSalarySummary3Excel ecsSalarySummary3Excel = employeeSalaryDetails.get(i);
            EcsSalaryPaySlipExcel paySlipRow = new EcsSalaryPaySlipExcel();
            paySlipRow.setEmpName(ecsSalarySummary3Excel.getEmpName());
            paySlipRow.setSalaryNum(ecsSalarySummary3Excel.getSalaryNum());
            paySlipRow.setTotalPaid(ecsSalarySummary3Excel.getTotalPaid());
            //给每列分配数据
            int step = i % 8;
            paySlipDatas.get(step).add(paySlipRow);
        }

        //五。个人三险两金科室汇总
        //查询个人三险两金(包括工会会费) 数据,按科室分组
        List<String> indiReimNames = Arrays.asList("pensionInsurance","medicalInsurance","unemploymentInsurance","housingFund","occupationalAnnuity","laborUnion");
        Map<String, List<EcsReimSalaryTaskDetailVo>> indiReduceGroup = taskDetails.stream()
                .filter(e -> indiReimNames.contains(e.getReimName()))
                .collect(Collectors.groupingBy(EcsReimSalaryTaskDetailVo::getOrgName));
        List<EcsSalaryIndiReduceExcel> indiReduceDatas = new ArrayList<>();
        indiReduceGroup.forEach((key, list) -> {
            EcsSalaryIndiReduceExcel indiReduceRow = new EcsSalaryIndiReduceExcel();
            indiReduceRow.setOrgName(key);
            //通过empType分组后，取每组第一个对象中的empCount
            indiReduceRow.setPeoNum(list.stream()
                    .collect(Collectors.groupingBy(EcsReimSalaryTaskDetailVo::getEmpType))
                    .values()
                    .stream()
                    .filter(group -> !group.isEmpty()) // 过滤掉空分组
                    .mapToInt(group -> group.get(0).getEmpCount())
                    .sum());
            indiReduceRow.setMonth(salaryTask.getFfMth().substring(0, 7));
            //养老保险
            indiReduceRow.setPensionInsurance(list.stream()
                    .filter(e -> StringUtils.equals(EcsConst.PENSION_INSURANCE, e.getReimName()))
                    .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            //医疗保险
            indiReduceRow.setMedicalInsurance(list.stream()
                    .filter(e -> StringUtils.equals(EcsConst.MEDICAL_INSURANCE, e.getReimName()))
                    .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            //失业保险
            indiReduceRow.setUnemploymentInsurance(list.stream()
                    .filter(e -> StringUtils.equals(EcsConst.UNEMPLOYMENT_INSURANCE, e.getReimName()))
                    .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            //住房基金
            indiReduceRow.setHousingFund(list.stream()
                    .filter(e -> StringUtils.equals(EcsConst.HOUSING_FUND, e.getReimName()))
                    .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            //职业年金
            indiReduceRow.setOccupationalAnnuity(list.stream()
                    .filter(e -> StringUtils.equals(EcsConst.OCCUPATION_ANNUITY, e.getReimName()))
                    .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            //工会会费
            indiReduceRow.setLaborUnion(list.stream()
                    .filter(e -> StringUtils.equals(EcsConst.LABOR_UNION, e.getReimName()))
                    .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            //个人所得税
            indiReduceRow.setPersonTax(list.stream()
                    .filter(e -> StringUtils.equals(EcsConst.PERSON_TAX, e.getReimName()))
                    .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            indiReduceDatas.add(indiReduceRow);
        });

        Map<String,String> res = new HashMap<>();
        String SALARY_TOTAL_TEMPLATE = "template/工资发放汇总表模板.xlsx";
        try(InputStream tempIs = OSSUtil.getObject(OSSConst.BUCKET_ECS,SALARY_TOTAL_TEMPLATE);
            ByteArrayOutputStream os = new ByteArrayOutputStream()) {

            //获取模板文件流，将数据写入到临时文件流中
            ExcelWriter excelWriter = EasyExcel.write(os).inMemory(true).withTemplate(tempIs).build();
            //写入第一个sheet页,工资汇总表
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();

            //写入工资分类数据
            excelWriter.fill(new FillWrapper("summaryDatas",datas),writeSheet);
            /*excelWriter.fill(new FillWrapper("estabDatas",estabDatas),writeSheet);
            excelWriter.fill(new FillWrapper("hireDatas",hireDatas),writeSheet);
            excelWriter.fill(new FillWrapper("tempHireDatas",tempHireDatas),writeSheet);*/

            //写入年月
            excelWriter.fill(title,writeSheet);
            //写入左右两侧备注信息
//            excelWriter.fill(new FillWrapper("abstDatas",abstDatas),writeSheet);
//            excelWriter.fill(new FillWrapper("left",left),writeSheet);
//            excelWriter.fill(new FillWrapper("right",right),writeSheet);

            //写入第二个sheet页，在编招聘工资统计表(按照部门)
            WriteSheet writeSheet2 = EasyExcel.writerSheet(1).build();
            excelWriter.fill(title,writeSheet2);
            excelWriter.fill(new FillWrapper("zbzpDatas",zbzpDatas),writeSheet2);

            //写入第三个sheet页，个人工资明细
            WriteSheet writeSheet3 = EasyExcel.writerSheet(2).build();
            excelWriter.fill(new FillWrapper("employeeSalaryDatas",employeeSalaryDetails),writeSheet3);
            title.put("peoNum",String.valueOf(employeeSalaryDetails.size()));
            excelWriter.fill(title,writeSheet3);

            //写入第四个sheet页，工资条
            WriteSheet writeSheet4 = EasyExcel.writerSheet(3).build();
            for (int i = 0; i < 8; i++) {
                List<EcsSalaryPaySlipExcel> paySlipDatasRow = paySlipDatas.get(i);
                excelWriter.fill(new FillWrapper("paySlipDatas" + i,paySlipDatasRow),writeSheet4);
            }
            //写入第五个sheet页  个人三险两金+工会会费
            WriteSheet writeSheet5 = EasyExcel.writerSheet(4).build();
            excelWriter.fill(new FillWrapper("indiReduceDatas",indiReduceDatas),writeSheet5);

            title.put("peoNum",String.valueOf(employeeSalaryDetails.size()));
            excelWriter.fill(title,writeSheet4);

            //强制公式计算
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            workbook.getCreationHelper().createFormulaEvaluator().evaluateAll();

            excelWriter.finish();

            InputStream is = new ByteArrayInputStream(os.toByteArray());
            String path = "reim/item/" + ULIDUtil.generate() + ".xlsx";
            String name = title.get("year")+"年" + title.get("month")+"月工资发放表.xlsx";
            res.put("path",path);
            res.put("name",name);
            OSSUtil.uploadFile(OSSConst.BUCKET_ECS,path,is);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        //获取模板

        return res;
    }

    private EcsSalarySummaryExcel getSalarySummaryRow(List<EcsReimSalaryTaskDetailVo> list,String empType) {
        EcsSalarySummaryExcel row = new EcsSalarySummaryExcel();
        int peoNums = list.stream()
                .filter(e -> StringUtils.equals(empType, e.getEmpType()))
                .collect(Collectors.groupingBy(
                        vo -> vo.getOrgId() == null ? "UNKNOWN" : vo.getOrgId(), // 处理 null 的 orgId
                        Collectors.toList()
                ))
                .values()
                .stream()
                .mapToInt(group -> group.isEmpty() ? 0 : group.get(0).getEmpCount()) // 取每个分组的第一个元素的 empCount
                .sum();

        row.setPeoNum(peoNums);
        //性质
        row.setEmpType(empType);
        //岗位工资
        row.setPostSalary(getDiffSalaryTotal(list,EcsConst.POST_SALARY,empType));
        //护士10%
        row.setNurseSalary(getDiffSalaryTotal(list,EcsConst.NURSE_SALARY,empType));
        //薪级工资
        row.setSalGradeSalary(getDiffSalaryTotal(list,EcsConst.SAL_GRADE_SALARY,empType));
        //基础绩效
        row.setBasicPerf(getDiffSalaryTotal(list,EcsConst.BASIC_PERF,empType));
        //护龄补贴
        row.setAgeSalary(getDiffSalaryTotal(list,EcsConst.AGE_SALARY,empType));
        //驾驶员津贴 （暂无）
        //通讯费补贴 （暂无）
        //生活补贴
        row.setLifeSalary(getDiffSalaryTotal(list,EcsConst.LIFE_SALARY,empType));
        //地区附件津贴
        row.setAreaSalary(getDiffSalaryTotal(list,EcsConst.AREA_SALARY,empType));
        //人力临时增加
        row.setTemporaryAddSalary(getDiffSalaryTotal(list,EcsConst.TEMPORARY_ADD_SALARY,empType));
        //财务临时增加
        row.setTemporaryAddSalary2(getDiffSalaryTotal(list,EcsConst.TEMPORARY_ADD_SALARY2,empType));

        //应发合计
        BigDecimal shouldPayTotal = BigDecimal.ZERO;
        shouldPayTotal = shouldPayTotal.add(row.getPostSalary())
                .add(row.getSalGradeSalary())
                .add(row.getNurseSalary())
                .add(row.getAreaSalary())
                .add(row.getAgeSalary())
                .add(row.getBasicPerf())
                .add(row.getLifeSalary())
                .add(row.getTemporaryAddSalary())
                .add(row.getTemporaryAddSalary2());
        row.setTotalPayable(shouldPayTotal);

        //养老保险
        row.setPensionInsurance(getDiffSalaryTotal(list,EcsConst.PENSION_INSURANCE,empType));
        //医疗保险
        row.setMedicalInsurance(getDiffSalaryTotal(list,EcsConst.MEDICAL_INSURANCE,empType));
        //失业保险
        row.setUnemploymentInsurance(getDiffSalaryTotal(list,EcsConst.UNEMPLOYMENT_INSURANCE,empType));
        //住房基金
        row.setHousingFund(getDiffSalaryTotal(list,EcsConst.HOUSING_FUND,empType));
        //职业年金
        row.setOccupationalAnnuity(getDiffSalaryTotal(list,EcsConst.OCCUPATION_ANNUITY,empType));
        //工会会费
        row.setLaborUnion(getDiffSalaryTotal(list,EcsConst.LABOR_UNION,empType));
        //个人所得税
        row.setPersonTax(getDiffSalaryTotal(list,EcsConst.PERSON_TAX,empType));
        //房租费
        row.setRent(getDiffSalaryTotal(list,EcsConst.RENT,empType));
        //水费
        row.setWaterCharge(getDiffSalaryTotal(list,EcsConst.WATER_CHARGE,empType));
        //人力临时扣款
        row.setTemporaryReduceSalary(getDiffSalaryTotal(list,EcsConst.TEMPORARY_REDUCE_SALARY,empType));
        //财务临时扣款
        row.setTemporaryReduceSalary2(getDiffSalaryTotal(list,EcsConst.TEMPORARY_REDUCE_SALARY2,empType));
        BigDecimal reduceSalaryTotal = BigDecimal.ZERO;
        reduceSalaryTotal = reduceSalaryTotal.add(row.getPensionInsurance())
                .add(row.getMedicalInsurance())
                .add(row.getUnemploymentInsurance())
                .add(row.getHousingFund())
                .add(row.getOccupationalAnnuity())
                .add(row.getLaborUnion())
                .add(row.getPersonTax())
                .add(row.getTemporaryReduceSalary())
                .add(row.getTemporaryReduceSalary2());
        row.setReduceSalaryTotal(reduceSalaryTotal);
        //实发合计
        BigDecimal totalPaid = shouldPayTotal.subtract(reduceSalaryTotal);
        row.setTotalPaid(totalPaid);
        return row;
    }

    private EcsSalarySummaryExcel getSalarySummaryRow3(List<EcsSalarySummary3Excel> list,String empType) {
        EcsSalarySummaryExcel row = new EcsSalarySummaryExcel();
        List<EcsSalarySummary3Excel> filterList = list.stream()
                .filter(e -> StringUtils.equals(empType, e.getEmpType()))
                .collect(Collectors.toList());

        row.setPeoNum(filterList.size());
        //性质
        row.setEmpType(empType);
        //岗位工资
        row.setPostSalary(filterList.stream().map(obj -> Optional.ofNullable(obj.getPostSalary()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //护士10%
        row.setNurseSalary(filterList.stream().map(obj -> Optional.ofNullable(obj.getNurseSalary()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //薪级工资
        row.setSalGradeSalary(filterList.stream().map(obj -> Optional.ofNullable(obj.getSalGradeSalary()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //基础绩效
        row.setBasicPerf(filterList.stream().map(obj -> Optional.ofNullable(obj.getBasicPerf()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //护龄补贴
        row.setAgeSalary(filterList.stream().map(obj -> Optional.ofNullable(obj.getAgeSalary()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //驾驶员津贴 （暂无）
        //通讯费补贴 （暂无）
        //生活补贴
        row.setLifeSalary(filterList.stream().map(obj -> Optional.ofNullable(obj.getLifeSalary()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //地区附件津贴
        row.setAreaSalary(filterList.stream().map(obj -> Optional.ofNullable(obj.getAreaSalary()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //人力临时增加
        row.setTemporaryAddSalary(filterList.stream().map(obj -> Optional.ofNullable(obj.getTemporaryAddSalary()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //财务临时增加
        row.setTemporaryAddSalary2(filterList.stream().map(obj -> Optional.ofNullable(obj.getTemporaryAddSalary2()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));

        //应发合计
        BigDecimal shouldPayTotal = BigDecimal.ZERO;
        shouldPayTotal = shouldPayTotal.add(row.getPostSalary())
                .add(row.getSalGradeSalary())
                .add(row.getNurseSalary())
                .add(row.getAreaSalary())
                .add(row.getAgeSalary())
                .add(row.getBasicPerf())
                .add(row.getLifeSalary())
                .add(row.getTemporaryAddSalary())
                .add(row.getTemporaryAddSalary2());
        row.setTotalPayable(shouldPayTotal);

        //养老保险
        row.setPensionInsurance(filterList.stream().map(obj -> Optional.ofNullable(obj.getPensionInsurance()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //医疗保险
        row.setMedicalInsurance(filterList.stream().map(obj -> Optional.ofNullable(obj.getMedicalInsurance()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //失业保险
        row.setUnemploymentInsurance(filterList.stream().map(obj -> Optional.ofNullable(obj.getUnemploymentInsurance()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //住房基金
        row.setHousingFund(filterList.stream().map(obj -> Optional.ofNullable(obj.getHousingFund()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //职业年金
        row.setOccupationalAnnuity(filterList.stream().map(obj -> Optional.ofNullable(obj.getOccupationalAnnuity()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //工会会费
        row.setLaborUnion(filterList.stream().map(obj -> Optional.ofNullable(obj.getLaborUnion()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //个人所得税
        row.setPersonTax(filterList.stream().map(obj -> Optional.ofNullable(obj.getPersonTax()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //房租费
//        row.setRent(filterList.stream().map(obj -> Optional.ofNullable(obj.getRent()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //水费
//        row.setWaterCharge(filterList.stream().map(obj -> Optional.ofNullable(obj.getWaterCharge()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //人力临时扣款
        row.setTemporaryReduceSalary(filterList.stream().map(obj -> Optional.ofNullable(obj.getTemporaryReduceSalary()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        //财务临时扣款
        row.setTemporaryReduceSalary2(filterList.stream().map(obj -> Optional.ofNullable(obj.getTemporaryReduceSalary2()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add));
        BigDecimal reduceSalaryTotal = BigDecimal.ZERO;
        reduceSalaryTotal = reduceSalaryTotal.add(row.getPensionInsurance())
                .add(row.getMedicalInsurance())
                .add(row.getUnemploymentInsurance())
                .add(row.getHousingFund())
                .add(row.getOccupationalAnnuity())
                .add(row.getLaborUnion())
                .add(row.getPersonTax())
                .add(row.getTemporaryReduceSalary())
                .add(row.getTemporaryReduceSalary2());
        row.setReduceSalaryTotal(reduceSalaryTotal);
        //实发合计
        BigDecimal totalPaid = shouldPayTotal.subtract(reduceSalaryTotal);
        row.setTotalPaid(totalPaid);
        return row;
    }


    private EcsSalarySummary2Excel getSalarySummaryRow2(List<EcsReimSalaryTaskDetailVo> list,String orgName,String month) {
        //部门名称
        EcsSalarySummary2Excel row = new EcsSalarySummary2Excel();
        row.setOrgName(orgName);
        //人数
        int peoNums = list.stream()
                .collect(Collectors.groupingBy(EcsReimSalaryTaskDetailVo::getEmpType))
                .values()
                .stream()
                .mapToInt(group -> group.isEmpty() ? 0 : group.get(0).getEmpCount()) // 取每个分组的第一个元素的 empCount
                .sum();
        row.setPeoNum(peoNums);
        //发放月份
        row.setMonth(month);
        //岗位工资
        row.setPostSalary(getDiffSalaryTotal2(list,EcsConst.POST_SALARY));
        //薪级工资
        row.setSalGradeSalary(getDiffSalaryTotal2(list,EcsConst.SAL_GRADE_SALARY));
        //护士10%
        row.setNurseSalary(getDiffSalaryTotal2(list,EcsConst.NURSE_SALARY));
        //地区附件津贴
        row.setAreaSalary(getDiffSalaryTotal2(list,EcsConst.AREA_SALARY));
        //护龄补贴
        row.setAgeSalary(getDiffSalaryTotal2(list,EcsConst.AGE_SALARY));
        //驾驶员津贴（暂无）
        //通讯费补贴（暂无）
        //基础绩效
        row.setBasicPerf(getDiffSalaryTotal2(list,EcsConst.BASIC_PERF));
        //生活补贴
        row.setLifeSalary(getDiffSalaryTotal2(list,EcsConst.LIFE_SALARY));

        //人力临时增加
        row.setTemporaryAddSalary(getDiffSalaryTotal2(list,EcsConst.TEMPORARY_ADD_SALARY));
        //财务临时增加
        row.setTemporaryAddSalary2(getDiffSalaryTotal2(list,EcsConst.TEMPORARY_ADD_SALARY2));
        //应发合计
        BigDecimal shouldPayTotal = BigDecimal.ZERO;
        shouldPayTotal = shouldPayTotal.add(row.getPostSalary())
                .add(row.getSalGradeSalary())
                .add(row.getNurseSalary())
                .add(row.getAreaSalary())
                .add(row.getAgeSalary())
                .add(row.getBasicPerf())
                .add(row.getLifeSalary())
                .add(row.getTemporaryAddSalary())
                .add(row.getTemporaryAddSalary2());
        row.setTotalPayable(shouldPayTotal);

        //养老保险
        row.setPensionInsurance(getDiffSalaryTotal2(list,EcsConst.PENSION_INSURANCE));
        //医疗保险
        row.setMedicalInsurance(getDiffSalaryTotal2(list,EcsConst.MEDICAL_INSURANCE));
        //失业保险
        row.setUnemploymentInsurance(getDiffSalaryTotal2(list,EcsConst.UNEMPLOYMENT_INSURANCE));
        //住房基金
        row.setHousingFund(getDiffSalaryTotal2(list,EcsConst.HOUSING_FUND));
        //职业年金
        row.setOccupationalAnnuity(getDiffSalaryTotal2(list,EcsConst.OCCUPATION_ANNUITY));
        //工会会费
        row.setLaborUnion(getDiffSalaryTotal2(list,EcsConst.LABOR_UNION));
        //个人所得税
        row.setPersonTax(getDiffSalaryTotal2(list,EcsConst.PERSON_TAX));
        //人力临时扣款
        row.setTemporaryReduceSalary(getDiffSalaryTotal2(list,EcsConst.TEMPORARY_REDUCE_SALARY));
        //财务临时扣款
        row.setTemporaryReduceSalary2(getDiffSalaryTotal2(list,EcsConst.TEMPORARY_REDUCE_SALARY2));
        //扣款合计
        BigDecimal reduceSalaryTotal = BigDecimal.ZERO;
        reduceSalaryTotal = reduceSalaryTotal.add(row.getPensionInsurance())
                .add(row.getMedicalInsurance())
                .add(row.getUnemploymentInsurance())
                .add(row.getHousingFund())
                .add(row.getOccupationalAnnuity())
                .add(row.getLaborUnion())
                .add(row.getPersonTax())
                .add(row.getTemporaryReduceSalary())
                .add(row.getTemporaryReduceSalary2());
        row.setReduceSalaryTotal(reduceSalaryTotal);
        //实发合计
        BigDecimal totalPaid = shouldPayTotal.subtract(reduceSalaryTotal);
        row.setTotalPaid(totalPaid);
        return row;

    }

    private BigDecimal getDiffSalaryTotal(List<EcsReimSalaryTaskDetailVo> list, String salaryType,String empType) {
        return list.stream()
                .filter(e -> StringUtils.equals(e.getReimName(), salaryType) && StringUtils.equals(e.getEmpType(),empType))
                .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getDiffSalaryTotal2(List<EcsReimSalaryTaskDetailVo> list, String salaryType) {
        return list.stream()
                .filter(e -> StringUtils.equals(e.getReimName(), salaryType))
                .map(EcsReimSalaryTaskDetailVo::getReimAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
