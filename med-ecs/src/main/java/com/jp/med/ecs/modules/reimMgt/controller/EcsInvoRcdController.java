package com.jp.med.ecs.modules.reimMgt.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsInvoRcdReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsInvoRcdWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
@Api(value = "发票记录", tags = "发票记录")
@RestController
@RequestMapping("ecsInvoRcd")
public class EcsInvoRcdController {

    @Autowired
    private EcsInvoRcdReadService ecsInvoRcdReadService;

    @Autowired
    private EcsInvoRcdWriteService ecsInvoRcdWriteService;

    /**
     * hrp库
     */
    @ApiOperation("查询发票记录")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsInvoRcdDto dto){
        return CommonResult.paging(ecsInvoRcdReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增发票记录")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsInvoRcdDto dto){
        ecsInvoRcdWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改发票记录")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsInvoRcdDto dto){
        ecsInvoRcdWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除发票记录")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsInvoRcdDto dto){
        ecsInvoRcdWriteService.invoDel(dto);
        return CommonResult.success();
    }

    /**
     * 查询发票校验记录及明细
     * @param dto
     * @return
     */
    @ApiOperation("查询发票校验记录")
    @PostMapping("/rcdChk")
    public CommonResult<?> queryEcsInvoRcdChk(@RequestBody EcsInvoRcdDto dto){
        return CommonResult.success(ecsInvoRcdReadService.queryEcsInvoRcdChk(dto));
    }

    /**
     * 查询发票识别信息
     * @param dto
     * @return
     */
    @ApiOperation("查询发票识别信息")
    @PostMapping("/rcdRecogn")
    public CommonResult<?> queryEcsInvoRcdRecogn(@RequestBody EcsInvoRcdDto dto){
        return CommonResult.success(ecsInvoRcdReadService.queryEcsInvoRcdRecogn(dto));
    }

    /**
     * 发票人工校正申请
     * @param dto
     * @return
     */
    @ApiOperation("发票人工校正")
    @PostMapping("/invoAmend")
    public CommonResult<?> manualAmendInvo(@RequestBody EcsInvoRcdDto dto){
        ecsInvoRcdWriteService.manualAmendInvo(dto);
        return CommonResult.success();
    }

    /**
     * 发票校正审核
     * @param dto
     * @return
     */
    @ApiOperation("发票校正审核")
    @PostMapping("/invoAudit")
    public CommonResult<?> invoAudit(@RequestBody EcsInvoRcdDto dto){
        ecsInvoRcdWriteService.invoAudit(dto);
        return CommonResult.success();
    }

    /**
     * 查询发票待审核数量
     * @param dto
     * @return
     */
    @ApiOperation("查询发票待审核数量")
    @PostMapping("/queryInvoAuditWarnNum")
    public CommonResult<?> queryInvoAuditWarnNum(@RequestBody EcsInvoRcdDto dto) {
        return CommonResult.success(ecsInvoRcdReadService.queryInvoAuditWarnNum(dto));
    }


}
