package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.dto.ecs.EcsReimFixedAsstDetailDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.config.service.read.EcsReimFixedAsstDetailReadService;
import com.jp.med.ecs.modules.config.service.write.EcsReimFixedAsstDetailWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 固定项辅助配置
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Api(value = "固定项辅助配置", tags = "固定项辅助配置")
@RestController
@RequestMapping("ecsReimFixedAsstDetail")
public class EcsReimFixedAsstDetailController {

    @Autowired
    private EcsReimFixedAsstDetailReadService ecsReimFixedAsstDetailReadService;

    @Autowired
    private EcsReimFixedAsstDetailWriteService ecsReimFixedAsstDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询固定项辅助配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimFixedAsstDetailDto dto){
        return CommonResult.paging(ecsReimFixedAsstDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增${comments}")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsReimFixedAsstDetailDto dto){
        ecsReimFixedAsstDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改${comments}")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimFixedAsstDetailDto dto){
        ecsReimFixedAsstDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除${comments}")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimFixedAsstDetailDto dto){
        ecsReimFixedAsstDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
