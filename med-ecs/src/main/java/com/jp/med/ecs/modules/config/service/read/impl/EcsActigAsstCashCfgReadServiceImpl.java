package com.jp.med.ecs.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.common.util.TreeNewUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsActigAsstCashCfgReadMapper;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstCashCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstCashCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsActigAsstCashCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsActigAsstCashCfgReadServiceImpl extends ServiceImpl<EcsActigAsstCashCfgReadMapper, EcsActigAsstCashCfgDto> implements EcsActigAsstCashCfgReadService {

    @Autowired
    private EcsActigAsstCashCfgReadMapper ecsActigAsstCashCfgReadMapper;

    @Override
    public List<EcsActigAsstCashCfgVo> queryList(EcsActigAsstCashCfgDto dto) {
        TreeNewUtil<String, EcsActigAsstCashCfgVo> treeNewUtil = new TreeNewUtil<>();
        return treeNewUtil.buildTree(ecsActigAsstCashCfgReadMapper.queryList(dto));
    }

}
