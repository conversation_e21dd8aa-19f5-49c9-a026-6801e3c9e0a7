package com.jp.med.ecs.modules.reimMgt.controller;

import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimDeprTaskReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimDeprTaskWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 费用报销-折旧凭证
 * <AUTHOR>
 * @email -
 * @date 2025-02-07 16:08:24
 */
@Api(value = "费用报销-折旧凭证", tags = "费用报销-折旧凭证")
@RestController
@RequestMapping("ecsReimDeprTask")
public class EcsReimDeprTaskController {

    @Autowired
    private EcsReimDeprTaskReadService ecsReimDeprTaskReadService;

    @Autowired
    private EcsReimDeprTaskWriteService ecsReimDeprTaskWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询费用报销-折旧凭证")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsReimDeprTaskDto dto){
        return CommonResult.paging(ecsReimDeprTaskReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询费用报销-折旧凭证")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimDeprTaskDto dto){
        return CommonResult.success(ecsReimDeprTaskReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增费用报销-折旧凭证")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsReimDeprTaskDto dto){
        ecsReimDeprTaskWriteService.save(dto);
        return CommonResult.success(dto.getId());
    }

    /**
     * 修改
     */
    @ApiOperation("修改费用报销-折旧凭证")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimDeprTaskDto dto){
        ecsReimDeprTaskWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除费用报销-折旧凭证")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimDeprTaskDto dto){
        ecsReimDeprTaskWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 保存折旧任务
     * @param dto
     * @return
     */
    @ApiOperation("保存折旧任务")
    @PostMapping("/saveDeprTask")
    public CommonFeignResult saveDeprTask(@RequestBody EcsReimDeprTaskDto dto) {
        ecsReimDeprTaskWriteService.saveDeprTask(dto);
        return CommonFeignResult.build();
    }

}
