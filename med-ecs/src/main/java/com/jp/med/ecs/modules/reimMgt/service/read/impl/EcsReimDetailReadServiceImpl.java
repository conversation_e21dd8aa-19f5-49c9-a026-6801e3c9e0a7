package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.aspose.words.Document;
import com.aspose.words.ImportFormatMode;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.*;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import com.deepoove.poi.policy.TableRenderPolicy;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.PdfUtil;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugPayDetailReadMapper;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.entity.*;
import com.jp.med.ecs.modules.reimMgt.enums.EcsReimTemplateEnum;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDetailReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimFileRecordReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimPurcTaskReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimSalaryTaskReadMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimDetailReadService;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimFileRecordVo;
import com.jp.med.ecs.modules.reimMgt.vo.HomeMsgNoteVo;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class EcsReimDetailReadServiceImpl extends ServiceImpl<EcsReimDetailReadMapper, EcsReimDetailDto> implements EcsReimDetailReadService {

    @Autowired
    private EcsReimDetailReadMapper ecsReimDetailReadMapper;

    @Autowired
    private EcsReimTravelApprReadServiceImpl ecsReimTravelApprReadService;

    @Autowired
    private AuditFeignService auditFeignService;

    @Autowired
    private EcsReimFileRecordReadMapper ecsReimFileRecordReadMapper;
    
    @Autowired
    private EcsReimSalaryTaskReadMapper ecsReimSalaryTaskReadMapper;

    @Autowired
    private EcsReimPurcTaskReadMapper ecsReimPurcTaskReadMapper;

    @Autowired
    private EcsDrugPayDetailReadMapper ecsDrugPayDetailReadMapper;

    @Override
    public List<EcsReimDetailVo> queryList(EcsReimDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        if (!Objects.isNull(dto.getAudit())) {
            if (dto.getAudit()) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        } /*else {
            if (StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        }*/
        //如果是管理员chker 和 appyer都设置为空
        List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryList(dto);
        if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
            ecsReimDetailVos.forEach(ecsReimDetailVo -> {
                List<String> time = new ArrayList<>();
                time.add(ecsReimDetailVo.getEvectionBegnTime());
                time.add(ecsReimDetailVo.getEvectionEndTime());
                ecsReimDetailVo.setEvectionTime(time);
            });
        }
        return ecsReimDetailVos;
    }

    @Override
    public List<EcsReimDetailVo> queryNoPageList(EcsReimDetailDto dto) {
        if (!Objects.isNull(dto.getAudit())) {
            if (dto.getAudit()) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        } /*else {
            if (StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        }*/
        //如果是管理员chker 和 appyer都设置为空
        List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryList(dto);
        if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
            ecsReimDetailVos.forEach(ecsReimDetailVo -> {
                List<String> time = new ArrayList<>();
                time.add(ecsReimDetailVo.getEvectionBegnTime());
                time.add(ecsReimDetailVo.getEvectionEndTime());
                ecsReimDetailVo.setEvectionTime(time);
            });
        }
        return ecsReimDetailVos;
    }

    @Override
    public List<EcsReimDetailVo> queryNoPageListNew(EcsReimDetailDto dto) {
        if (!Objects.isNull(dto.getAudit())) {
            if (dto.getAudit()) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        } /*else {
            if (StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        }*/
        //如果是管理员chker 和 appyer都设置为空
        List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryListNew(dto);
        if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
            ecsReimDetailVos.forEach(ecsReimDetailVo -> {
                List<String> time = new ArrayList<>();
                time.add(ecsReimDetailVo.getEvectionBegnTime());
                time.add(ecsReimDetailVo.getEvectionEndTime());
                ecsReimDetailVo.setEvectionTime(time);
            });
        }
        return ecsReimDetailVos;
    }

    @Override
    public List<EcsReimDetailVo> queryListNew(EcsReimDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        if (!Objects.isNull(dto.getAudit())) {
            if (dto.getAudit()) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        } /*else {
            if (StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        }*/
        //针对查询某个报销详情时，只通过id查询导致type丢失的问题
        if (!Objects.isNull(dto.getId()) && Objects.isNull(dto.getType()) && Objects.isNull(dto.getApprDeptType())) {
            EcsReimDetailDto ecsReimDetailDto = ecsReimDetailReadMapper.selectById(dto.getId());
            dto.setType(ecsReimDetailDto.getType());
        }
        List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryListNew(dto);
        if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
            ecsReimDetailVos.forEach(ecsReimDetailVo -> {
                List<String> time = new ArrayList<>();
                time.add(ecsReimDetailVo.getEvectionBegnTime());
                time.add(ecsReimDetailVo.getEvectionEndTime());
                ecsReimDetailVo.setEvectionTime(time);
            });
        }
        return ecsReimDetailVos;
    }

    @Override
    public List<EcsReimDetailVo> queryListMultiTrip(EcsReimDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        if (!Objects.isNull(dto.getAudit())) {
            if (dto.getAudit()) {
                // 审核页面
                dto.setChker(dto.getSysUser().getHrmUser().getEmpCode());
            } else {
                dto.setAppyer(dto.getSysUser().getHrmUser().getEmpCode());
            }
        }
        //针对查询某个报销详情时，只通过id查询导致type丢失的问题
        if (!Objects.isNull(dto.getId()) && Objects.isNull(dto.getType())) {
            EcsReimDetailDto ecsReimDetailDto = ecsReimDetailReadMapper.selectById(dto.getId());
            if (!Objects.isNull(ecsReimDetailDto)) {
                dto.setType(ecsReimDetailDto.getType());
            }
        }
        List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryListNew(dto);
        if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
            ecsReimDetailVos.forEach(ecsReimDetailVo -> {
                List<String> time = new ArrayList<>();
                time.add(ecsReimDetailVo.getEvectionBegnTime());
                time.add(ecsReimDetailVo.getEvectionEndTime());
                ecsReimDetailVo.setEvectionTime(time);
            });
        }
        if (CollectionUtil.isNotEmpty(dto.getIds()) || StrUtil.isNotBlank(dto.getProcessInstanceId())){
            return ecsReimDetailVos;
        }

        Map<String, List<EcsReimDetailVo>> collect = ecsReimDetailVos.stream()
                .collect(Collectors.groupingBy(EcsReimDetailVo::getProcessInstanceId,LinkedHashMap::new,
                        Collectors.toList()) );
        List<EcsReimDetailVo> result = new ArrayList<>();
        collect.forEach((key, value) -> result.add(value.get(0)));
        return result;
    }

    @Override
    public Map<String, Object> queryItemDetail(EcsReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        //查询项目信息
        List<EcsReimItemDetail> itemDetails = ecsReimDetailReadMapper.queryItemDetail(dto);
        //查询项目辅助项信息
        List<EcsReimSubsItemDetail> subsItemDetails = ecsReimDetailReadMapper.querySubsItemDetail(dto);
        // 报销
        dto.setType(MedConst.TYPE_2);
        List<EcsReimPsnDetail> psnDetails = ecsReimDetailReadMapper.queryPsnDetail(dto);
        // 查询辅项信息
        EcsReimItemDetail itemDetail = new EcsReimItemDetail();
        itemDetail.setReimDetailId(dto.getId());
        itemDetail.setSupType(MedConst.TYPE_1);
        List<EcsReimAsstDetail> reimAsstDetails = ecsReimDetailReadMapper.queryReimAsstDetail(itemDetail);
        // 查询附件列表
        List<EcsReimFileRecordVo> fileRecordVos = ecsReimFileRecordReadMapper.queryByAttCode(dto.getAttCode());
        //查询报销详情 ，如果是零星采购或者物资采购
        EcsReimDetailDto ecsReimDetailDto = ecsReimDetailReadMapper.selectById(dto.getId());
        if (StringUtils.equals(ecsReimDetailDto.getType(), MedConst.TYPE_8) || StringUtils.equals(ecsReimDetailDto.getType(), MedConst.TYPE_10)) {
            if (!Objects.isNull(ecsReimDetailDto.getPayRcptId())) {
                EcsDrugPayDetailDto ecsDrugPayDetailDto = ecsDrugPayDetailReadMapper.selectById(ecsReimDetailDto.getPayRcptId());
                fileRecordVos.addAll(ecsReimFileRecordReadMapper.queryByAttCode(ecsDrugPayDetailDto.getAttCode()));
            }
        }
        return new HashMap<>(){
            {
                put("itemDetails", itemDetails);
                put("subsItemDetails", subsItemDetails);
                put("psnDetails", psnDetails);
                put("reimAsstDetails", reimAsstDetails);
                put("fileRecords",fileRecordVos);
            }
        };
    }

    @Override
    public List<EcsReimItemDetail> queryItemDetail2(EcsReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimDetailReadMapper.queryItemDetail(dto);
    }

    @Override
    public List<EcsReimPsnDetail> queryDeptAmt(EcsReimDetailDto dto) {
        return ecsReimDetailReadMapper.queryDeptAmt(dto);
    }

    /**
     * 查询租车费金额
     * @param dto
     * @return
     */
    @Override
    public List<EcsReimPsnDetail> queryZCFDeptAmt(EcsReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimDetailReadMapper.queryZCFDeptAmt(dto);
    }

    @Override
    public List<EcsReimPsnDetail> psnDetails(EcsReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimDetailReadMapper.queryPsnDetail(dto);
    }

    @Override
    public Map<Long, List<EcsReimPsnDetail>> psnDetailsMultiTrip(EcsReimDetailDto dto) {
        String processInstanceId = dto.getProcessInstanceId();
        List<EcsReimTravelApprDto> list = ecsReimTravelApprReadService.list(
                Wrappers.lambdaQuery(EcsReimTravelApprDto.class)
                        .eq(EcsReimTravelApprDto::getProcessInstanceId, processInstanceId)
        );
        List<Long> idList = list.stream().map(EcsReimTravelApprDto::getId).collect(Collectors.toList());
        dto.setEcsReimTravelApprIds(idList);
        dto.setSqlAutowiredHospitalCondition(true);
        List<EcsReimPsnDetail> ecsReimPsnDetails = ecsReimDetailReadMapper.queryPsnDetail(dto);
        return ecsReimPsnDetails.stream().collect(Collectors.groupingBy(EcsReimPsnDetail::getReimDetailId));
    }

    @Override
    public EcsReimDetailVo queryAppAuditDetail(EcsReimDetailDto dto) {
        // 查询审核流程
        dto.setSqlAutowiredHospitalCondition(true);
        List<AuditDetail> details = auditFeignService.getAuditDetails(new AuditDetail(dto.getAuditBchno(), dto.getSysUser().getHrmUser().getEmpCode(), OSSConst.BUCKET_ECS));
        List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryList(dto);
        EcsReimDetailVo reimVo = new EcsReimDetailVo();
        if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
            reimVo = ecsReimDetailVos.get(0);
            reimVo.setAppEvectionTime(reimVo.getEvectionBegnTime() + " 至 " + reimVo.getEvectionEndTime());
            details.sort(Comparator.comparing(AuditDetail::getChkSeq));
            reimVo.setAuditDetails(details);

            List<EcsReimPsnDetail> psnDetails = ecsReimTravelApprReadService.getPsnDetails(reimVo.getId(), MedConst.TYPE_1);
            if (CollectionUtil.isNotEmpty(psnDetails)) {
                reimVo.setTableDetails(psnDetails);
            }
        }
        return reimVo;
    }

    @Override
    public EcsReimDetailVo queryAppAuditDetail2(EcsReimDetailDto dto) {
        // 查询审核流程
        dto.setSqlAutowiredHospitalCondition(true);
        List<AuditDetail> details = auditFeignService.getAuditDetails(new AuditDetail(dto.getAuditBchno(), dto.getSysUser().getHrmUser().getEmpCode(), OSSConst.BUCKET_ECS));
        List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryList(dto);
        EcsReimDetailVo reimVo = new EcsReimDetailVo();
        if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
            reimVo = ecsReimDetailVos.get(0);
            details.sort(Comparator.comparing(AuditDetail::getChkSeq));
            reimVo.setAuditDetails(details);

            // 查询摘要详情
            dto.setId(reimVo.getId());
            List<EcsReimItemDetail> itemDetails = ecsReimDetailReadMapper.queryItemDetail(dto);
            if (CollectionUtil.isNotEmpty(itemDetails)) {
                reimVo.setTableDetails(itemDetails);
            }
        }
        return reimVo;
    }

    @Override
    public Integer queryExpenseAuditWarnNum(EcsReimDetailDto dto) {
        dto.setId(null);
        dto.setAudit(true);
        dto.setAuditFlag(MedConst.TYPE_1);
        dto.setPageNum(1);
        dto.setPageSize(99999);
        dto.setAuditState(Arrays.asList("3"));
        return queryList(dto).size();
    }

    @Override
    public List<EcsReimApprManage> queryTravelPsnInfo(EcsReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimDetailReadMapper.queryTravelPsnInfo(dto);
    }


    private void listAddToMap(List<Object> data,Map<String,Object> map) {
        for (int i = 0; i < data.size(); i++) {
            Map<String,Object> dataMap = BeanUtil.beanToMap(data.get(i));
            for (String key: dataMap.keySet()) {
                Object o = dataMap.get(key);
                if (!Objects.isNull(o)){
                    map.put(key+i, o);
                }
            }
        }
    }

    @Override
    public Map<String, String> queryEcsReimDoc(Map<String, Object> params) {
        //文件路径
        Map<String,String> fileInfo = new HashMap<>();
        //处理项目数据
        List<Object> items = (List<Object>) params.get("items");
        listAddToMap(items,params);
        //处理辅助项目数据
        List<Object> subItems = (List<Object>) params.get("subItems");
        listAddToMap(subItems,params);
        //处理审核流程中数据
        List<Object> list = (List<Object>) params.get("auditSteps");

        ConfigureBuilder builder = Configure.builder();
        //行循环插件
        HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy(true);

        //每行数量
        int columns = 3;
        double[] tableWidth = new double[]{2.3f,3.88f,2.3f,3.88f,2.3f,3.88f};
        if (StringUtils.equals(params.get("reimType").toString(),MedConst.TYPE_13)) {       //如果是借款流程，只有两列
            columns = 2;
            tableWidth = new double[] {3.45f,5.82,3.45f,5.82f};
        }
        //审核流程行数
        int auditNum = list.size();
        int rowsNums = (auditNum + columns -1)/ columns;

        List<RowRenderData> rows = new ArrayList<>(rowsNums);

        for (int i = 0; i< rowsNums;i++) {
            RowRenderData row;
            List<CellRenderData> cells = new ArrayList<>();
            for (int j = 0 ;j < columns; j++) {
                int index = i * columns + j;
                if (index < auditNum) {
                    //当前审核节点
                    Object step = list.get(index);
                    Map<String, Object> stepStr = BeanUtil.beanToMap(step);
                    //构造Cell
                    CellRenderData cellTitle = Cells.of()
                            .addParagraph(stepStr.get("title").toString()).center().create();
                    cells.add(cellTitle);
                    //是否有签名
                    boolean isSignNull = Objects.isNull(stepStr.get("sign"));
                    //是否有审核时间
                    boolean isTimeNull = Objects.isNull(stepStr.get("chkTime"));
                    Cells.CellBuilder of = Cells.of();
                    of.addParagraph(Paragraphs.of(Texts.of(stepStr.get("remark").toString()).create()).left().create());
                    if (isSignNull) {
                        of.addParagraph(Paragraphs.of(Texts.of("").create()).left().create());
                    } else {
                        String signUrl = stepStr.get("sign").toString();
                        String signUri = signUrl.split("\\?")[0];
                        if (signUrl.contains("hrp.zjxrmyy.cn:18090")) {
                            //替换为文件服务器真实地址
                            signUrl = signUrl.replace("https","http");
                            signUrl = signUrl.replace("hrp.zjxrmyy.cn:18090","10.2.233.10:4556");
                            //去掉oss
                            int ossIdx = signUrl.indexOf("oss");
                            if (ossIdx != -1) {
                                StringBuilder sb = new StringBuilder(signUrl);
                                sb.delete(ossIdx,ossIdx+4);
                                signUrl = sb.toString();
                            }
                        }

                        /*PictureRenderData pictureRenderData = Pictures.ofUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_ECS, stepStr.get("sign").toString())
                                , PictureType.suggestFileType(stepStr.get("sign").toString())).size(70, 30).altMeta("").create();*/
                        PictureRenderData pictureRenderData = Pictures.ofUrl(signUrl
                                , PictureType.suggestFileType(signUri)).size(70, 30).altMeta("").create();
                        //如果图片路径对应文件不存在，则使用字符串替代
                        if (Objects.isNull(pictureRenderData.getImage())) {
                            of.addParagraph(Paragraphs.of(Texts.of("").create()).left().create());
                        } else {
                            of.addParagraph(pictureRenderData);
                        }
                    }
                    of.addParagraph(Paragraphs.of(Texts.of(isTimeNull?"":stepStr.get("chkTime").toString()).create()).right().create());
                    cells.add(of.create());
                } else {
                    //行中为空的cell 也需要进行空白填充
                    CellRenderData cellTitle = Cells.of()
                            .addParagraph(Texts.of("").create()).create();
                    cells.add(cellTitle);
                    CellRenderData cellContent = Cells.of()
                            .addParagraph(Paragraphs.of(Texts.of("").create()).left().create())
                            .addParagraph(Paragraphs.of(Texts.of("").create()).create())
                            .addParagraph(Paragraphs.of(Texts.of("").create()).right().create())
                            .create();
                    cells.add(cellContent);
                }
            }
            row = Rows.of(cells.toArray(new CellRenderData[0])).create();
            rows.add(row);
        }

        //如果是打印零星采购，查询采购报销明细  (暂时不打印)
        /*if (StringUtils.equals(params.get("reimType").toString(),MedConst.TYPE_8)) {
            List<EcsReimPurcTaskDetailVo> details = ecsReimPurcTaskReadMapper.queryPurcTaskDetailByReimId((Integer) params.get("id"));
            params.put("stoinItems",details);
        }*/

        /*double[] tableWidth = new double[columns * 2];
        for (int i = 0; i < columns; i++) {
            tableWidth[i] = 2.3;
            tableWidth[i+1] = 3.88;
        }*/

//        TableRenderData table = Tables.of(rows.toArray(new RowRenderData[0])).width(18.54f,new double[] {2.3f,3.88f,2.3f,3.88f,2.3f,3.88f}).center().create();
        TableRenderData table = Tables.of(rows.toArray(new RowRenderData[0])).width(18.54f,tableWidth).center().create();
        params.put("auditTable",table);


        log.debug("---------------参数---------------"+new Gson().toJson(params));
        Configure config = builder
                .bind("reimItems",policy)
//                .bind("stoinItems",policy)
                .bind("auditTable",new TableRenderPolicy())
                .build();

        EcsReimTemplateEnum reimType = EcsReimTemplateEnum.getByType(params.get("reimType").toString());
        String templateName = reimType.getTemplatePath();
        //后续文件名可能拼接其他信息
        String fileName = reimType.getFileBaseName();
        try {
            //获取费用报销模板 差旅/培训/其他费用/工资....
            String filePath = PdfUtil.readOssRender(OSSConst.BUCKET_ECS,templateName,params,config);
            fileInfo.put("filePath",filePath);
            fileInfo.put("fileName",fileName);
        } catch (Exception e) {
            log.error("生成费用报销审批表失败",e);
            throw new AppException("生成费用报销审批表失败");
        }
        return fileInfo;
    }

    @Override
    public Map<String, String> queryEcsReimDocMultiTrip(List<Map<String, Object>> params) {
        //文件路径
        Map<String,String> fileInfo = new HashMap<>();
        List<ByteArrayOutputStream> wordStreams = new ArrayList<>();
        params.forEach(param -> {
            //处理项目数据
            List<Object> items = (List<Object>) param.get("items");
            listAddToMap(items,param);
            //处理辅助项目数据
            List<Object> subItems = (List<Object>) param.get("subItems");
            listAddToMap(subItems,param);
            //处理审核流程中数据
            List<Object> list = (List<Object>) param.get("auditSteps");
            ConfigureBuilder builder = Configure.builder();
            //行循环插件
            HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy(true);
            //每行数量
            int columns = 3;
            //审核流程行数
            int auditNum = list.size();
            int rowsNums = (auditNum + columns -1)/ columns;
            List<RowRenderData> rows = new ArrayList<>(rowsNums);
            for (int i = 0; i< rowsNums;i++) {
                RowRenderData row;
                List<CellRenderData> cells = new ArrayList<>();
                for (int j = 0 ;j < columns; j++) {
                    int index = i * columns + j;
                    if (index < auditNum) {
                        //当前审核节点
                        Object step = list.get(index);
                        Map<String, Object> stepStr = BeanUtil.beanToMap(step);
                        //构造Cell
                        CellRenderData cellTitle = Cells.of()
                                .addParagraph(stepStr.get("title").toString()).center().create();
                        cells.add(cellTitle);
                        //是否有签名
                        boolean isSignNull = Objects.isNull(stepStr.get("sign"));
                        //是否有审核时间
                        boolean isTimeNull = Objects.isNull(stepStr.get("chkTime"));
                        Cells.CellBuilder of = Cells.of();
                        of.addParagraph(Paragraphs.of(Texts.of(stepStr.get("remark").toString()).create()).left().create());
                        if (isSignNull) {
                            of.addParagraph(Paragraphs.of(Texts.of("").create()).left().create());
                        } else {
                            String signUrl = stepStr.get("sign").toString();
                            String signUri = signUrl.split("\\?")[0];
                            if (signUrl.contains("hrp.zjxrmyy.cn:18090")) {
                                //替换为文件服务器真实地址
                                signUrl = signUrl.replace("https","http");
                                signUrl = signUrl.replace("hrp.zjxrmyy.cn:18090","10.2.233.10:4556");
                                //去掉oss
                                int ossIdx = signUrl.indexOf("oss");
                                if (ossIdx != -1) {
                                    StringBuilder sb = new StringBuilder(signUrl);
                                    sb.delete(ossIdx,ossIdx+4);
                                    signUrl = sb.toString();
                                }
                            }

                        /*PictureRenderData pictureRenderData = Pictures.ofUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_ECS, stepStr.get("sign").toString())
                                , PictureType.suggestFileType(stepStr.get("sign").toString())).size(70, 30).altMeta("").create();*/
                            PictureRenderData pictureRenderData = Pictures.ofUrl(signUrl
                                    , PictureType.suggestFileType(signUri)).size(70, 30).altMeta("").create();
                            //如果图片路径对应文件不存在，则使用字符串替代
                            if (Objects.isNull(pictureRenderData.getImage())) {
                                of.addParagraph(Paragraphs.of(Texts.of("").create()).left().create());
                            } else {
                                of.addParagraph(pictureRenderData);
                            }
                        }
                        of.addParagraph(Paragraphs.of(Texts.of(isTimeNull?"":stepStr.get("chkTime").toString()).create()).right().create());
                        cells.add(of.create());
                    } else {
                        //行中为空的cell 也需要进行空白填充
                        CellRenderData cellTitle = Cells.of()
                                .addParagraph(Texts.of("").create()).create();
                        cells.add(cellTitle);
                        CellRenderData cellContent = Cells.of()
                                .addParagraph(Paragraphs.of(Texts.of("").create()).left().create())
                                .addParagraph(Paragraphs.of(Texts.of("").create()).create())
                                .addParagraph(Paragraphs.of(Texts.of("").create()).right().create())
                                .create();
                        cells.add(cellContent);
                    }
                }
                row = Rows.of(cells.toArray(new CellRenderData[0])).create();
                rows.add(row);
            }
            TableRenderData table = Tables.of(rows.toArray(new RowRenderData[0])).width(18.54f,new double[] {2.3f,3.88f,2.3f,3.88f,2.3f,3.88f}).center().create();
            param.put("auditTable",table);
            log.debug("---------------参数---------------"+new Gson().toJson(param));
            Configure config = builder
                    .bind("reimItems",policy)
//                .bind("stoinItems",policy)
                    .bind("auditTable",new TableRenderPolicy())
                    .build();
            EcsReimTemplateEnum reimType = EcsReimTemplateEnum.getByType(param.get("reimType").toString());
            String templateName = reimType.getTemplatePath();
            //后续文件名可能拼接其他信息
            String fileName = reimType.getFileBaseName();

            try {
                // 验证License
                if (!PdfUtil.isWordLicense()) {
                    return;
                }
                InputStream inputStream = OSSUtil.getObject(OSSConst.BUCKET_ECS, templateName);
                XWPFTemplate xwpfTemplate = XWPFTemplate.compile(inputStream,config);
                xwpfTemplate.render(param);
                // 文件输出流
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                // 选入后的数据写入流
                xwpfTemplate.write(bos);
                wordStreams.add(bos);
            }catch (Exception e) {
                log.error("合并多行程失败",e);
                throw new AppException("合并多行程失败");
            }

        });



        try {
            Document finalDoc = null;
            for (ByteArrayOutputStream wordStream : wordStreams) {
                Document doc = new Document(new ByteArrayInputStream(wordStream.toByteArray()));
                if(ObjectUtil.isEmpty(finalDoc)){
                    finalDoc = doc;
                }else{
                    finalDoc.appendDocument(doc, ImportFormatMode.USE_DESTINATION_STYLES);
                }
            }
            // 3. 转换为 PDF
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            finalDoc.save(pdfOutputStream, com.aspose.words.SaveFormat.PDF);

            // 4. 上传 PDF 到 OSS
            FileItem fileItem = PdfUtil.streamToMultipartFile(pdfOutputStream, "application/pdf", "merged.pdf");
            MultipartFile file = new CommonsMultipartFile(fileItem);
            String filePath = OSSUtil.uploadFile("temp", "", file);
            String fileName = "xxxx";
            fileInfo.put("filePath",filePath);
            fileInfo.put("fileName",fileName);
        }catch (Exception e) {
            log.error("生成申请审批表失败",e);
            throw new AppException("生成申请审批表失败");
        }
        return fileInfo;
    }

    @Override
    public List<HomeMsgNoteVo> msgNote(EcsReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(1,20);
        return ecsReimDetailReadMapper.msgNote(dto);
    }

    /**
     * 费用概况
     * @param dto
     * @return
     */
    @Override
    public Map<String, Object> expenseOverview(EcsReimDetailDto dto) {
        Map<String,Object> map = new HashMap<>();
        //当前月份
        LocalDate now = LocalDate.now();
        String monthStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String nexMonthStr = now.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        dto.setMonth(monthStr);
        //查询本月报销总额、申请单数
        List<EcsReimDetailVo> curMonthReims = ecsReimDetailReadMapper.queryReimDetailOfMonth(dto);
        //查询上月报销总额、申请单数
        dto.setMonth(nexMonthStr);
        List<EcsReimDetailVo> nextMonthReims = ecsReimDetailReadMapper.queryReimDetailOfMonth(dto);

        //查询部门报销总额  已付款的报销
        List<Integer> ids = curMonthReims.stream()
                .filter(item -> StrUtil.equals(item.getBusstas(), MedConst.TYPE_1))
                // 将 Long 转为 Integer（注意检查数值范围）
                .map(item -> item.getId().intValue())
                .collect(Collectors.toList());
        dto.setIds(ids);
        List<EcsReimItemDetail> ecsReimItemDetails = ecsReimDetailReadMapper.queryReimItemDetails(dto);
        return null;
    }

    /**
     * 报销趋势
     * @param dto
     * @return
     */
    @Override
    public Map<String, Object> expenseTrends(EcsReimDetailDto dto) {
        Map<String,Object> map = new HashMap<>();
        //查询部门报销总额
        return null;
    }

    @Override
    public Object myAppr(EcsReimDetailDto dto) {
        return null;
    }

    @Override
    public Object myAudit(EcsReimDetailDto dto) {
        return null;
    }
}
