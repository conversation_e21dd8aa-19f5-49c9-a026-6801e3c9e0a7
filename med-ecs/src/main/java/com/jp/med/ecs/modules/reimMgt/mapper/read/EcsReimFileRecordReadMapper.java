package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.jp.med.common.dto.ecs.EcsReimFileRecordDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimFileRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用报销-文件记录表
 * <AUTHOR>
 * @email -
 * @date 2024-07-04 01:46:46
 */
@Mapper
public interface EcsReimFileRecordReadMapper extends BaseMapper<EcsReimFileRecordDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimFileRecordVo> queryList(EcsReimFileRecordDto dto);

    /**
     * 通过附件编码查询
     * @param attCode
     * @return
     */
    List<EcsReimFileRecordVo> queryByAttCode(@Param("attCode") String attCode);
}
