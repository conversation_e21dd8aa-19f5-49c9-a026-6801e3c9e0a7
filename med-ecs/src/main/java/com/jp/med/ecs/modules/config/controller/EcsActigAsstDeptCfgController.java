package com.jp.med.ecs.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ecs.modules.config.dto.EcsActigAsstDeptCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsActigAsstDeptCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsActigAsstDeptCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 会计科目科室辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2024-02-01 16:36:57
 */
@Api(value = "会计科目科室辅助信息配置", tags = "会计科目科室辅助信息配置")
@RestController
@RequestMapping("ecsActigAsstDeptCfg")
public class EcsActigAsstDeptCfgController {

    @Autowired
    private EcsActigAsstDeptCfgReadService ecsActigAsstDeptCfgReadService;

    @Autowired
    private EcsActigAsstDeptCfgWriteService ecsActigAsstDeptCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询会计科目科室辅助信息配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsActigAsstDeptCfgDto dto){
        return CommonResult.success(ecsActigAsstDeptCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增会计科目科室辅助信息配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsActigAsstDeptCfgDto dto){
        ecsActigAsstDeptCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改会计科目科室辅助信息配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsActigAsstDeptCfgDto dto){
        ecsActigAsstDeptCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除会计科目科室辅助信息配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsActigAsstDeptCfgDto dto){
        ecsActigAsstDeptCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
