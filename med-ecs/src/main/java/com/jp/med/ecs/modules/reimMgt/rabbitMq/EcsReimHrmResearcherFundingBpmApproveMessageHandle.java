package com.jp.med.ecs.modules.reimMgt.rabbitMq;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.enums.ecs.ReimTypeEnum;
import com.jp.med.common.enums.ecs.ShareTypeEnum;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimItemDetail;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimSubsItemDetail;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDetailReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsInvoRcdWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDetailWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimTravelApprWriteMapper;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo;
import com.rabbitmq.client.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@Getter
@Setter
public class EcsReimHrmResearcherFundingBpmApproveMessageHandle extends AbstractBpmApproveMessageHandle {

    @Autowired
    private EcsReimDetailReadMapper ecsReimDetailReadMapper;

    @Autowired
    private EcsReimDetailWriteMapper ecsReimDetailWriteMapper;

    @Autowired
    private EcsInvoRcdWriteMapper ecsInvoRcdWriteMapper;

    @Autowired
    private EcsReimTravelApprWriteMapper ecsReimTravelApprWriteMapper;

    public String[] processIdentifier = {"REIM_HRM_RESEARCHER_FUNDING","REIM_HRM_PAPER_PAGE_CHARGE"};

    @Override
    @RabbitListener(queues = {"REIM_HRM_RESEARCHER_FUNDING","REIM_HRM_PAPER_PAGE_CHARGE"})
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        receiveMessage0(msg);
    }

    /**
     * 处理创建的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {

    }

    /**
     * 处理审批通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {

        log.info("------------message-----------",message);
        String businessKey = message.getBusinessKey();
        List<Long> businessIdList = Arrays.stream(businessKey.split(",")).map(Long::parseLong).collect(Collectors.toList());
        businessIdList.forEach(businessId -> {
            //1.查询当前业务key下所有发票记录id
            EcsReimDetailDto ecsReimDetailDto = new EcsReimDetailDto();
            ecsReimDetailDto.setId(businessId);
            List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryListNew(ecsReimDetailDto);
            //更新发票状态
            if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
                updateInvoStatus(ecsReimDetailVos.get(0),MedConst.TYPE_2);
            }
            LambdaUpdateWrapper<EcsReimDetailDto> wrapper = Wrappers.lambdaUpdate();
            wrapper.eq(EcsReimDetailDto::getId,ecsReimDetailVos.get(0).getId() );
            //获取报销类型，如果是零星采购和物资采购，则只更新busstas为审核通过(此时未上传付款文件)
            if (StringUtils.equals(ecsReimDetailVos.get(0).getType(), ReimTypeEnum.PURC_FEE.getCode()) ||
                    StringUtils.equals(ecsReimDetailVos.get(0).getType(), ReimTypeEnum.WZCG_FEE.getCode())) {
                //更新为已审核即可
                wrapper.set(EcsReimDetailDto::getBusstas, EcsConst.REIM_BUSSTAS_PASS);
            } else {
                //更新当前付款状态为已付款
                wrapper.set(EcsReimDetailDto::getBusstas, MedConst.TYPE_1);
            }
            ecsReimDetailWriteMapper.update(null,wrapper);

            List<EcsReimItemDetail> itemDetails = ecsReimDetailReadMapper.queryItemDetail(ecsReimDetailDto);
            List<Long> researcherFundingApplyIds = itemDetails.stream().map(EcsReimItemDetail::getResearcherFundingApplyId).collect(Collectors.toList());
            ecsReimDetailWriteMapper.updateHrmResearcherFundingApplyStatus(researcherFundingApplyIds,"2");
        });

    }

    /**
     * 处理审批不通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        String businessKey = message.getBusinessKey();
        List<Long> businessIdList = Arrays.stream(businessKey.split(",")).map(Long::parseLong).collect(Collectors.toList());
        businessIdList.forEach(businessId -> {
            //1.查询当前业务key下所有发票记录id
            EcsReimDetailDto ecsReimDetailDto = new EcsReimDetailDto();
            ecsReimDetailDto.setId(businessId);
            List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryListNew(ecsReimDetailDto);

            if (CollectionUtil.isEmpty(ecsReimDetailVos)) {
                return;
            }
            EcsReimDetailVo vo = ecsReimDetailVos.get(0);
            //更新发票状态
            updateInvoStatus(vo,MedConst.TYPE_1);
            //更新报销的业务状态
            LambdaUpdateWrapper<EcsReimDetailDto> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(EcsReimDetailDto::getBusstas, EcsConst.REIM_BUSSTAS_REJECTED)
                    .eq(EcsReimDetailDto::getId,vo.getId());
            ecsReimDetailWriteMapper.update(null,updateWrapper);

            List<EcsReimItemDetail> itemDetails = ecsReimDetailReadMapper.queryItemDetail(ecsReimDetailDto);
            List<Long> researcherFundingApplyIds = itemDetails.stream().map(EcsReimItemDetail::getResearcherFundingApplyId).collect(Collectors.toList());
            ecsReimDetailWriteMapper.updateHrmResearcherFundingApplyStatus(researcherFundingApplyIds,"0");
        });
    }

    /**
     * 处理审批中的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {

    }

    /**
     * 处理已取消的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {

    }

    /**
     * 报销审核成功/拒绝都需要更新发票状态
     * @param ecsReimDetailVo
     * @param status
     */
    private void updateInvoStatus(EcsReimDetailVo ecsReimDetailVo,String status) {
        if (!Objects.isNull(ecsReimDetailVo)) {
            EcsReimDetailDto detailDto = new EcsReimDetailDto();
            detailDto.setId(ecsReimDetailVo.getId());
            List<EcsReimItemDetail> itemDetails = ecsReimDetailReadMapper.queryItemDetail(detailDto);
            List<EcsReimSubsItemDetail> subsItemDetails = ecsReimDetailReadMapper.querySubsItemDetail(detailDto);
            List<Long> ids = new ArrayList<>();
            //报销自带发票
            if (StringUtils.isNotEmpty(ecsReimDetailVo.getInvoId())){
                ids.addAll(getInvoIds(ecsReimDetailVo.getInvoId()));
            }
            // 项目
            if (CollectionUtil.isNotEmpty(itemDetails)) {
                itemDetails.forEach(i -> {
                    if (StringUtils.isNotEmpty(i.getInvoId())) {
                        ids.addAll(getInvoIds(i.getInvoId()));
                    }
                });
            }

            // 补助项目
            if (CollectionUtil.isNotEmpty(subsItemDetails)) {
                subsItemDetails.forEach(i -> {
                    if (StringUtils.isNotEmpty(i.getInvoId())) {
                        ids.addAll(getInvoIds(i.getInvoId()));
                    }
                });
            }

            if (CollectionUtil.isNotEmpty(ids)) {
                // 更新发票记录表状态，更改为已报销(2为已报销)
                ecsInvoRcdWriteMapper.updateStateByIds(ids, status,StringUtils.equals(status,MedConst.TYPE_1)?"":getReimInvoUsedBy(ecsReimDetailVo));
            }
        }
    }

    private String getReimInvoUsedBy(EcsReimDetailVo reim) {
        if (StringUtils.equals(reim.getType(),MedConst.TYPE_4)) {
            return ShareTypeEnum.getByType(reim.getShareType()).getName();
        }
        return ReimTypeEnum.getByCode(reim.getType()).getMessage();
    }

    private List<Long> getInvoIds(String invoId){
        List<Long> ids = new ArrayList<>();
        String[] split = invoId.split(",");
        for (int i = 0; i < split.length; i++) {
            ids.add(Long.parseLong(split[i]));
        }
        return ids;
    }


}
