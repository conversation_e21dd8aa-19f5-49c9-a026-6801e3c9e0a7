package com.jp.med.ecs.modules.drugMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 药品报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:45
 */
@Mapper
public interface EcsDrugReimDetaiReadMapper extends BaseMapper<EcsDrugReimDetaiDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsDrugReimDetaiVo> queryList(EcsDrugReimDetaiDto dto);

    /**
     * 查询审核数据
     * @param dto
     * @return
     */
    List<EcsDrugReimDetaiVo> queryAuditData(EcsDrugReimDetaiDto dto);

    /**
     * 查询审核归集数据
     * @param dto
     * @return
     */
    List<EcsDrugReimDetaiVo> queryAuditCollData(EcsDrugReimDetaiDto dto);
}
