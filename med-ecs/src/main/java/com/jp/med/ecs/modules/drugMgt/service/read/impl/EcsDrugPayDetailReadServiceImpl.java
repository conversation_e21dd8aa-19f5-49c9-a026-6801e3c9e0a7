package com.jp.med.ecs.modules.drugMgt.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugPayDetailReadMapper;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.vo.ecs.drug.EcsDrugPayDetailVo;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsDrugPayDetailReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsDrugPayDetailReadServiceImpl extends ServiceImpl<EcsDrugPayDetailReadMapper, EcsDrugPayDetailDto> implements EcsDrugPayDetailReadService {

    @Autowired
    private EcsDrugPayDetailReadMapper ecsDrugPayDetailReadMapper;

    @Override
    public List<EcsDrugPayDetailVo> queryList(EcsDrugPayDetailDto dto) {
        return ecsDrugPayDetailReadMapper.queryList(dto);
    }

    @Override
    public List<EcsDrugPayDetailVo> queryPageList(EcsDrugPayDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsDrugPayDetailReadMapper.queryList(dto);
    }

}
