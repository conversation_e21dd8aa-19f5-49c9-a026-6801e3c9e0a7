package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.dto.ecs.EcsReimFixedAsstDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.config.service.read.EcsReimFixedAsstReadService;
import com.jp.med.ecs.modules.config.service.write.EcsReimFixedAsstWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 会计科目固定项配置
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Api(value = "会计科目固定项配置", tags = "会计科目固定项配置")
@RestController
@RequestMapping("ecsReimFixedAsst")
public class EcsReimFixedAsstController {

    @Autowired
    private EcsReimFixedAsstReadService ecsReimFixedAsstReadService;

    @Autowired
    private EcsReimFixedAsstWriteService ecsReimFixedAsstWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询费用报销会计科目固定项")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimFixedAsstDto dto){
        return CommonResult.success(ecsReimFixedAsstReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询费用报销会计科目固定项")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsReimFixedAsstDto dto){
        return CommonResult.paging(ecsReimFixedAsstReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增费用报销会计科目固定项")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsReimFixedAsstDto dto){
        ecsReimFixedAsstWriteService.saveFixedAsstWithDetail(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改费用报销会计科目固定项")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimFixedAsstDto dto){
        ecsReimFixedAsstWriteService.updateFixedAsstWithDetail(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除费用报销会计科目固定项")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimFixedAsstDto dto){
        ecsReimFixedAsstWriteService.removeByIdWithAsstDetail(dto);
        return CommonResult.success();
    }

    /**
     * 查询固定项上级
     * @param dto
     * @return
     */
    @ApiOperation("查询上级固定项")
    @PostMapping("/queryFixedTree")
    public CommonResult<?> queryFixedTree(@RequestBody EcsReimFixedAsstDto dto){
        return CommonResult.success(ecsReimFixedAsstReadService.queryFixedTree(dto));
    }
}
