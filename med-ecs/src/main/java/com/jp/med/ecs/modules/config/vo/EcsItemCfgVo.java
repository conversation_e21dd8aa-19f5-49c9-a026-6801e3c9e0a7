package com.jp.med.ecs.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 财务科目辅助项目项目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Data
public class EcsItemCfgVo implements BaseTree<String, EcsItemCfgVo> {

	/** id */
	private Integer id;

	/** 项目代码 */
	private String itemCode;

	/** 项目名称 */
	private String itemName;

	/** 拼音助记码 */
	private String pinyin;

	/** 开始日期 */
	private String begnDate;

	/** 结束日期 */
	private String endDate;

	/** 负责部门 */
	private String dept;

	/** 负责人 */
	private String resper;

	/** 立项年度 */
	private String estaYear;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改时间 */
	private String modiTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 启用标志 */
	private String activeFlag;

	/** 上级项目编码 */
	private String parentItemCode;

	/** 子集数据 */
	List<EcsItemCfgVo> children;

	/**  */
	private String value;

	/**  */
	private String label;

	@Override
	public String getCode() {
		return this.itemCode;
	}

	@Override
	public void setCode(String code) {
      	this.itemCode = code;
	}

	@Override
	public String getPid() {
		return this.parentItemCode;
	}

	@Override
	public void setPid(String pid) {
		this.parentItemCode = pid;
	}

	@Override
	public void addChild(EcsItemCfgVo node) {
		if (Objects.isNull(this.children)){
			this.children = new ArrayList<>();
		}
		this.children.add(node);
	}
}
