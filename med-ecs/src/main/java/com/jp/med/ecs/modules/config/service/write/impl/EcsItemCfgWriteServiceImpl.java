package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ecs.modules.config.dto.EcsItemCfgDto;
import com.jp.med.ecs.modules.config.mapper.write.EcsItemCfgWriteMapper;
import com.jp.med.ecs.modules.config.service.write.EcsItemCfgWriteService;
import com.jp.med.ecs.modules.config.utils.YySyncUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务科目辅助项目项目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Service
@Transactional(readOnly = false)
public class EcsItemCfgWriteServiceImpl extends ServiceImpl<EcsItemCfgWriteMapper, EcsItemCfgDto> implements EcsItemCfgWriteService {

    @Autowired
    private EcsItemCfgWriteMapper ecsItemCfgWriteMapper;

    @Value("${urls.mid.yy.item-sync}")
    private String ecsItemSyncUrl;

    private static final int BATCH_SIZE = 500;  //批处理大小
    @Override
    public void sync(EcsItemCfgDto dto) {

        int start = 1;
        int end = start + BATCH_SIZE;
//        int end = 2;
        Map<String,Object> params = new HashMap<>();
        params.put("year",dto.getYear());

        /*params.put("start",start);
        params.put("end",end);
        List<EcsEconFunSubCfgDto> efcDtos = doSync(econFunSyncUrl, params, EcsEconFunSubCfgDto.class);
        //处理parentCode
        efcDtos.forEach(item -> {
            if (item.getSubCode().length()> 3) {
                item.setParentCode(item.getSubCode().substring(0,item.getSubCode().length()-2));
            }
        });
        ecsEconFunSubCfgWriteMapper.saveEcsEconSubCfg(efcDtos);*/

        //删除某年度项目
        LambdaQueryWrapper<EcsItemCfgDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EcsItemCfgDto::getYear,dto.getYear());
        ecsItemCfgWriteMapper.delete(wrapper);

        List<EcsItemCfgDto> efcDtos;
        do{
            params.put("start",start);
            params.put("end",end);

            efcDtos = YySyncUtils.doSync(ecsItemSyncUrl, params, EcsItemCfgDto.class);
            if (!efcDtos.isEmpty()) {
                String crter = dto.getSysUser().getHrmUser().getEmpCode();
                //处理parentCode
                efcDtos.forEach(item -> {
                    item.setCrter(crter);
                    if (item.getItemCode().length()> 2) {
                        item.setParentItemCode(item.getItemCode().substring(0,item.getItemCode().length()-3));
                    }
                });

                //插入项目
                ecsItemCfgWriteMapper.saveItemCfg(efcDtos);

                start +=BATCH_SIZE;
                end +=BATCH_SIZE;
            }
        }while(!efcDtos.isEmpty());
    }
}
