package com.jp.med.ecs.modules.config.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDetailDto;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Mapper
public interface EcsReimFixedAsstDetailWriteMapper extends BaseMapper<EcsReimFixedAsstDetailDto> {

    boolean removeByFixedAsstId(Serializable id);

    void addFixedDetails(List<EcsReimFixedAsstDetailDto> fixedAsstDetails);
}
