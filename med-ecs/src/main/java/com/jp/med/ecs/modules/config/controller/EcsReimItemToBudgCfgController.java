package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.common.dto.ecs.EcsReimItemToBudgCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsReimItemToBudgCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsReimItemToBudgCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 报销项目对应预算项目
 * <AUTHOR>
 * @email -
 * @date 2023-12-12 09:43:44
 */
@Api(value = "报销项目对应预算项目", tags = "报销项目对应预算项目")
@RestController
@RequestMapping("ecsReimItemToBudgCfg")
public class EcsReimItemToBudgCfgController {

    @Autowired
    private EcsReimItemToBudgCfgReadService ecsReimItemToBudgCfgReadService;

    @Autowired
    private EcsReimItemToBudgCfgWriteService ecsReimItemToBudgCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询报销项目对应预算项目")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimItemToBudgCfgDto dto){
        return CommonResult.paging(ecsReimItemToBudgCfgReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询报销项目对应预算项目-不分页")
    @PostMapping("/listNoPage")
    public CommonResult<?> listNoPage(@RequestBody EcsReimItemToBudgCfgDto dto){
        return CommonResult.success(ecsReimItemToBudgCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增报销项目对应预算项目")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsReimItemToBudgCfgDto dto){
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        ecsReimItemToBudgCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改报销项目对应预算项目")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimItemToBudgCfgDto dto){
        dto.setModiTime(DateUtil.getCurrentTime(null));
        ecsReimItemToBudgCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除报销项目对应预算项目")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimItemToBudgCfgDto dto){
        ecsReimItemToBudgCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
