package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsItemCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsItemCfgVo;

import java.util.List;

/**
 * 财务科目辅助项目项目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
public interface EcsItemCfgReadService extends IService<EcsItemCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsItemCfgVo> queryList(EcsItemCfgDto dto);
}

