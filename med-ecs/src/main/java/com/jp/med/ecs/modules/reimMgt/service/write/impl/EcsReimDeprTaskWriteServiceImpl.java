package com.jp.med.ecs.modules.reimMgt.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDetailDto;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.ULIDUtil;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDeprTaskDetailWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDeprTaskWriteMapper;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimDeprTaskWriteService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 费用报销-折旧凭证
 * <AUTHOR>
 * @email -
 * @date 2025-02-07 16:08:24
 */
@Service
@Transactional(readOnly = false)
@Slf4j
public class EcsReimDeprTaskWriteServiceImpl extends ServiceImpl<EcsReimDeprTaskWriteMapper, EcsReimDeprTaskDto> implements EcsReimDeprTaskWriteService {


    @Autowired
    private EcsReimDeprTaskWriteMapper ecsReimDeprTaskWriteMapper;

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void saveDeprTask(EcsReimDeprTaskDto dto) {
        log.info("-------------------------start插入折旧任务及明细----------------------");
        List<EcsReimDeprTaskDetailDto> details = dto.getDetails();
        if (Objects.isNull(details) || CollectionUtil.isEmpty(details)){
            log.error(EcsConst.DEPR_TASK_DETAILS_NOT_EXIST);
            throw new AppException(EcsConst.DEPR_TASK_DETAILS_NOT_EXIST);
        }
        //保存折旧任务
        dto.setId(null);
        dto.setAttCode(ULIDUtil.generate());
        dto.setCrter(dto.getSysUser().getUsername());
        dto.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        ecsReimDeprTaskWriteMapper.insert(dto);
        //保存折旧明细
        details.stream().forEach(detail -> {
            detail.setTaskId(dto.getId());
            detail.setCrter(dto.getSysUser().getUsername());
            detail.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        });
        BatchUtil.batch("insertDepraskDetail",details, EcsReimDeprTaskDetailWriteMapper.class);

        log.info("-------------------------end插入折旧任务及明细----------------------");
    }
}
