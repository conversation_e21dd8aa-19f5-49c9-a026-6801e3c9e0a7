package com.jp.med.ecs.modules.config.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.TreeNewUtil;
import com.jp.med.ecs.modules.config.dto.*;
import com.jp.med.ecs.modules.config.mapper.read.EcsActigAsstCfgReadMapper;
import com.jp.med.ecs.modules.config.mapper.read.EcsActigCfgReadMapper;
import com.jp.med.ecs.modules.config.service.read.*;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstCfgVo;
import com.jp.med.ecs.modules.config.vo.EcsActigCfgVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsActigCfgReadServiceImpl extends ServiceImpl<EcsActigCfgReadMapper, EcsActigCfgDto> implements EcsActigCfgReadService {

    @Autowired
    private EcsActigCfgReadMapper ecsActigCfgReadMapper;

    @Autowired
    private EcsEconFunSubCfgReadService ecsEconFunSubCfgReadService;

    @Autowired
    private EcsItemCfgReadService ecsItemCfgReadService;

    @Autowired
    private EcsCorrsInsCfgReadService ecsCorrsInsCfgReadService;

    @Autowired
    private EcsActigAsstCashCfgReadService ecsActigAsstCashCfgReadService;

    @Autowired
    private EcsActigAsstDeptCfgReadService ecsActigAsstDeptCfgReadService;

    @Autowired
    private EcsActigAsstCfgReadMapper ecsActigAsstCfgReadMapper;

    @Override
    public List<EcsActigCfgVo> queryList(EcsActigCfgDto dto) {
        if (StringUtils.isNotEmpty(dto.getStatus())) {
            // 财务会计科目
            if (MedConst.TYPE_3.equals(dto.getStatus())) {
                dto.setStatus(MedConst.TYPE_1);
            } else {
                // 预算会计科目
                dto.setStatus(MedConst.TYPE_2);
            }
        }
        List<EcsActigCfgVo> ecsActigCfgVos = ecsActigCfgReadMapper.queryList(dto);
        ecsActigCfgVos.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getEmpType())) {
                e.setEmpTypeArr(e.getEmpType().split(","));
            }
            if (StringUtils.isNotEmpty(e.getAsstInfo())) {
                e.setAsstInfoArr(e.getAsstInfo().split(","));
            }
        });
        TreeNewUtil<String,EcsActigCfgVo> treeNewUtil = new TreeNewUtil<>();
        return treeNewUtil.buildTree(ecsActigCfgVos);
    }

    @Override
    public List<EcsActigCfgVo> queryListLayFlat(EcsActigCfgDto dto) {
        if (StringUtils.isNotEmpty(dto.getStatus())) {
            // 财务会计科目
            if (MedConst.TYPE_3.equals(dto.getStatus())) {
                dto.setStatus(MedConst.TYPE_1);
            } else {
                // 预算会计科目
                dto.setStatus(MedConst.TYPE_2);
            }
        }
        List<EcsActigCfgVo> ecsActigCfgVos = ecsActigCfgReadMapper.queryList(dto);
        ecsActigCfgVos.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getEmpType())) {
                e.setEmpTypeArr(e.getEmpType().split(","));
            }
            if (StringUtils.isNotEmpty(e.getAsstInfo())) {
                e.setAsstInfoArr(e.getAsstInfo().split(","));
            }
        });
        return ecsActigCfgVos;
    }

    @Override
    public List<EcsActigAsstCfgVo> queryAuxItem(EcsActigCfgDto dto) {
        List<EcsActigAsstCfgVo> res = new ArrayList<>();
        // 辅项信息配置
        List<EcsActigAsstCfgVo> ecsActigAsstCfgVos = ecsActigAsstCfgReadMapper.queryList(new EcsActigAsstCfgDto());
        if (dto.getAsstInfoArr() != null && CollectionUtil.isNotEmpty(ecsActigAsstCfgVos)) {
            for (int i = 0; i < dto.getAsstInfoArr().length; i++) {
                String auxCode = dto.getAsstInfoArr()[i];
                if (StringUtils.isNotEmpty(auxCode) && !"106".equals(auxCode) && !"102".equals(auxCode)) {
                    EcsActigAsstCfgVo cfgVo = ecsActigAsstCfgVos.stream().filter(ecsActigAsstCfgVo -> ecsActigAsstCfgVo.getAsstCode().equals(auxCode)).findFirst().get();
                    switch (auxCode) {
                        case "108": // 部门经济科目
                        case "107": // 功能科目
                        case "104": //资金性质
                            EcsEconFunSubCfgDto ecsEconFunSubCfgDto = new EcsEconFunSubCfgDto();
                            if ("108".equals(auxCode)) {
                                ecsEconFunSubCfgDto.setStatus(MedConst.TYPE_2);
                            } else if ("107".equals(auxCode)){
                                ecsEconFunSubCfgDto.setStatus(MedConst.TYPE_1);
                            } else {
                                ecsEconFunSubCfgDto.setStatus(MedConst.TYPE_3);
                            }
                            cfgVo.setAuxData(ecsEconFunSubCfgReadService.queryList(ecsEconFunSubCfgDto));
                            break;
                        case "105": // 项目
                            cfgVo.setAuxData(ecsItemCfgReadService.queryList(new EcsItemCfgDto()));
                            break;
                        case "103": // 单位往来
                            cfgVo.setAuxData(ecsCorrsInsCfgReadService.queryList(new EcsCorrsInsCfgDto()));
                            break;
                        case "101": // 现金流量
                            cfgVo.setAuxData(ecsActigAsstCashCfgReadService.queryList(new EcsActigAsstCashCfgDto()));
                            break;
//                        case "106": // 部门
//                            cfgVo.setAuxData(ecsActigAsstDeptCfgReadService.queryList(new EcsActigAsstDeptCfgDto()));
//                            break;
                    }
                    res.add(cfgVo);
                }
            }
        }
        return res;
    }

}
