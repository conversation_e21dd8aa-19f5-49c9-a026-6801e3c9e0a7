package com.jp.med.ecs.modules.reimMgt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
@Data
@TableName("ecs_invo_rcd")
public class EcsInvoRcdEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 发票代码 */
	@TableField("invo_code")
	private String invoCode;

	/** 发票号码 */
	@TableField("invo_num")
	private String invoNum;

	/** 开票日期 */
	@TableField("invo_date")
	private String invoDate;

	/** 校验码 */
	@TableField("chk_code")
	private String chkCode;

	/** 发票附件 */
	@TableField("att")
	private String att;

	/** 发票附件名称 */
	@TableField("att_name")
	private String attName;

	/** 校验状态(1:有效,0:无效) */
	@TableField("chk_state")
	private String chkState;

	/** 校验时间 */
	@TableField("chk_time")
	private String chkTime;

	/** OCR识别错误信息 */
	@TableField("idtf_err_msg")
	private String idtfErrMsg;

	/** 创建日期 */
	@TableField("create_time")
	private String createTime;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}
