package com.jp.med.ecs.modules.reimMgt.utils;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.ULIDUtil;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;
import com.jp.med.ecs.modules.reimMgt.enums.InvoiceOcrEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * ocr识别结果转为EcsInvoChkRcdEntity
 */
public class OcrDataToInvoRcdUtil {

    public static EcsInvoRcdDto dataToInvoRcd(String hospitalId,String invoFrom,String crter,String typeName, String dataString) throws Exception {
        InvoiceOcrEnum byTypeName = InvoiceOcrEnum.getByTypeName(typeName);
        if (ObjectUtil.isNull(byTypeName)) {
            throw new AppException("不存在当前发票类型!");
        }
        EcsInvoRcdDto rcdDto = getBaseInvoRcdDto(hospitalId, invoFrom, crter);
        switch (byTypeName) {
            case INVOICE:
                return invoiceToDto(rcdDto,dataString);
            case CARINVOICE:
                return carinvoiceToDto(rcdDto,dataString);
            case QUOTAINVOICE:
                return quotainvoiceToDto(rcdDto,dataString);
            case AIRITINERARY:
                return airitineraryToDto(rcdDto,dataString);
            case TRAINTICKET:
                return trainticketToDto(rcdDto,dataString);
            case TOLLINVOICE:
                return tollinvoiceToDto(rcdDto,dataString);
            case ROLLTICKET:
                return rollticketToDto(rcdDto,dataString);
            case BANKACCEPTANCE:
                return bankacceptanceToDto(rcdDto,dataString);
            case BUSSHIPTICKET:
                return busshipticketToDto(rcdDto,dataString);
            case NONTAXINVOICE:
                return nontaxinvoiceToDto(rcdDto,dataString);
            case COMMONPRINTEDINVOICE:
                return commonprintedinvoiceToDto(rcdDto,dataString);
            case TAXIINVOICE:
                return taxiInvoiceToDto(rcdDto,dataString);
            default: return null;
        }
    }

    /**
     * 增值税发票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto invoiceToDto(EcsInvoRcdDto dto,String dataString) {
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("INVOICE");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("invoiceDate"));
        dto.setChkCode(jsonObject.getString("checkCode"));
        dto.setPurchaserName(jsonObject.getString("purchaserName"));
        dto.setPurchaserTaxpayerNumber(jsonObject.getString("purchaserTaxNumber"));
        dto.setInvoiceMoney(jsonObject.getString("invoiceAmountPreTax"));
        dto.setInvoiceAmountPreTax(new BigDecimal(jsonObject.getString("invoiceAmountPreTax") == null? "0": jsonObject.getString("invoiceAmountPreTax")));
        dto.setAllValoremTax(jsonObject.getString("totalAmount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 机动车销售统一发票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto carinvoiceToDto(EcsInvoRcdDto dto,String dataString) {
        JSONObject jsonObject = JSON.parseObject("dataString");
        dto.setInvoType("CARINVOICE");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("invoiceDate"));
        dto.setChkCode(jsonObject.getString("checkCode"));
        dto.setPurchaserName(jsonObject.getString("purchaserName"));
        dto.setPurchaserTaxpayerNumber(jsonObject.getString("purchaserTaxNumber"));
        dto.setInvoiceMoney(jsonObject.getString("preTaxAmount"));
        dto.setInvoiceAmountPreTax(new BigDecimal(jsonObject.getString("preTaxAmount") == null? "0": jsonObject.getString("preTaxAmount")));
        dto.setAllValoremTax(jsonObject.getString("invoiceAmount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 定额发票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto quotainvoiceToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("QUOTAINVOICE");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("invoiceDate"));
        dto.setChkCode(jsonObject.getString("checkCode"));
        dto.setAllValoremTax(jsonObject.getString("Amount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 机票行程单
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto airitineraryToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("AIRITINERARY");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("ticketNumber"));  //电子客票号作为number，唯一查询标识
        if (StringUtils.isEmpty(dto.getInvoNum())) {            //兼容意外保险凭证票据类型识别为本类型时，ticketNumber为空的情况
            dto.setInvoNum(ULIDUtil.generate());
        }
        dto.setInvoDate(jsonObject.getString("issueDate"));
        dto.setChkCode(jsonObject.getString("checkCode"));
        dto.setPurchaserName(jsonObject.getString("purchaserBankAccountInfo"));
        dto.setPurchaserTaxpayerNumber(jsonObject.getString("purchaserTaxNumber"));
        dto.setInvoiceMoney(jsonObject.getString("invoiceAmountPreTax"));
        dto.setAllValoremTax(jsonObject.getString("totalAmount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 火车票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto trainticketToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("TRAINTICKET");
        if (StringUtils.isNotEmpty(jsonObject.getString("ticketNumber"))) {        //火车票的电子客票有此号码，优先使用
            dto.setInvoNum(jsonObject.getString("ticketNumber"));
        } else {
            dto.setInvoNum(jsonObject.getString("ticketCode"));
        }
        if (StringUtils.isNotEmpty(jsonObject.getString("invoiceDate"))) {          //火车票的电子客票有发票日期，优先使用
            dto.setInvoDate(jsonObject.getString("invoiceDate"));
        } else {
            dto.setInvoDate(jsonObject.getString("departureTime"));
        }
        dto.setAllValoremTax(jsonObject.getString("fare"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 过路过桥费发票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto tollinvoiceToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("TOLLINVOICE");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("date"));
        dto.setAllValoremTax(jsonObject.getString("totalAmount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 增值税发票卷票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto rollticketToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("ROLLTICKET");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("invoiceDate"));
        dto.setChkCode(jsonObject.getString("checkCode"));
        dto.setPurchaserName(jsonObject.getString("purchaserName"));
        dto.setPurchaserTaxpayerNumber(jsonObject.getString("purchaserTaxCode"));
        dto.setAllValoremTax(jsonObject.getString("totalAmount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 银行承兑汇票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto bankacceptanceToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("BANKACCEPTANCE");
        dto.setInvoNum(jsonObject.getString("draftNumber"));            //票据号码作为唯一查询标识
        dto.setInvoDate(jsonObject.getString("issueDate"));
        dto.setPurchaserName(jsonObject.getString("acceptorAccountBank"));
        dto.setPurchaserTaxpayerNumber(jsonObject.getString("acceptorBankNumber"));
        if (jsonObject.getString("totalAmount").matches("^[-+]?[0-9]*\\.?[0-9]+$")) {         //判断是否为数字字符串，兼容错误识别票据类型的情况
            dto.setAllValoremTax(jsonObject.getString("totalAmount"));
        } else {
            dto.setAllValoremTax("0.00");
        }

        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 客运车船票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto busshipticketToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("BUSSHIPTICKET");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("date"));
        dto.setAllValoremTax(jsonObject.getString("totalAmount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 非税收入票据
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto nontaxinvoiceToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("NONTAXINVOICE");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("invoiceDate"));
        dto.setChkCode(jsonObject.getString("validationCode"));
        dto.setPurchaserName(jsonObject.getString("payerName"));
        dto.setPurchaserTaxpayerNumber(jsonObject.getString("payerCreditCode"));
        dto.setAllValoremTax(jsonObject.getString("totalAmount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    /**
     * 通用机打发票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto commonprintedinvoiceToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("COMMONPRINTEDINVOICE");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("invoiceDate"));
        dto.setPurchaserName(jsonObject.getString("purchaserName"));
        dto.setPurchaserTaxpayerNumber(jsonObject.getString("purchaserTaxNumber"));
        dto.setInvoiceMoney(jsonObject.getString("invoiceAmountPreTax"));
        dto.setAllValoremTax(jsonObject.getString("totalAmount"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    //TaxiInvoice
    /**
     * 出租车票
     * @param dataString
     * @return
     */
    private static EcsInvoRcdDto taxiInvoiceToDto (EcsInvoRcdDto dto,String dataString){
        JSONObject jsonObject = JSON.parseObject(dataString);
        dto.setInvoType("TAXIINVOICE");
        dto.setInvoCode(jsonObject.getString("invoiceCode"));
        dto.setInvoNum(jsonObject.getString("invoiceNumber"));
        dto.setInvoDate(jsonObject.getString("date"));
        dto.setAllValoremTax(jsonObject.getString("fare"));
        //将OCR识别信息插入字段
        dto.setIdtfErrMsg(dataString);
        return dto;
    }

    private static EcsInvoRcdDto getBaseInvoRcdDto(String hospitalId,String invoFrom,String crter) {
        EcsInvoRcdDto ecsInvoRcdDto = new EcsInvoRcdDto();
        //设置基本发票记录数据
//        ecsInvoRcdDto.setAttName(file.getOriginalFilename());
        ecsInvoRcdDto.setCreateTime(DateUtil.getCurrentTime(null));
        ecsInvoRcdDto.setHospitalId(hospitalId);
        //默认核验成功
        ecsInvoRcdDto.setChkState(MedConst.TYPE_1);
        ecsInvoRcdDto.setState(EcsConst.CAN_REIM);
//        ecsInvoRcdDto.setFileIdentifier(fileIdentifier);
        ecsInvoRcdDto.setRcdIdentifier(ULIDUtil.generate());
        //设置发票来源
        ecsInvoRcdDto.setInvoFrom(invoFrom);
        //设置创建人
        ecsInvoRcdDto.setCreateUser(crter);
        ecsInvoRcdDto.setIsSub(MedConst.TYPE_1);
//        String url = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/invo/", file);
//        ecsInvoRcdDto.setAtt(url);
        return ecsInvoRcdDto;
    }


}
