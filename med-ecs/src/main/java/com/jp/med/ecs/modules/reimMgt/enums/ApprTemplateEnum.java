package com.jp.med.ecs.modules.reimMgt.enums;

import io.seata.common.util.StringUtils;

public enum ApprTemplateEnum {

    TRAVEL_TEMPLATE("1","template/出差申请文档模板.docx"),

    TRAINING_TEMPLATE("2","template/培训申请文档模板.docx");


    private final String type;

    private final String templatePath;

    ApprTemplateEnum(String type,String templatePath){
        this.type = type;
        this.templatePath = templatePath;
    }

    public String getType() {
        return type;
    }

    public String getTemplatePath() {
        return templatePath;
    }

    public static ApprTemplateEnum getByType(String type) {
        for (ApprTemplateEnum status : ApprTemplateEnum.values()) {
            if (StringUtils.equals(status.getType(),type)) {
                return status;
            }
        }
        return null;
    }
}
