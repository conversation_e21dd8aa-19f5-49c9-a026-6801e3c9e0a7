package com.jp.med.ecs.modules.reimMgt.service.write.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.dto.bpm.BpmProcessInstanceCreateReqDTO;
import com.jp.med.common.dto.ecs.EcsReimContractTask;
import com.jp.med.common.dto.ecs.EcsReimFileRecordDto;
import com.jp.med.common.dto.ecs.EcsReimPurcTask;
import com.jp.med.common.dto.ecs.EcsReimSalaryTask;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.dto.emp.EmpEmployeeInfoDto;
import com.jp.med.common.dto.emp.HrmOrgAgencyMapDto;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.enums.ecs.InvoStatusEnum;
import com.jp.med.common.enums.ecs.ReimTypeEnum;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.feign.EmpEmployeeFeignService;
import com.jp.med.common.feign.HrmOrgMapFeignService;
import com.jp.med.common.feign.rms.RmsExpenseReimbursementFeinService;
import com.jp.med.common.util.*;
import com.jp.med.common.vo.EcsReimPurcTaskDetailVo;
import com.jp.med.common.vo.EmpEmployeeInfoVo;
import com.jp.med.common.vo.HrmOrgAgencyMapVo;
import com.jp.med.ecs.modules.config.entity.EcsReimShareEntity;
import com.jp.med.ecs.modules.config.entity.EcsReimShareGasExcelEntity;
import com.jp.med.ecs.modules.config.service.read.EcsActigCfgReadService;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsDrugPayDetailWriteMapper;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.dto.SaveMultiTripDto;
import com.jp.med.ecs.modules.reimMgt.entity.*;
import com.jp.med.ecs.modules.reimMgt.enums.BizTypeToProcessEnum;
import com.jp.med.ecs.modules.reimMgt.enums.InvoiceOcrEnum;
import com.jp.med.ecs.modules.reimMgt.enums.ShareExcelEnum;
import com.jp.med.ecs.modules.reimMgt.feign.cms.CmsFeignService;
import com.jp.med.ecs.modules.reimMgt.feign.hrm.HrmFeignService;
import com.jp.med.ecs.modules.reimMgt.feign.mmis.MmisFeignService;
import com.jp.med.ecs.modules.reimMgt.feign.purms.PurmsFeignService;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsInvoRcdReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDetailReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimFileRecordReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimPurcTaskReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.*;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimDetailWriteService;
import com.jp.med.ecs.modules.reimMgt.utils.OcrDataToInvoRcdUtil;
import com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * 报销明细
 *
 * <AUTHOR>
 * @email -
 * @date 2023-12-04 22:01:14
 */
@Service
@Transactional(readOnly = false)
public class EcsReimDetailWriteServiceImpl extends ServiceImpl<EcsReimDetailWriteMapper, EcsReimDetailDto>
        implements EcsReimDetailWriteService {

    @Autowired
    private EcsReimDetailWriteMapper ecsReimDetailWriteMapper;

    @Autowired
    private EcsReimDetailReadMapper ecsReimDetailReadMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Autowired
    private EcsReimTravelApprWriteMapper ecsReimTravelApprWriteMapper;

    @Autowired
    private EcsInvoRcdWriteMapper ecsInvoRcdWriteMapper;

    @Autowired
    private EcsInvoRcdReadMapper ecsInvoRcdReadMapper;

    @Autowired
    private HrmOrgMapFeignService hrmOrgMapFeignService;

    @Autowired
    private EmpEmployeeFeignService empEmployeeFeignService;

    @Autowired
    private EcsReimFileRecordWriteMapper ecsReimFileRecordWriteMapper;

    @Autowired
    private EcsReimFileRecordReadMapper ecsReimFileRecordReadMapper;

    @Autowired
    private EcsReimSalaryTaskWriteMapper ecsReimSalaryTaskWriteMapper;

    @Autowired
    private EcsReimContractTaskWriteMapper ecsReimContractTaskWriteMapper;

    @Autowired
    private EcsReimPurcTaskWriteMapper ecsReimPurcTaskWriteMapper;

    @Autowired
    private EcsReimPurcTaskReadMapper ecsReimPurcTaskReadMapper;

    @Autowired
    private EcsResearchFundingTaskWriteMapper ecsResearchFundingTaskWriteMapper;

    @Autowired
    private EcsDrugPayDetailWriteMapper ecsDrugPayDetailWriteMapper;

    @Autowired
    private HrmFeignService hrmFeignService;

    @Autowired
    private PurmsFeignService purmsFeignService;

    @Autowired
    private MmisFeignService mmisFeignService;

    @Autowired
    private CmsFeignService cmsFeignService;

    @Autowired
    private RmsExpenseReimbursementFeinService rmsExpenseReimbursementFeinService;

    @Autowired
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;
    // OCR识别地址
    @Value("${urls.mid.ocr}")
    private String ocrItfUrl;

    @Value("${urls.mid.ocrNew}")
    private String ocrItfUrlNew;

    // 发票核验地址
    @Value("${urls.mid.invoice-verify}")
    private String invoiceVerifyItfUrl;

    private static final int MAX_THREAD_POOL_SIZE = 10;
    private static final ExecutorService executorService = Executors.newFixedThreadPool(MAX_THREAD_POOL_SIZE);

    @Override
    public void saveReimDetail(EcsReimDetailDto dto) {
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode()
                : dto.getSysUser().getUsername();
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setAppyerTime(DateUtil.getCurrentTime(null));
        if (StringUtils.equals(dto.getTempSave(), MedConst.TYPE_1)) {
            // 设置业务状态为未提交
            dto.setBusstas(EcsConst.REIM_BUSSTAS_UNSUBMIT);
        } else {
            dto.setBusstas(EcsConst.REIM_BUSSTAS_AUDITING);
        }
        if (StringUtils.isEmpty(dto.getAppyerDept())) {
            dto.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgId());
        }
        if (StringUtils.isNotEmpty(dto.getEvectionTime())) {
            String[] split = dto.getEvectionTime().split(",");
            if (split.length == 2) {
                dto.setEvectionBegnTime(split[0]);
                dto.setEvectionEndTime(split[1]);
            }
        }
        // 页面图片
        if (dto.getPageImageFile() != null) {
            String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/page/", dto.getPageImageFile());
            dto.setPageImage(filePath);
        }
        // 附件
        if (Arrays.asList(MedConst.TYPE_1, MedConst.TYPE_2, MedConst.TYPE_3, MedConst.TYPE_6,MedConst.TYPE_8).contains(dto.getType()) &&
                CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            // 暂时只针对 差旅、培训保存附件
            List<List<String>> ossPaths = OSSUtil.getOSSPaths(dto.getAttFiles(), OSSConst.BUCKET_ECS, "reim/item/");
            dto.setAtt(String.join(",", ossPaths.get(0)));
            dto.setAttName(String.join(",", ossPaths.get(1)));
        }
        String batchNum = AuditConst.ECS_FYBX + ULIDUtil.generate();
        dto.setAuditBchno(batchNum);
        String attCode = "ECS-" + ULIDUtil.generate();
        dto.setAttCode(attCode);
        if (!Objects.isNull(dto.getId())) {
            // 删除报销
            ecsReimDetailWriteMapper.deleteById(dto.getId());
            // 删除项目
            ecsReimDetailWriteMapper.deleteReimItemWithReimId(dto.getId().intValue());
            // 删除补助项目
            ecsReimDetailWriteMapper.deleteReimSubItemWithReimId(dto.getId().intValue());
            // 删除随行人员
            ecsReimDetailWriteMapper.deletePsnWithReimDetailId(dto.getId().intValue(), MedConst.TYPE_2);
            // TODO 分摊数据还需要删除分摊表和和往来单位表
        }
        String purcReimType = getPurcReimType(dto);
        dto.setType(purcReimType);
        ecsReimDetailWriteMapper.insert(dto);

        // 所有的发票id
        List<Long> invoIds = new ArrayList<>();
        // 更改申请状态
        if (StringUtils.isNotEmpty(dto.getType()) &&
                Arrays.asList(MedConst.TYPE_1, MedConst.TYPE_2).contains(dto.getType())) {
            // 更新差旅和培训申请状态，改成已报销，类型为1，2，
            if (dto.getTravelApprId() == null) {
                throw new AppException("未查询到差旅申请记录");
            }
            EcsReimTravelApprDto ecsReimTravelApprDto = new EcsReimTravelApprDto();
            ecsReimTravelApprDto.setId(dto.getTravelApprId());
            ecsReimTravelApprDto.setReimFlag(MedConst.TYPE_1);
            ecsReimTravelApprWriteMapper.updateById(ecsReimTravelApprDto);
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_4)) {
            // 保存分摊报销发票
            /*
             * if (CollectionUtil.isEmpty(dto.getShareInvos())) {
             * throw new AppException("分摊报销发票未上传");
             * }
             * 
             * List<List<String>> ossPaths =
             * OSSUtil.getOSSPaths(dto.getShareInvos(),OSSConst.BUCKET_ECS,"reim/item/");
             * LambdaUpdateWrapper<EcsReimDetailDto> shareWrapper = Wrappers.lambdaUpdate();
             * shareWrapper.set(EcsReimDetailDto::getAtt,String.join(",",ossPaths.get(0)))
             * .set(EcsReimDetailDto::getAttName,String.join(",",ossPaths.get(1)))
             * .set(EcsReimDetailDto::getInvoId,getInvoIdByFileIdentifier(dto.
             * getFileIdentifierMap(), dto.getShareInvos(), invoIds))
             * .eq(EcsReimDetailDto::getId,dto.getId());
             * ecsReimDetailWriteMapper.update(null,shareWrapper);
             */
            // 添加发票
            if (StringUtils.isNotEmpty(dto.getInvoId())) {
                invoIds.addAll(
                        Arrays.stream(dto.getInvoId().split(",")).map(Long::valueOf).collect(Collectors.toList()));
            }
            // 解析往来单位文件
            doShareFiles(dto);
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_5)) {
            // 更改工资申请状态
            if (dto.getTravelApprId() == null) {
                throw new AppException("未查询到工资待报销记录");
            }
            EcsReimSalaryTask salaryTask = new EcsReimSalaryTask();
            salaryTask.setId(dto.getTravelApprId().intValue());
            salaryTask.setReimFlag(MedConst.TYPE_1);
            salaryTask.setReimId(dto.getId().intValue());
            ecsReimSalaryTaskWriteMapper.updateById(salaryTask);

            // 新增附件信息
            EcsReimFileRecordDto fileRecordDto = new EcsReimFileRecordDto();
            fileRecordDto.setAtt(dto.getAtt());
            fileRecordDto.setAttCode(dto.getAttCode());
            fileRecordDto.setAttName(dto.getAttName());
            fileRecordDto.setType(MedConst.TYPE_1);

            ecsReimFileRecordWriteMapper.insert(fileRecordDto);

            // 更新报销id到工资任务表
            FeignExecuteUtil.execute(hrmFeignService.updateSalaryTaskReimId(dto.getSalaryId(), dto.getId().intValue()));
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_6)) {
            // 更改合同申请状态
            if (dto.getTravelApprId() == null) {
                throw new AppException("未查询到合同待报销记录");
            }
            EcsReimContractTask contractTask = new EcsReimContractTask();
            contractTask.setId(dto.getTravelApprId().intValue());
            contractTask.setReimFlag(MedConst.TYPE_1);
            contractTask.setReimId(dto.getId().intValue());
            ecsReimContractTaskWriteMapper.updateById(contractTask);

            // 更新报销id到合同任务表
            FeignExecuteUtil
                    .execute(cmsFeignService.updatePaymentTermsReimId(dto.getPaymentId(), dto.getId().intValue(),
                            null, null, null, null));

        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_8)) {
            savePurcReimDetail(dto);
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_10)) {
            savePurcReimDetail(dto);
        }

        // 项目
        if (CollectionUtil.isNotEmpty(dto.getItemDetails())) {
            dto.getItemDetails().forEach(item -> {
                item.setReimDetailId(dto.getId());
                if (StringUtils.isNotEmpty(item.getInvoId())) {
                    invoIds.addAll(Arrays.stream(item.getInvoId()
                            .split(","))
                            .map(Long::valueOf)
                            .collect(Collectors.toList()));
                }
                /*
                 * 前端直接对att attName invoId赋值
                 * if (CollectionUtil.isNotEmpty(item.getAttFiles())) {
                 * List<List<String>> ossPaths =
                 * OSSUtil.getOSSPaths(item.getAttFiles(),OSSConst.BUCKET_ECS,"reim/item/");
                 * item.setAtt(String.join(",", ossPaths.get(0)));
                 * item.setAttName(String.join(",", ossPaths.get(1)));
                 * item.setInvoId(getInvoIdByFileIdentifier(dto.getFileIdentifierMap(),
                 * item.getAttFiles(), invoIds));
                 * }
                 */
            });
            BatchUtil.batch("insertItemDetail", dto.getItemDetails(), EcsReimDetailWriteMapper.class);
        }

        // 补助项目
        if (CollectionUtil.isNotEmpty(dto.getSubsItemDetails())) {
            dto.getSubsItemDetails().forEach(item -> {
                item.setReimDetailId(dto.getId());
                if (StringUtils.isNotEmpty(item.getInvoId())) {
                    invoIds.addAll(Arrays.stream(item.getInvoId()
                            .split(","))
                            .map(Long::valueOf)
                            .collect(Collectors.toList()));
                }
                /*
                 * 前端直接对att attName invoId赋值
                 * if (CollectionUtil.isNotEmpty(item.getAttFiles())) {
                 * List<List<String>> ossPaths =
                 * OSSUtil.getOSSPaths(item.getAttFiles(),OSSConst.BUCKET_ECS,"reim/item/");
                 * item.setAtt(String.join(",", ossPaths.get(0)));
                 * item.setAttName(String.join(",", ossPaths.get(1)));
                 * item.setInvoId(getInvoIdByFileIdentifier(dto.getFileIdentifierMap(),
                 * item.getAttFiles(), invoIds));
                 * }
                 */
            });
            BatchUtil.batch("insertSubsItemDetail", dto.getSubsItemDetails(), EcsReimDetailWriteMapper.class);
        }
        // 出差人
        if (CollectionUtil.isNotEmpty(dto.getPsnDetails())) {
            dto.getPsnDetails().forEach(item -> {
                item.setType(MedConst.TYPE_2);
                item.setReimDetailId(dto.getId());
            });
            BatchUtil.batch("insertPsnDetail", dto.getPsnDetails(), EcsReimDetailWriteMapper.class);
        }

        // 更新发票记录表状态 临时保存不需要修改发票状态
        if (CollectionUtil.isNotEmpty(invoIds) && !StringUtils.equals(dto.getTempSave(), MedConst.TYPE_1)) {
            // 4 为审核中
            ecsInvoRcdWriteMapper.updateStateByIds(invoIds, MedConst.TYPE_4,
                    ReimTypeEnum.getByCode(dto.getType()).getMessage());
        }
        if (!StringUtils.equals(dto.getTempSave(), MedConst.TYPE_1)) {
            if (Arrays.asList(MedConst.TYPE_1, MedConst.TYPE_2).contains(dto.getType())) {
                push(dto, hrmUser, batchNum, appyer, "报销申请");
            } else {
                // 费用报销
                push2(dto, hrmUser, batchNum, appyer, "费用报销申请");
            }
        }
    }

    /**
     * 查询采购报销类型
     * 根据type判断返回对应的报销类型
     * type为8时:
     * - reimTaskType为1时返回8(零星采购)
     * - reimTaskType为3时返回10(物资报销)
     * 其他情况返回原type
     */
    public String getPurcReimType(EcsReimDetailDto dto) {
        if (StringUtils.equals(dto.getType(), MedConst.TYPE_8)) {
            // 获取第一个任务ID,用于获取报销任务类型
            if (StringUtils.isEmpty(dto.getPurcDetailIds())) {
                dto.setType(MedConst.TYPE_8);
                return MedConst.TYPE_8; // 低于100的采购不需要通过任务申请,返回8
            }

            List<Integer> detailIds = Arrays.stream(dto.getPurcDetailIds().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            // 查询采购详情
            List<EcsReimPurcTaskDetailVo> ecsReimPurcTaskDetailVos = ecsReimPurcTaskReadMapper
                    .selectListByDetailIds(detailIds);

            // 按taskId分组
            Map<Integer, List<EcsReimPurcTaskDetailVo>> groupByItemNo = ecsReimPurcTaskDetailVos.stream()
                    .collect(Collectors.groupingBy(EcsReimPurcTaskDetailVo::getTaskId));

            // 获取第一个任务ID
            Integer firstTaskId = groupByItemNo.keySet().iterator().next();
            EcsReimPurcTask firstTask = ecsReimPurcTaskReadMapper.selectById(firstTaskId);

            if (firstTask == null) {
                throw new AppException("未找到对应的采购任务");
            }

            String reimTaskType = firstTask.getReimTaskType();
            // 根据reimTaskType设置对应的type并返回
            switch (reimTaskType) {
                case MedConst.TYPE_1:
                    dto.setType(MedConst.TYPE_8); // 零星采购设为8
                    return MedConst.TYPE_8; // 返回8
                case MedConst.TYPE_3:
                    dto.setType(MedConst.TYPE_10); // 物资报销设为10
                    return MedConst.TYPE_10; // 返回10
                default:
                    return dto.getType(); // 其他情况返回原type
            }
        } else {
            return dto.getType(); // 其他类型直接返回原type
        }
    }

    /**
     * 保存采购报销
     */
    public void savePurcReimDetail(EcsReimDetailDto dto) {
        // 更改零星采购申请状态
        // 低于100的采购不需要通过任务申请
        if (StringUtils.isEmpty(dto.getPurcDetailIds())) {
            return;
        }
        /*
         * if (dto.getPurcDetailIds() == null) {
         * throw new AppException("未查询到零星采购待报销记录");
         * }
         */

        List<Integer> detailIds = Arrays.stream(dto.getPurcDetailIds().split(",")).map(Integer::parseInt)
                .collect(Collectors.toList());
        // 1.更新零星采购任务状态
        ecsReimPurcTaskWriteMapper.updatePurcTaskDetails(detailIds, dto.getId().intValue(), MedConst.TYPE_1);
        // 2.查询采购详情
        List<EcsReimPurcTaskDetailVo> ecsReimPurcTaskDetailVos = ecsReimPurcTaskReadMapper
                .selectListByDetailIds(detailIds);

        // 按taskId分组
        Map<Integer, List<EcsReimPurcTaskDetailVo>> groupByItemNo = ecsReimPurcTaskDetailVos.stream()
                .collect(Collectors.groupingBy(EcsReimPurcTaskDetailVo::getTaskId));

        // groupByItemNo中拿到报销类型reimTaskType ：1零星 3物资
        // 如果reimTaskType为1，就走零星报销路线
        // 如果reimTaskType为3，这里多个taskId，对应多个报销任务，但reimTaskType只有一个，物资报销任务生成报销时不会混用,就拿第一个就行
        // TODO 前端需要做限制，未来其他采购类型之间不能混在一起报销，只能选择一种类型
        // 获取第一个任务ID,用于获取报销任务类型
        Integer firstTaskId = groupByItemNo.keySet().iterator().next();
        EcsReimPurcTask firstTask = ecsReimPurcTaskReadMapper.selectById(firstTaskId);
        if (firstTask == null) {
            throw new AppException("未找到对应的采购任务");
        }
        String purcReimType = firstTask.getReimTaskType();

        // 遍历每个taskId组,检查是否所有明细都已报销
        groupByItemNo.forEach((taskId, details) -> {
            boolean allReimbursed = details.stream()
                    .allMatch(detail -> MedConst.TYPE_1.equals(detail.getReimFlag()));

            if (allReimbursed) {
                // 更新该taskId的reimFlag标志为已全部报销 1
                EcsReimPurcTask updateTask = new EcsReimPurcTask();
                updateTask.setId(taskId);
                updateTask.setReimFlag(MedConst.TYPE_1);
                ecsReimPurcTaskWriteMapper.updateById(updateTask);

                // 3.更新报销id到零星采购任务表
                // 按reimId分组
                Map<Integer, List<Integer>> reimIdItemNoMap = details.stream()
                        .collect(Collectors.groupingBy(
                                EcsReimPurcTaskDetailVo::getReimId,
                                Collectors.mapping(EcsReimPurcTaskDetailVo::getPurcDetailId, Collectors.toList())));

                // 对每组进行一次feign调用
                reimIdItemNoMap.forEach((reimId, purcDetailIds) -> {
                    switch (purcReimType) {
                        case "1":
                            // 零星采购报销
                            EcsPurmsReimIdEntity entity = new EcsPurmsReimIdEntity();
                            entity.setReimId(reimId);
                            entity.setPurcDetailIds(purcDetailIds);
                            FeignExecuteUtil.execute(purmsFeignService.updatePurcTaskReimId(entity));
                            break;
                        case "3":
                            // 物资报销
                            EcsPurmsReimIdEntity entity1 = new EcsPurmsReimIdEntity();
                            entity1.setReimId(reimId);
                            entity1.setPurcDetailIds(purcDetailIds);
                            FeignExecuteUtil.execute(mmisFeignService.updatePurcTaskReimId(entity1));
                            break;
                        default:
                            throw new AppException("未知的报销任务类型");
                    }
                });
            }
        });
    }

    @Override
    public void saveReimDetailNew(EcsReimDetailDto dto) {
        // if (StringUtils.isEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
        // throw new AppException("非医院成员无法报销");
        // }
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode()
                : dto.getSysUser().getUsername();
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setAppyerTime(DateUtil.getCurrentTime(null));
        if (StringUtils.equals(dto.getTempSave(), MedConst.TYPE_1)) {
            // 设置业务状态为未提交
            dto.setBusstas(EcsConst.REIM_BUSSTAS_UNSUBMIT);
        } else {
            dto.setBusstas(EcsConst.REIM_BUSSTAS_AUDITING);
        }
        if (StringUtils.isEmpty(dto.getAppyerDept())) {
            dto.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgId());
        }
        if (StringUtils.isNotEmpty(dto.getEvectionTime())) {
            String[] split = dto.getEvectionTime().split(",");
            if (split.length == 2) {
                dto.setEvectionBegnTime(split[0]);
                dto.setEvectionEndTime(split[1]);
            }
        }
        // 页面图片
        if (dto.getPageImageFile() != null) {
            String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/page/", dto.getPageImageFile());
            dto.setPageImage(filePath);
        }
        // 附件
        if (Arrays
                .asList(MedConst.TYPE_1, MedConst.TYPE_2, MedConst.TYPE_3,MedConst.TYPE_6, MedConst.TYPE_8, MedConst.TYPE_10,
                        MedConst.TYPE_11,MedConst.TYPE_12,MedConst.TYPE_13,MedConst.TYPE_14)
                .contains(dto.getType()) &&
                CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            List<List<String>> ossPaths = OSSUtil.getOSSPaths(dto.getAttFiles(), OSSConst.BUCKET_ECS, "reim/item/");
            dto.setAtt(String.join(",", ossPaths.get(0)));
            dto.setAttName(String.join(",", ossPaths.get(1)));
        }
        String batchNum = AuditConst.ECS_FYBX + ULIDUtil.generate();
        // dto.setAuditBchno(batchNum);
        String attCode = "ECS-" + ULIDUtil.generate();
        dto.setAttCode(attCode);
        if (!Objects.isNull(dto.getId())) {
            // 删除报销
            ecsReimDetailWriteMapper.deleteById(dto.getId());
            // 删除项目
            ecsReimDetailWriteMapper.deleteReimItemWithReimId(dto.getId().intValue());
            // 删除补助项目
            ecsReimDetailWriteMapper.deleteReimSubItemWithReimId(dto.getId().intValue());
            // 删除随行人员
            ecsReimDetailWriteMapper.deletePsnWithReimDetailId(dto.getId().intValue(), MedConst.TYPE_2);
            // TODO 分摊数据还需要删除分摊表和和往来单位表
        }
        String purcReimType = getPurcReimType(dto);
        dto.setType(purcReimType);
        ecsReimDetailWriteMapper.insert(dto);

        /*
         * List<String> attPaths = new ArrayList<>();
         * List<String> attNames = new ArrayList<>();
         * //附件外链路径(用于BPM审核流程展示)
         * if (!Objects.isNull(dto.getAtt())) {
         * String[] att = dto.getAtt().split(",");
         * String[] attName = dto.getAttName().split(",");
         * for (int i = 0; i < att.length; i++) {
         * attPaths.add();
         * attNames.add(attName[i]);
         * }
         * }
         */

        // 所有的发票id
        List<Long> invoIds = new ArrayList<>();
        // 更改申请状态
        if (StringUtils.isNotEmpty(dto.getType()) &&
                Arrays.asList(MedConst.TYPE_1, MedConst.TYPE_2).contains(dto.getType())) {
            // 更新差旅和培训申请状态，改成已报销，类型为1，2，
            if (dto.getTravelApprId() == null) {
                throw new AppException("未查询到差旅申请记录");
            }
            EcsReimTravelApprDto ecsReimTravelApprDto = new EcsReimTravelApprDto();
            ecsReimTravelApprDto.setId(dto.getTravelApprId());
            ecsReimTravelApprDto.setReimFlag(MedConst.TYPE_1);
            ecsReimTravelApprWriteMapper.updateById(ecsReimTravelApprDto);
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_4)) {
            // 保存分摊报销发票
            /*
             * if (CollectionUtil.isEmpty(dto.getShareInvos())) {
             * throw new AppException("分摊报销发票未上传");
             * }
             * 
             * List<List<String>> ossPaths =
             * OSSUtil.getOSSPaths(dto.getShareInvos(),OSSConst.BUCKET_ECS,"reim/item/");
             * LambdaUpdateWrapper<EcsReimDetailDto> shareWrapper = Wrappers.lambdaUpdate();
             * shareWrapper.set(EcsReimDetailDto::getAtt,String.join(",",ossPaths.get(0)))
             * .set(EcsReimDetailDto::getAttName,String.join(",",ossPaths.get(1)))
             * .set(EcsReimDetailDto::getInvoId,getInvoIdByFileIdentifier(dto.
             * getFileIdentifierMap(), dto.getShareInvos(), invoIds))
             * .eq(EcsReimDetailDto::getId,dto.getId());
             * ecsReimDetailWriteMapper.update(null,shareWrapper);
             */
            // 添加发票
            if (StringUtils.isNotEmpty(dto.getInvoId())) {
                invoIds.addAll(
                        Arrays.stream(dto.getInvoId().split(",")).map(Long::valueOf).collect(Collectors.toList()));
            }
            // 解析往来单位文件
            doShareFiles(dto);
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_5)) {
            // 更改工资申请状态
            if (dto.getTravelApprId() == null) {
                throw new AppException("未查询到工资待报销记录");
            }
            EcsReimSalaryTask salaryTask = new EcsReimSalaryTask();
            salaryTask.setId(dto.getTravelApprId().intValue());
            salaryTask.setReimFlag(MedConst.TYPE_1);
            salaryTask.setReimId(dto.getId().intValue());
            ecsReimSalaryTaskWriteMapper.updateById(salaryTask);

            // 新增附件信息
            EcsReimFileRecordDto fileRecordDto = new EcsReimFileRecordDto();
            fileRecordDto.setAtt(dto.getAtt());
            fileRecordDto.setAttCode(dto.getAttCode());
            fileRecordDto.setAttName(dto.getAttName());
            fileRecordDto.setType(MedConst.TYPE_1);

            ecsReimFileRecordWriteMapper.insert(fileRecordDto);

            // 更新报销id到工资任务表
            FeignExecuteUtil.execute(hrmFeignService.updateSalaryTaskReimId(dto.getSalaryId(), dto.getId().intValue()));
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_6)) {
            // 更改合同申请状态
            if (dto.getTravelApprId() == null) {
                throw new AppException("未查询到合同待报销记录");
            }
            EcsReimContractTask contractTask = new EcsReimContractTask();
            contractTask.setId(dto.getTravelApprId().intValue());
            contractTask.setReimFlag(MedConst.TYPE_1);
            contractTask.setReimId(dto.getId().intValue());
            ecsReimContractTaskWriteMapper.updateById(contractTask);

            // 更新报销id到合同任务表
            // FeignExecuteUtil
            // .execute(cmsFeignService.updatePaymentTermsReimId(dto.getPaymentId(),
            // dto.getId().intValue(),
            // null, null, null, null));

            // 更新合同付款条件的详细报销信息

            String reimbursePersonName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName()
                    : dto.getSysUser().getNickname();
            String reimburseDeptName = StringUtils.isNotEmpty(hrmUser.getHrmOrgName()) ? hrmUser.getHrmOrgName() : "";
            String reimburseTime = DateUtil.getCurrentTime(null);
            String reimburseNo = dto.getAuditBchno(); // 审核批次号作为报销单号

            FeignExecuteUtil.execute(cmsFeignService.updatePaymentTermsReimId(
                    dto.getPaymentId(),
                    dto.getId().intValue(),
                    reimbursePersonName,
                    reimburseDeptName,
                    reimburseTime,
                    reimburseNo));
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_8)) {
            savePurcReimDetail(dto);
        } else if (StringUtils.equals(dto.getType(), MedConst.TYPE_10)) {
            savePurcReimDetail(dto);
        }

        // 项目
        if (CollectionUtil.isNotEmpty(dto.getItemDetails())) {
            dto.getItemDetails().forEach(item -> {
                item.setReimDetailId(dto.getId());
                if (StringUtils.isNotEmpty(item.getInvoId())) {
                    invoIds.addAll(Arrays.stream(item.getInvoId()
                            .split(","))
                            .map(Long::valueOf)
                            .collect(Collectors.toList()));
                }
            });
            BatchUtil.batch("insertItemDetail", dto.getItemDetails(), EcsReimDetailWriteMapper.class);
            // 当type=14时，需要更新临床试验申请表中的信息
            if (dto.getType().equals(MedConst.TYPE_14)){
                List<Long> researcherFundingApplyIds = dto.getItemDetails().stream().map(item -> Long.valueOf(item.getResearcherFundingApplyId())).collect(Collectors.toList());
                ecsReimDetailWriteMapper.updateHrmResearcherFundingApplyStatus(researcherFundingApplyIds,"1");
            }
        }

        // 补助项目
        if (CollectionUtil.isNotEmpty(dto.getSubsItemDetails())) {
            dto.getSubsItemDetails().forEach(item -> {
                item.setReimDetailId(dto.getId());
                if (StringUtils.isNotEmpty(item.getInvoId())) {
                    invoIds.addAll(Arrays.stream(item.getInvoId()
                            .split(","))
                            .map(Long::valueOf)
                            .collect(Collectors.toList()));
                }
            });
            BatchUtil.batch("insertSubsItemDetail", dto.getSubsItemDetails(), EcsReimDetailWriteMapper.class);
        }
        // 出差人
        if (CollectionUtil.isNotEmpty(dto.getPsnDetails())) {
            dto.getPsnDetails().forEach(item -> {
                item.setType(MedConst.TYPE_2);
                item.setReimDetailId(dto.getId());
            });
            BatchUtil.batch("insertPsnDetail", dto.getPsnDetails(), EcsReimDetailWriteMapper.class);
        }

        // 更新发票记录表状态 临时保存不需要修改发票状态
        if (CollectionUtil.isNotEmpty(invoIds) && !StringUtils.equals(dto.getTempSave(), MedConst.TYPE_1)) {
            // 4 为审核中
            ecsInvoRcdWriteMapper.updateStateByIds(invoIds, MedConst.TYPE_4,
                    ReimTypeEnum.getByCode(dto.getType()).getMessage());
        }

        // 如果非临时保存，则进入审核
        if (!StringUtils.equals(dto.getTempSave(), MedConst.TYPE_1)) {
            // ------------BPM流程测试--------------
            // 发起BPM流程
            Map<String, Object> processInstanceVariables = new HashMap<>();
            // 获取参数 是否临床科室的培训报销 是否采管部门 是否职能科室 是否金额高于5W
            dto.getBpmParams().forEach((key, value) -> {
                processInstanceVariables.put(key, value);
            });
            processInstanceVariables.put(OSSConst.APP_ATT_PATHS, dto.getAtt());
            processInstanceVariables.put(OSSConst.APP_ATT_NAMES, dto.getAttName());
            processInstanceVariables.put(OSSConst.APP_ATT_BUCKET_NAME, OSSConst.BUCKET_ECS);

            String userCode = dto.getSysUser().getHrmUser().getEmpCode();
            // 获取审核流程
            String PROCESS_KEY = getProcessName(dto.getType(), dto.getBpmParams());
            if (StringUtils.isEmpty(PROCESS_KEY)) {
                throw new AppException("未获取到对应审核流程！");
            }

            /*
             * if (!Objects.isNull(dto.getBpmParams().get("isClinic"))) {
             * BizTypeToProcessEnum clinic =
             * BizTypeToProcessEnum.getSpecialProcess(dto.getType(), "Clinic");
             * if (ObjectUtils.isNotNull(clinic)) {
             * PROCESS_KEY = clinic.getProcessName();
             * }
             * // PROCESS_KEY = "ECS_EXPENSE_REIM_CLINC";
             * } else if (!Objects.isNull(dto.getBpmParams().get("isPCM"))) {
             * PROCESS_KEY = "ECS_EXPENSE_REIM_PCM";
             * } else {
             * PROCESS_KEY = "ECS_EXPENSE_REIM_OTHER";
             * }
             */

            try {
                val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
                bpmProcessInstanceCreateReqDTO
                        .setUserId(userCode)
                        .setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables)
                        .setBusinessKey(String.valueOf(dto.getId()));
                // .setStartUserSelectAssignees(dto.getStartUserSelectAssignees());
                CommonFeignResult processInstance = bpmProcessInstanceFeignApi
                        .createProcessInstance(bpmProcessInstanceCreateReqDTO);
                if (!StringUtils.equals(processInstance.get("code").toString(), "200")) {
                    throw new AppException("生成BPM流程异常");
                }
                String processInstanceId = processInstance.get("data").toString();
                // 将工作流编号，更新到报销中
                LambdaUpdateWrapper<EcsReimDetailDto> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(EcsReimDetailDto::getProcessInstanceId, processInstanceId)
                        .eq(EcsReimDetailDto::getId, dto.getId());
                ecsReimDetailWriteMapper.update(null, updateWrapper);
            } catch (Exception e) {
                log.error("BPM流程生成失败", e);
                throw new AppException("BPM流程生成失败");
            }

            // ------------BPM流程测试--------------
            // !MedConst.TYPE_3.equals(dto.getType())
            if (Arrays.asList(MedConst.TYPE_1, MedConst.TYPE_2).contains(dto.getType())) {
                push(dto, hrmUser, batchNum, appyer, "报销申请");
            } else {
                // 费用报销
                push2(dto, hrmUser, batchNum, appyer, "费用报销申请");
            }
        }
    }

    @Override
    public void saveReimDetailMultiTrip(SaveMultiTripDto dto) {

        String batchNum = AuditConst.ECS_FYBX + ULIDUtil.generate();

        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        List<List<String>> ossPaths = null;
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())){
            //暂时只针对 差旅、培训保存附件
            ossPaths = OSSUtil.getOSSPaths(dto.getAttFiles(), OSSConst.BUCKET_ECS, "reim/item/");
        }

        List<EcsReimDetailDto> ecsReimDetailDtoList = new ArrayList<>();
        String dataForms = dto.getDataForms();
        JSONArray jsonArray = JSON.parseArray(dataForms);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            EcsReimDetailDto ecsReimDetailDto = new EcsReimDetailDto();
            ecsReimDetailDto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
            ecsReimDetailDto.setAppyerTime(DateUtil.getCurrentTime(null));
            if (StringUtils.equals(jsonObject.getString("tempSave"), MedConst.TYPE_1)) {
                //设置业务状态为未提交
                ecsReimDetailDto.setBusstas(EcsConst.REIM_BUSSTAS_UNSUBMIT);
            } else {
                ecsReimDetailDto.setBusstas(EcsConst.REIM_BUSSTAS_AUDITING);
            }

            if (StringUtils.isEmpty(jsonObject.getString("appyerDept"))) {
                ecsReimDetailDto.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgId());
            }

            if (CollectionUtil.isNotEmpty(jsonObject.getJSONArray("evectionTime"))) {
                JSONArray evectionTimes = jsonObject.getJSONArray("evectionTime");
                if (evectionTimes.size() == 2) {
                    ecsReimDetailDto.setEvectionBegnTime(evectionTimes.getString(0));
                    ecsReimDetailDto.setEvectionEndTime(evectionTimes.getString(1));
                }
            }
            //页面图片
            if (dto.getPageImageFile() != null) {
                String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/page/", dto.getPageImageFile());
                ecsReimDetailDto.setPageImage(filePath);
            }

            //附件
            if (Arrays.asList(MedConst.TYPE_1, MedConst.TYPE_2, MedConst.TYPE_3,MedConst.TYPE_8).contains(jsonObject.getString("type")) &&
                    CollectionUtil.isNotEmpty(ossPaths)) {
                ecsReimDetailDto.setAtt(String.join(",", ossPaths.get(0)));
                ecsReimDetailDto.setAttName(String.join(",", ossPaths.get(1)));
            }

            String attCode = "ECS-" + ULIDUtil.generate();
            ecsReimDetailDto.setAttCode(attCode);
            if (!Objects.isNull(jsonObject.getLong("id"))) {
                Long id = jsonObject.getLong("id");
                //删除报销
                ecsReimDetailWriteMapper.deleteById(id);
                //删除项目
                ecsReimDetailWriteMapper.deleteReimItemWithReimId(id.intValue());
                //删除补助项目
                ecsReimDetailWriteMapper.deleteReimSubItemWithReimId(id.intValue());
                //删除随行人员
                ecsReimDetailWriteMapper.deletePsnWithReimDetailId(id.intValue(), MedConst.TYPE_2);
            }
            ecsReimDetailDto.setAppyer(appyer);
            ecsReimDetailDto.setEvectionAddr(jsonObject.getString("evectionAddr"));
            ecsReimDetailDto.setType(jsonObject.getString("type"));
            ecsReimDetailDto.setBank(jsonObject.getString("bank"));
            ecsReimDetailDto.setAcctname(jsonObject.getString("acctname"));
            ecsReimDetailDto.setBankcode(jsonObject.getString("bankcode"));
            ecsReimDetailDto.setOppositeName(jsonObject.getString("oppositeName"));
            ecsReimDetailDto.setSum(jsonObject.getBigDecimal("sum"));
            ecsReimDetailDto.setShareAmt(jsonObject.getBigDecimal("shareAmt"));
            ecsReimDetailDto.setCapSum(jsonObject.getString("capSum"));
            ecsReimDetailDto.setBusMet(jsonObject.getString("busMet"));
            ecsReimDetailDto.setEvectionAddrName(jsonObject.getString("evectionAddrName"));
            ecsReimDetailDto.setProjectId(jsonObject.getInteger("projectId"));
            ecsReimDetailDto.setFundingId(jsonObject.getInteger("fundingId"));
            ecsReimDetailDto.setEvectionRea(jsonObject.getString("evectionRea"));
            ecsReimDetailDto.setHospitalId(dto.getHospitalId());
            ecsReimDetailDto.setApprDeptType(jsonObject.getString("apprDeptType"));
            ecsReimDetailDto.setAuditFlag(jsonObject.getString("auditFlag"));
            ecsReimDetailDto.setAppyerDept(jsonObject.getString("apprDept"));
            ecsReimDetailDto.setTravelApprId(jsonObject.getLong("travelApprId"));
            ecsReimDetailDto.setTempSave(jsonObject.getString("tempSave"));
            if (ecsReimDetailDto.getTempSave() != null) {
                ecsReimDetailDto.setProcessInstanceId(batchNum);
            }

            ecsReimDetailWriteMapper.insert(ecsReimDetailDto);

            //更改申请状态
            if (StringUtils.isNotEmpty(ecsReimDetailDto.getType()) &&
                    Arrays.asList(MedConst.TYPE_1, MedConst.TYPE_2).contains(ecsReimDetailDto.getType())) {
                // 更新差旅和培训申请状态，改成已报销，类型为1，2，
                if (ecsReimDetailDto.getTravelApprId() == null) {
                    throw new AppException("未查询到差旅申请记录");
                }
                EcsReimTravelApprDto ecsReimTravelApprDto = new EcsReimTravelApprDto();
                ecsReimTravelApprDto.setId(ecsReimDetailDto.getTravelApprId());
                ecsReimTravelApprDto.setReimFlag(MedConst.TYPE_1);
                ecsReimTravelApprWriteMapper.updateById(ecsReimTravelApprDto);
            }

            // 所有的发票id
            List<Long> invoIds = new ArrayList<>();

            JSONObject tabs = jsonObject.getJSONObject("tabs");
            List<EcsReimItemDetail> itemDetails = tabs.getObject("itemDetails", new TypeReference<List<EcsReimItemDetail>>() {
            });
            // 项目
            if (CollectionUtil.isNotEmpty(itemDetails)) {
                itemDetails.forEach(item -> {
                    item.setReimDetailId(ecsReimDetailDto.getId());
                    if (StringUtils.isNotEmpty(item.getInvoId())) {
                        invoIds.addAll(Arrays.stream(item.getInvoId()
                                        .split(","))
                                .map(Long::valueOf)
                                .collect(Collectors.toList()));
                    }
                });
                BatchUtil.batch("insertItemDetail", itemDetails, EcsReimDetailWriteMapper.class);
            }

            List<EcsReimSubsItemDetail> subsItemDetails = tabs.getObject("subsItemDetails", new TypeReference<List<EcsReimSubsItemDetail>>() {
            });

            // 补助项目
            if (CollectionUtil.isNotEmpty(subsItemDetails)) {
                subsItemDetails.forEach(item -> {
                    item.setReimDetailId(ecsReimDetailDto.getId());
                    if (StringUtils.isNotEmpty(item.getInvoId())) {
                        invoIds.addAll(Arrays.stream(item.getInvoId()
                                        .split(","))
                                .map(Long::valueOf)
                                .collect(Collectors.toList()));
                    }
                });
                BatchUtil.batch("insertSubsItemDetail", subsItemDetails, EcsReimDetailWriteMapper.class);
            }

            List<EcsReimPsnDetail> psnDetails = jsonObject.getObject("psnDetails",new TypeReference<List<EcsReimPsnDetail>>() {});

            // 出差人
            if (CollectionUtil.isNotEmpty(psnDetails)) {
                psnDetails.forEach(item -> {
                    item.setType(MedConst.TYPE_2);
                    item.setReimDetailId(ecsReimDetailDto.getId());
                });
                BatchUtil.batch("insertPsnDetail", psnDetails, EcsReimDetailWriteMapper.class);
            }


            // 更新发票记录表状态  临时保存不需要修改发票状态
            if (CollectionUtil.isNotEmpty(invoIds) && !StringUtils.equals(ecsReimDetailDto.getTempSave(), MedConst.TYPE_1)) {
                // 4 为审核中
                ecsInvoRcdWriteMapper.updateStateByIds(invoIds, MedConst.TYPE_4, ReimTypeEnum.getByCode(ecsReimDetailDto.getType()).getMessage());
            }

            ecsReimDetailDtoList.add(ecsReimDetailDto);
        }

        EcsReimDetailDto ecsReimDetailDto = ecsReimDetailDtoList.get(0);
        String tempSave = ecsReimDetailDto.getTempSave();


        //如果非临时保存，则进入审核
        if (!StringUtils.equals(tempSave, MedConst.TYPE_1)) {
            //------------BPM流程测试--------------
            //发起BPM流程
            Map<String, Object> processInstanceVariables = new HashMap<>();
            //获取参数 是否临床科室的培训报销  是否采管部门  是否职能科室 是否金额高于5W
            dto.getBpmParams().forEach((key, value) -> {
                processInstanceVariables.put(key, value);
            });
            processInstanceVariables.put(OSSConst.APP_ATT_PATHS, ecsReimDetailDto.getAtt());
            processInstanceVariables.put(OSSConst.APP_ATT_NAMES, ecsReimDetailDto.getAttName());
            processInstanceVariables.put(OSSConst.APP_ATT_BUCKET_NAME,OSSConst.BUCKET_ECS);

            String userCode = dto.getSysUser().getHrmUser().getEmpCode();
            //获取审核流程
            String PROCESS_KEY = getProcessName(ecsReimDetailDto.getType(), dto.getBpmParams());
            if (StringUtils.isEmpty(PROCESS_KEY)) {
                throw new AppException("未获取到对应审核流程！");
            }

            try {
                val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
                bpmProcessInstanceCreateReqDTO
                        .setUserId(userCode)
                        .setProcessDefinitionKey("1".equals(dto.getType()) ? "ECS_DUTY_TRIP_APPR_TRIP_REIM" : "ECS_CLINICAL_TRIP_APPR_TRIP_REIM")
                        .setVariables(processInstanceVariables)
                        .setBusinessKey(ecsReimDetailDtoList.stream().map(item -> item.getId().toString()).collect(Collectors.joining(",")));
                CommonFeignResult processInstance = bpmProcessInstanceFeignApi.createProcessInstance(bpmProcessInstanceCreateReqDTO);
                if (!StringUtils.equals(processInstance.get("code").toString(), "200")) {
                    throw new AppException("生成BPM流程异常");
                }
                String processInstanceId = processInstance.get("data").toString();
                //将工作流编号，更新到报销中
                LambdaUpdateWrapper<EcsReimDetailDto> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(EcsReimDetailDto::getProcessInstanceId, processInstanceId)
                        .in(EcsReimDetailDto::getId, ecsReimDetailDtoList.stream().map(EcsReimDetailDto::getId).collect(Collectors.toList()));
                ecsReimDetailWriteMapper.update(null, updateWrapper);
            } catch (Exception e) {
                log.error("BPM流程生成失败", e);
                throw new AppException("BPM流程生成失败");
            }

            ecsReimDetailDtoList.forEach(item -> push(item, hrmUser, batchNum, appyer, "报销申请"));

        }
    }

    /**
     * 获取报销对应审核流程名
     * type 1:差旅 2：培训 3：其他费用 4：分摊 5：工资 6：合同 7：折旧 8：零星采购
     *
     * @param type
     * @param params
     * @return
     */
    private String getProcessName(String type, Map<String, String> params) {
        String processName = "";
        // 默认获取每个报销类的普通审核流程
        BizTypeToProcessEnum normal = BizTypeToProcessEnum.getSpecialProcess(type, "Normal");
        if (ObjectUtils.isNotNull(normal)) {
            processName = normal.getProcessName();
        }
        if (StringUtils.equals(type, MedConst.TYPE_1)) {
            if (ObjectUtils.isNotNull(params.get("isClinic"))) {
                BizTypeToProcessEnum sp = BizTypeToProcessEnum.getSpecialProcess(type, "Clinic");
                if (ObjectUtils.isNotNull(sp)) {
                    processName = sp.getProcessName();
                }
            }
        } else if (StringUtils.equals(type, MedConst.TYPE_2)) {
            if (ObjectUtils.isNotNull(params.get("isClinic"))) {
                BizTypeToProcessEnum sp = BizTypeToProcessEnum.getSpecialProcess(type, "Clinic");
                if (ObjectUtils.isNotNull(sp)) {
                    processName = sp.getProcessName();
                }
            }
        } else if (StringUtils.equals(type, MedConst.TYPE_3)) {
            if (ObjectUtils.isNotNull(params.get("isPCM"))) {
                BizTypeToProcessEnum sp = BizTypeToProcessEnum.getSpecialProcess(type, "PCM");
                if (ObjectUtils.isNotNull(sp)) {
                    processName = sp.getProcessName();
                }
            }
        } else if (StringUtils.equals(type, MedConst.TYPE_4)) {

        } else if (StringUtils.equals(type, MedConst.TYPE_5)) {

        } else if (StringUtils.equals(type, MedConst.TYPE_6)) {

        } else if (StringUtils.equals(type, MedConst.TYPE_7)) {

        } else if (StringUtils.equals(type, MedConst.TYPE_8)) {

        } else if (StringUtils.equals(type, MedConst.TYPE_10)) {

        } else if (StringUtils.equals(type, MedConst.TYPE_11)) {
            if (ObjectUtils.isNotNull(params.get("isPCM"))) {
                BizTypeToProcessEnum sp = BizTypeToProcessEnum.getSpecialProcess(type, "PCM");
                if (ObjectUtils.isNotNull(sp)) {
                    processName = sp.getProcessName();
                }
            }
        } else if (StringUtils.equals(type, MedConst.TYPE_13)) {

        }
        return processName;
    }

    public void push2(EcsReimDetailDto dto, HrmUser hrmUser, String batchNum, String appyer, String title) {
        // 审核流程
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            AppMsgSup appMsgSup = new AppMsgSup();
            appMsgSup.setTitle(title);
            String appyerName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName()
                    : dto.getSysUser().getNickname();
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);
            // 生成类型名称
            List<String> types = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(dto.getItemDetails())) {
                dto.getItemDetails().forEach(i -> {
                    if (StringUtils.isNotEmpty(i.getTypeName())) {
                        types.add(i.getTypeName());
                    }
                });
            }
            appMsgSup.setContent((types.size() > 0 ? types.toString() : "其他") + "费用报销，报销金额[" + dto.getSum() + "元]");
            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(batchNum);
            auditPayload.setTableTitle("摘要详情");
            auditPayload.setTableHeader("报销摘要,类型,金额");
            auditPayload.setTableContent("reimAbst,subName,amt");
            auditPayload.setDetailUrl("/ecs/ecsReimDetail/appAuditDetail2");
            Map<String, Object> map = new HashMap<>();
            map.put("appyerDeptName", "报销科室");
            auditPayload.setDisplayItem(map);
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(
                    new AuditDetail(batchNum, dto.getAuditDetails(), appMsgSup, auditPayload, OSSConst.BUCKET_ECS)));
        }
    }

    public void push(EcsReimDetailDto dto, HrmUser hrmUser, String batchNum, String appyer, String title) {
        // 审核流程
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            AppMsgSup appMsgSup = new AppMsgSup();
            appMsgSup.setTitle(title);
            String appyerName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName()
                    : dto.getSysUser().getNickname();
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);
            appMsgSup.setContent("出差地点是[" + dto.getEvectionAddrName() + "]，" +
                    "出差事由是[" + dto.getEvectionRea() + "]，" +
                    "出差时间[" + dto.getEvectionBegnTime() + " 至 " + dto.getEvectionEndTime() + "]，" +
                    "报销金额[" + dto.getSum() + "元]");
            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(batchNum);
            auditPayload.setAdditionItems(JSON.toJSONString(List.of(new HashMap<>() {
                {
                    put("title", "出差地点");
                    put("value", dto.getEvectionAddrName());
                }
            })));
            auditPayload.setTableTitle("出差人");
            auditPayload.setTableHeader("科室,出差人");
            auditPayload.setTableContent("dept,tripPsn");
            auditPayload.setDetailUrl("/ecs/ecsReimDetail/appAuditDetail");
            Map<String, Object> map = new HashMap<>();
            map.put("appEvectionTime", "出差时间");
            map.put("evectionAddrName", "出差地点");
            map.put("evectionRea", "出差事由");
            auditPayload.setDisplayItem(map);
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(
                    new AuditDetail(batchNum, dto.getAuditDetails(), appMsgSup, auditPayload, OSSConst.BUCKET_ECS)));
        }
    }

    private String getInvoIdByFileIdentifier(Map<String, Long> fileIdentifierMap, List<MultipartFile> files,
            List<Long> invoIds) {
        if (files != null && fileIdentifierMap != null) {
            List<String> ids = new ArrayList<>();
            files.forEach(f -> {
                try {
                    String identifier = generateFileIdentifier(f);
                    Long id = fileIdentifierMap.get(identifier);
                    if (id != null) {
                        invoIds.add(id);
                        ids.add(id.toString());
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            // 查询是否已经报销，防止前端篡改代码
            int count = ecsInvoRcdReadMapper.queryAlreadyReim(invoIds);
            if (count > 0) {
                throw new AppException("存在发票已经报销记录，请勿篡改报销");
            }
            return String.join(",", ids);
        }
        return null;
    }

    @Override
    public List<EcsInvoRcdVo> ocrIdentify(EcsReimDetailDto dto) {
        List<EcsInvoRcdVo> res = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            List<EcsInvoRcdDto> resRcd = new ArrayList<>();
            List<EcsInvoRcdDto> notNullRcds = new ArrayList<>();
            List<EcsInvoRcdDto> nullRcds = new ArrayList<>();
            dto.getAttFiles().forEach(file -> {
                String fileIdentifier = "";
                try {
                    fileIdentifier = generateFileIdentifier(file);
                } catch (Exception e) {
                    fileIdentifier = ULIDUtil.generate();
                }
                EcsInvoRcdDto ecsInvoRcdDto = new EcsInvoRcdDto();
                // OCR识别
                alyOcr(file, ecsInvoRcdDto);

                // 设置发票记录数据
                ecsInvoRcdDto.setAttFile(file);
                ecsInvoRcdDto.setAttName(file.getOriginalFilename());
                ecsInvoRcdDto.setCreateTime(DateUtil.getCurrentTime(null));
                ecsInvoRcdDto.setHospitalId(dto.getHospitalId());
                ecsInvoRcdDto.setChkState(MedConst.TYPE_0);
                ecsInvoRcdDto.setState(EcsConst.CAN_REIM);
                ecsInvoRcdDto.setFileIdentifier(fileIdentifier);
                ecsInvoRcdDto.setRcdIdentifier(ULIDUtil.generate());
                // 设置发票来源
                ecsInvoRcdDto.setInvoFrom(dto.getInvoFrom());
                // 设置创建人
                ecsInvoRcdDto.setCreateUser(dto.getSysUser().getUsername());
                String url = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/invo/", file);
                ecsInvoRcdDto.setAtt(url);
                resRcd.add(ecsInvoRcdDto);

                if (StringUtils.isNotEmpty(ecsInvoRcdDto.getInvoNum()) &&
                        StringUtils.isNotEmpty(ecsInvoRcdDto.getInvoDate())) {
                    notNullRcds.add(ecsInvoRcdDto);
                } else {
                    // 状态设置为识别失败
                    ecsInvoRcdDto.setState(EcsConst.OCR_ERROR);
                    nullRcds.add(ecsInvoRcdDto);
                }
            });
            // 识别后不为空的发票
            if (CollectionUtil.isNotEmpty(notNullRcds)) {
                List<EcsInvoRcdVo> rcdVos = ecsInvoRcdReadMapper.queryExistsRcd(notNullRcds);
                List<EcsInvoRcdDto> addRcds = new ArrayList<>();
                for (EcsInvoRcdDto notNullRcd : notNullRcds) {
                    List<EcsInvoRcdVo> collect = rcdVos.stream()
                            .filter(ecsInvoRcdVo -> ecsInvoRcdVo.getInvoNum().equals(notNullRcd.getInvoNum()) &&
                                    ecsInvoRcdVo.getInvoDate().equals(notNullRcd.getInvoDate()))
                            .collect(Collectors.toList());
                    if (collect.size() == 0) {
                        // String url = OSSUtil.uploadFile(OSSConst.BUCKET_ECS,"reim/invo/"
                        // ,notNullRcd.getAttFile());
                        // notNullRcd.setAtt(url);
                        addRcds.add(notNullRcd);
                    } else {
                        EcsInvoRcdVo t = collect.get(0);
                        notNullRcd.setId(t.getId());
                        notNullRcd.setState(t.getState());
                        notNullRcd.setChkState(t.getChkState());
                        notNullRcd.setChkData(t.getChkData());
                        notNullRcd.setChkTime(t.getChkTime());
                        notNullRcd.setPurchaserName(t.getPurchaserName());
                        notNullRcd.setPurchaserTaxpayerNumber(t.getPurchaserTaxpayerNumber());
                        notNullRcd.setInvoiceMoney(t.getInvoiceMoney());
                        notNullRcd.setAllValoremTax(t.getAllValoremTax());
                        notNullRcd.setFileIdentifier(t.getFileIdentifier());
                        notNullRcd.setCreateUser(t.getCreateUser());
                    }
                }

                // 需要新增的记录
                if (CollectionUtil.isNotEmpty(addRcds)) {
                    ArrayList<EcsInvoRcdDto> addRecords = distinct(addRcds);
                    ArrayList<EcsInvoRcdDto> insertRecords = new ArrayList<>();
                    // 发票核验
                    addRecords.forEach(d -> {
                        d.setChkTime(DateUtil.getCurrentTime(null));
                        try {
                            EcsInvoiceParam ecsInvoiceParam = new EcsInvoiceParam();
                            ecsInvoiceParam.setInvoiceCode(d.getInvoCode());
                            ecsInvoiceParam.setInvoiceNo(d.getInvoNum());
                            ecsInvoiceParam.setInvoiceDate(d.getInvoDate().replaceAll("[年月日]", ""));
                            if (StringUtils.isNotEmpty(d.getChkCode()) && d.getChkCode().length() == 20) {
                                ecsInvoiceParam.setVerifyCode(d.getChkCode().substring(d.getChkCode().length() - 6));
                            } else {
                                ecsInvoiceParam.setVerifyCode(d.getChkCode());
                            }
                            ecsInvoiceParam.setInvoiceSum(d.getTotalAmount());
                            String post = HttpRequestUtil.post(BeanUtil.beanToMap(ecsInvoiceParam),
                                    invoiceVerifyItfUrl);
                            if (StringUtils.isNotEmpty(post)) {
                                post = post.substring(1, post.length() - 1).replaceAll("\\\\", "");
                                EcsInvoiceRes ecsInvoiceRes = JSON.parseObject(post, EcsInvoiceRes.class);
                                if (ecsInvoiceRes != null) {
                                    switch (ecsInvoiceRes.getCode()) {
                                        case "200":
                                        case "201":
                                            d.setChkState(EcsConst.INVO_CHECK_STATE_VALID);
                                            d.setChkData(ecsInvoiceRes.getData());
                                            // insertRecords.add(d);
                                            break;
                                        default:
                                            // 设置核验状态
                                            d.setChkState(EcsConst.INVO_CHECK_STATE_INVALID);
                                            // 设置状态
                                            d.setState(EcsConst.VERIFY_ERROR);
                                            d.setChkData(ecsInvoiceRes.getMessage());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            // 核验不通过
                            d.setChkState(EcsConst.INVO_CHECK_STATE_INVALID);
                            // 设置状态
                            d.setState(EcsConst.VERIFY_ERROR);
                            d.setChkData(e.getMessage());
                        }
                        insertRecords.add(d);
                    });
                    // 插入发票核验数据及核验数据明细
                    List<EcsInvoChkRcdEntity> rcdEntityList = new ArrayList<>();
                    List<EcsInvoChkRcdDetailEntity> rcdDetailEntityList = new ArrayList<>();
                    insertRecords.stream().forEach(item -> {
                        if (StringUtils.equals(item.getChkState(), MedConst.TYPE_1)
                                && !Objects.isNull(item.getChkData())) {
                            JSONObject jsonObject = JSON.parseObject(item.getChkData());
                            EcsInvoChkRcdEntity rcdEntity = JSON.parseObject(jsonObject.getString("data"),
                                    EcsInvoChkRcdEntity.class);
                            // 存储发票核验基本信息
                            item.setPurchaserName(rcdEntity.getPurchaserName());
                            item.setPurchaserTaxpayerNumber(rcdEntity.getPurchaserTaxpayerNumber());
                            item.setInvoiceMoney(rcdEntity.getInvoiceMoney());
                            item.setAllValoremTax(rcdEntity.getAllValoremTax());
                            // 校验开具的发票是否规范
                            checkInvoInfo(item);
                            ecsInvoRcdWriteMapper.insert(item);
                            rcdEntity.setInvoRcdId(item.getId().intValue());
                            rcdEntityList.add(rcdEntity);
                            // ecsInvoRcdWriteMapper.insertInvoChkRcd(rcdEntity);
                            rcdEntity.getDetailList().forEach(rcdDetail -> {
                                rcdDetail.setInvoRcdId(item.getId().intValue());
                                rcdDetailEntityList.add(rcdDetail);
                                // ecsInvoRcdWriteMapper.insertInvoChkRcdDetail(rcdDetail);
                            });
                        } else {
                            ecsInvoRcdWriteMapper.insert(item);
                        }
                    });
                    BatchUtil.batch("insertInvoChkRcd", rcdEntityList, EcsInvoRcdWriteMapper.class);
                    BatchUtil.batch("insertInvoChkRcdDetail", rcdDetailEntityList, EcsInvoRcdWriteMapper.class);
                    // BatchUtil.batch(insertRecords, EcsInvoRcdWriteMapper.class);
                }
            }

            // 识别失败的发票
            if (CollectionUtil.isNotEmpty(nullRcds)) {
                List<EcsInvoRcdVo> rcdVos = ecsInvoRcdReadMapper.queryExistsRcdByHash(nullRcds);
                List<EcsInvoRcdDto> addRcds = new ArrayList<>();
                for (EcsInvoRcdDto nullRcd : nullRcds) {
                    List<EcsInvoRcdVo> collect = rcdVos.stream()
                            .filter(item -> StringUtils.equals(item.getFileIdentifier(), nullRcd.getFileIdentifier()))
                            .collect(Collectors.toList());
                    if (collect.size() == 0) {
                        addRcds.add(nullRcd);
                    } else {
                        EcsInvoRcdVo t = collect.get(0);
                        nullRcd.setId(t.getId());
                        nullRcd.setState(t.getState());
                        nullRcd.setChkState(t.getChkState());
                        nullRcd.setChkData(t.getChkData());
                        nullRcd.setChkTime(t.getChkTime());
                        nullRcd.setPurchaserName(t.getPurchaserName());
                        nullRcd.setPurchaserTaxpayerNumber(t.getPurchaserTaxpayerNumber());
                        nullRcd.setInvoiceMoney(t.getInvoiceMoney());
                        nullRcd.setAllValoremTax(t.getAllValoremTax());
                        nullRcd.setFileIdentifier(t.getFileIdentifier());
                        nullRcd.setCreateUser(t.getCreateUser());
                    }
                }
                // 需要新增的记录
                if (CollectionUtil.isNotEmpty(addRcds)) {
                    // 去重
                    ArrayList<EcsInvoRcdDto> collect = addRcds.stream().collect(
                            collectingAndThen(
                                    toCollection(
                                            () -> new TreeSet<>(Comparator.comparing(d -> d.getFileIdentifier()))),
                                    ArrayList::new));
                    // 新增
                    BatchUtil.batch(collect, EcsInvoRcdWriteMapper.class);
                }
            }

            // 返回结果
            if (CollectionUtil.isNotEmpty(resRcd)) {
                // 去重
                ArrayList<EcsInvoRcdDto> distinct = distinct(resRcd);
                // 存在重复记录
                if (distinct.size() != resRcd.size()) {
                    Map<String, List<EcsInvoRcdDto>> map = resRcd.stream()
                            .filter(d -> StringUtils.isNotEmpty(d.getInvoNum())).collect(Collectors.groupingBy(
                                    d -> d.getInvoCode() + d.getInvoNum() + d.getInvoDate() + d.getChkCode()));
                    map.forEach((s, ecsInvoRcdDtos) -> {
                        if (ecsInvoRcdDtos.size() > 1) {
                            // 重复发票
                            ecsInvoRcdDtos.forEach(d -> d.setState(EcsConst.REPEAT_INVO));
                        }
                    });
                }

                resRcd.forEach(invo -> {
                    EcsInvoRcdVo r = new EcsInvoRcdVo();
                    BeanUtils.copyProperties(invo, r);
                    res.add(r);
                });
            }
        }
        return res;
    }

    private void checkInvoInfo(EcsInvoRcdDto dto) {
        // 判断发票记录 单位名称/纳税人识别号
        if (StringUtils.equals(dto.getPurchaserName(), MedConst.INVOICE_UNIT_NAME)) {
            // 开给医院的，判断纳税识别号
            if (!StringUtils.equals(dto.getPurchaserTaxpayerNumber(), MedConst.INVOICE_TAXPAYER_NUMBER)) {
                dto.setState(EcsConst.INVO_MSG_ERROR);
            }
        } else {
            // 开给个人的，纳税识别号需要为空
            if (StringUtils.isNotEmpty(dto.getPurchaserTaxpayerNumber())) {
                dto.setState(EcsConst.INVO_MSG_ERROR);
            }
        }
    }

    /**
     * 检查需要校验的发票的二维码区信息是否一致
     * 
     * @param dto
     * @return
     */
    private boolean checkQrCodeIdentify(EcsInvoRcdDto dto) {
        // 正常的发票二维码信息也可能识别为空，如果为空，则不校验二维码信息
        if (StringUtils.isEmpty(dto.getQrCodeData())) {
            return false;
            // log.error("发票二维码区识别信息为空");
            // throw new AppException("发票二维码区识别信息为空");
        }
        // 如果识别的二维码结果为链接，为区块链发票，则不校验二维码信息
        // https://bcfp.shenzhen.chinatax.gov.cn/verify/scan?hash=018f2d79c9f36aa2e6f6e400b53426142d1bab5ac7f9d895fe2047e6e1a8b189f0&bill_num=09035741&total_amount=30000
        if (StringUtils.startsWith(dto.getQrCodeData(), "http")) {
            // 设置发票为区块链发票 1：区块链发票 2：非区块链发票
            dto.setInvoiceKind(MedConst.TYPE_1);
            return false;
        }
        // 二维码识别data示例 01,32,,24512000000144186957,30.00,20240717,,2935
        // 另一种二维码信息示例 CZ-EI-51,1.1.0,51060125,0193841241,8988f9,20250306,39227.40
        List<String> splitList = Arrays.asList(dto.getQrCodeData().split(","));
        if (StringUtils.startsWith(dto.getQrCodeData(), "01")) {
            BigDecimal expected = new BigDecimal(splitList.get(4)).setScale(2, RoundingMode.HALF_UP);
            BigDecimal allTax = new BigDecimal(dto.getAllValoremTax()).setScale(2, RoundingMode.HALF_UP);
            BigDecimal invoMoney = new BigDecimal(dto.getInvoiceMoney()).setScale(2, RoundingMode.HALF_UP);
            // 分别 号码、日期、金额 都出现在二维码识别信息中 (二维码识别的金额可能为含税金额，也可能为不含税金额，两者之存在即可)
            if (StringUtils.equals(splitList.get(3), dto.getInvoNum())
                    && StringUtils.equals(
                            splitList.get(5).replace("年", "").replace("月", "").replace("日", "").replace("-", ""),
                            dto.getInvoDate().replace("年", "").replace("月", "").replace("日", ""))
                    && (expected.compareTo(allTax) == 0) || expected.compareTo(invoMoney) == 0) {
                return false;
            }
        } else {
            List<String> splitList2 = Arrays.asList(dto.getQrCodeData().split(","));
            BigDecimal expected2 = new BigDecimal(splitList2.get(6)).setScale(2, RoundingMode.HALF_UP);
            BigDecimal allTax2 = new BigDecimal(dto.getAllValoremTax()).setScale(2, RoundingMode.HALF_UP);
            BigDecimal invoMoney2 = new BigDecimal(dto.getInvoiceMoney()).setScale(2, RoundingMode.HALF_UP);

            if (StringUtils.equals(splitList2.get(3), dto.getInvoNum())
                    && StringUtils.equals(
                            splitList2.get(5).replace("年", "").replace("月", "").replace("日", "").replace("-", ""),
                            dto.getInvoDate().replace("年", "").replace("月", "").replace("日", ""))
                    && (expected2.compareTo(allTax2) == 0) || expected2.compareTo(invoMoney2) == 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查发票抬头和税号
     * 
     * @param dto
     * @return
     */
    private boolean checkInvoInfoNew(EcsInvoRcdDto dto) {
        boolean error = false;
        // 判断发票记录 单位名称/纳税人识别号
        if (StringUtils.equals(dto.getPurchaserName(), MedConst.INVOICE_UNIT_NAME)) {
            // 开给医院的，判断纳税识别号
            if (!StringUtils.equals(dto.getPurchaserTaxpayerNumber(), MedConst.INVOICE_TAXPAYER_NUMBER)) {
                dto.setState(EcsConst.INVO_MSG_ERROR);
                error = true;
            }
        } else {
            // 开给个人的，纳税识别号需要为空
            if (StringUtils.isNotEmpty(dto.getPurchaserTaxpayerNumber())) {
                dto.setState(EcsConst.INVO_MSG_ERROR);
                error = true;
            }
        }
        return error;
    }

    @Override
    public void modifyReimAsst(EcsReimDetailDto dto) {
        if (CollectionUtil.isNotEmpty(dto.getReimAsstDetails())) {
            for (EcsReimAsstDetail reimAsstDetail : dto.getReimAsstDetails()) {
                reimAsstDetail.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
                reimAsstDetail.setCreateTime(DateUtil.getCurrentTime(null));
                reimAsstDetail.setHospitalId(dto.getHospitalId());
            }
            ecsReimDetailWriteMapper.addReimAsst(dto.getReimAsstDetails());
        }
    }

    @Override
    public void deleteNoAudit(EcsReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);

        // 查询当前报销信息
        EcsReimDetailDto ecsReimDetailDto = ecsReimDetailReadMapper.selectById(dto.getId());

        /*
         * if (StringUtils.equals(ecsReimDetailDto.getBusstas(), MedConst.TYPE_4)) {
         * throw new AppException("审核失败的流程才能删除");
         * }
         */
        // 审核失败或者报销未被审核则可以删除
        AuditDetail firstStep = ecsReimDetailReadMapper.getFirstAudit(dto);
        if (!StringUtils.equals(ecsReimDetailDto.getBusstas(), MedConst.TYPE_4)
                && (!StringUtils.equals(firstStep.getChkState(), MedConst.TYPE_0)
                        || StringUtils.isNotEmpty(firstStep.getChkTime()))) {
            throw new AppException("审核流程已开始审核，不能删除");
        }

        // 删除
        doDeleteNoAudit(dto);
    }

    @Override
    public void deleteNoAuditMultiTrip(EcsReimDetailDto dto) {

        if (Objects.isNull(dto.getProcessInstanceId()) || Objects.isNull(dto.getReimStatus())) {
            throw new AppException("参数不完整");
        }
        dto.setSqlAutowiredHospitalCondition(true);
        String processInstanceId = dto.getProcessInstanceId();
        List<EcsReimDetailDto> ecsReimDetailDtoList = ecsReimDetailReadMapper.selectList(
                Wrappers.lambdaQuery(EcsReimDetailDto.class)
                        .eq(EcsReimDetailDto::getProcessInstanceId, processInstanceId)
        );
        //前端已判断过，直接删除申请
        ecsReimDetailDtoList.forEach(this::doDeleteNoAudit);
        //删除对应流程实例
        Map<String, Object> variables = new HashMap<>();
        variables.put("isRunning", StringUtils.equals(dto.getReimStatus(), EcsConst.REIM_BUSSTAS_REJECTED) ? MedConst.TYPE_0 : MedConst.TYPE_1);
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                .setVariables(variables)
                .setProcessId(dto.getProcessInstanceId());
        CommonFeignResult processIns = bpmProcessInstanceFeignApi.deleteRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
        if (!StringUtils.equals(processIns.get("code").toString(), "200")) {
            throw new AppException("删除BPM流程异常");
        }


    }

    private void doDeleteNoAudit(EcsReimDetailDto dto) {
        EcsReimDetailDto item = ecsReimDetailReadMapper.selectById(dto.getId());
        if (Objects.isNull(item)) {
            throw new AppException(EcsConst.REIM_ITEM_NOT_EXSIST);
        }

        List<Long> invoIds = new ArrayList<>();
        List<String> invoStrs = new ArrayList<>();
        // 不同类型不同操作
        if (StringUtils.equals(item.getType(), MedConst.TYPE_1)
                || StringUtils.equals(item.getType(), MedConst.TYPE_2)) {
            // 回退申请的报销状态
            Long travelApprId = item.getTravelApprId();
            LambdaUpdateWrapper<EcsReimTravelApprDto> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(EcsReimTravelApprDto::getReimFlag, MedConst.TYPE_0)
                    .eq(EcsReimTravelApprDto::getId, travelApprId);
            ecsReimTravelApprWriteMapper.update(null, wrapper);
            // 获取对应项目、补助项目的发票id
            invoStrs = ecsReimDetailReadMapper.queryBusInvoIds(item);
        } else if (StringUtils.equals(item.getType(), MedConst.TYPE_4)) {
            if (StringUtils.isNotEmpty(item.getInvoId())) {
                String[] split = item.getInvoId().split(",");
                for (int i = 0; i < split.length; i++) {
                    invoIds.add(Long.parseLong(split[i]));
                }
            }
        } else if (StringUtils.equals(item.getType(), MedConst.TYPE_5)) {
            // 回退工资申请的报销状态
            Long travelApprId = item.getTravelApprId();
            LambdaUpdateWrapper<EcsReimSalaryTask> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(EcsReimSalaryTask::getReimFlag, MedConst.TYPE_0)
                    .set(EcsReimSalaryTask::getReimId, null)
                    .eq(EcsReimSalaryTask::getId, travelApprId);
            ecsReimSalaryTaskWriteMapper.update(null, wrapper);
            invoStrs = ecsReimDetailReadMapper.queryItemDetailsInvoIds(dto);
        } else if (StringUtils.equals(item.getType(), MedConst.TYPE_6)) {
            // 回退合同申请的报销状态
            Long travelApprId = item.getTravelApprId();
            LambdaUpdateWrapper<EcsReimContractTask> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(EcsReimContractTask::getReimFlag, MedConst.TYPE_0)
                    .set(EcsReimContractTask::getReimId, null)
                    .eq(EcsReimContractTask::getId, travelApprId);
            ecsReimContractTaskWriteMapper.update(null, wrapper);
            invoStrs = ecsReimDetailReadMapper.queryItemDetailsInvoIds(dto);
        } else if (StringUtils.equals(item.getType(), MedConst.TYPE_8) || StringUtils.equals(item.getType(), MedConst.TYPE_10)) {
            // 回退零星采购待申请报销状态
            LambdaUpdateWrapper<EcsReimPurcTask> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(EcsReimPurcTask::getReimFlag, MedConst.TYPE_0)
                    .eq(EcsReimPurcTask::getReimId, dto.getId());
            ecsReimPurcTaskWriteMapper.update(null, wrapper);
            // 回退零星采购明细待申请报销状态
            ecsReimPurcTaskWriteMapper.updatePurcTaskDetailsByReimId(dto.getId().intValue());
            // todo 调用feign 删除采购模块中对应任务的报销id
            invoStrs = ecsReimDetailReadMapper.queryItemDetailsInvoIds(dto);
        } else if (StringUtils.equals(item.getType(), MedConst.TYPE_14)){
            //查询项目信息
            List<EcsReimItemDetail> itemDetails = ecsReimDetailReadMapper.queryItemDetail(dto);
            List<Long> researcherFundingApplyIds = itemDetails.stream().map(EcsReimItemDetail::getResearcherFundingApplyId).collect(Collectors.toList());
            ecsReimDetailWriteMapper.updateHrmResearcherFundingApplyStatus(researcherFundingApplyIds,"0");
            invoStrs = ecsReimDetailReadMapper.queryItemDetailsInvoIds(dto);
        }else {
            // 获取项目的发票id
            invoStrs = ecsReimDetailReadMapper.queryItemDetailsInvoIds(dto);
        }
        if (CollectionUtil.isNotEmpty(invoStrs)) {
            invoStrs.stream().forEach(invoItem -> {
                String[] strArray = invoItem.split(",");
                List<Long> longArray = Arrays.stream(strArray)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                invoIds.addAll(longArray);
            });
        }
        // 更新发票id的发票状态
        if (CollectionUtil.isNotEmpty(invoIds)) {
            LambdaUpdateWrapper<EcsInvoRcdDto> wrapper = Wrappers.lambdaUpdate();
            wrapper.set(EcsInvoRcdDto::getState, MedConst.TYPE_1)
                    .in(EcsInvoRcdDto::getId, invoIds);
            // 如果当前是审核失败，则不更新发票状态(在审核拒绝时已经更新过)
            if (!StringUtils.equals(dto.getBusstas(), MedConst.TYPE_4)) {
                wrapper.set(EcsInvoRcdDto::getInvoUsedBy, "");
                ecsInvoRcdWriteMapper.update(null, wrapper);
            }
        }

        // 删除报销记录
        ecsReimDetailWriteMapper.deleteById(dto.getId());
    }

    @Override
    public void deleteNoAuditNew(EcsReimDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        // 前端已判断过，直接删除申请
        doDeleteNoAudit(dto);
        if (!Objects.isNull(dto.getProcessInstanceId())) {
            // 删除对应流程实例
            Map<String, Object> variables = new HashMap<>();
            variables.put("isRunning",
                    StringUtils.equals(dto.getReimStatus(), EcsConst.REIM_BUSSTAS_REJECTED) ? MedConst.TYPE_0
                            : MedConst.TYPE_1);
            val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
            bpmProcessInstanceCreateReqDTO
                    .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                    .setVariables(variables)
                    .setProcessId(dto.getProcessInstanceId());
            CommonFeignResult processIns = bpmProcessInstanceFeignApi
                    .deleteRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
            if (!StringUtils.equals(processIns.get("code").toString(), "200")) {
                throw new AppException("删除BPM流程异常");
            }
        }

    }

    /**
     * 上传付款证明文件
     *
     * @param dto
     */
    @Override
    public void uploadPayFiles(EcsReimDetailDto dto) {

        // 页面图片
        /*
         * if (dto.getPageImageFile() != null) {
         * String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/page/",
         * dto.getPageImageFile());
         * dto.setPageImage(filePath);
         * }
         */
        // 更新页面图片及数据
        ecsReimDetailWriteMapper.updateById(dto);

        // 是否为冲抵借款或者现金支付  零星采购和物资采购部上传付款单
        if ((!StringUtils.equals(dto.getType(),MedConst.TYPE_8)
                && !StringUtils.equals(dto.getType(), MedConst.TYPE_10))
                && StringUtils.equals(dto.getIsLoan(), MedConst.TYPE_0)
                && StringUtils.equals(dto.getPayMethod(), MedConst.TYPE_0)) {
            if (CollectionUtil.isEmpty(dto.getAttFiles())) {
                throw new AppException("付款证明文件不能为空");
            }

            List<List<String>> ossPaths = OSSUtil.getOSSPaths(dto.getAttFiles(), OSSConst.BUCKET_ECS, "reim/item/");

            List<EcsReimFileRecordDto> rcds = new ArrayList<>();
            for (int i = 0; i < ossPaths.get(0).size(); i++) {
                EcsReimFileRecordDto rcd = new EcsReimFileRecordDto();
                rcd.setAttCode(dto.getAttCode());
                rcd.setAtt(ossPaths.get(0).get(i));
                rcd.setAttName(ossPaths.get(1).get(i));
                rcd.setType(EcsConst.FILE_TYPE_PAY);
                rcds.add(rcd);
            }
            BatchUtil.batch("insertFileRecord", rcds, EcsReimFileRecordWriteMapper.class);
        }
    }

    /**
     * 更新资金类型
     *
     * @param dto
     */
    @Override
    public void updateFundType(EcsReimDetailDto dto) {
        ecsReimDetailWriteMapper.updateById(dto);
    }

    /**
     * 自定义的OCR识别，python百度开源版本
     *
     * @param file          文件
     * @param ecsInvoRcdDto 返回的数据
     */
    private void customOcr(MultipartFile file, EcsInvoRcdDto ecsInvoRcdDto) {
        // 临时存储到OSS获取外链
        String path = OSSUtil.uploadFile(OSSConst.BUCKET_TEMP, "reim/invo/", file);
        String url = OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_TEMP, path);

        // OCR识别
        EcsOCRParam ecsOCRParam = new EcsOCRParam();
        ecsOCRParam.setUrl(url);
        ecsOCRParam.setType(Objects.requireNonNull(file.getOriginalFilename()).contains(".pdf") ? "pdf" : "image");
        try {
            String post = HttpRequestUtil.post(BeanUtil.beanToMap(ecsOCRParam), ocrItfUrl);
            if (StringUtils.isNotEmpty(post)) {
                post = post.substring(1, post.length() - 1).replaceAll("\\\\", "");
                EcsOCRRes ecsOCRRes = JSON.parseObject(post, EcsOCRRes.class);
                if (ecsOCRRes != null && "200".equals(ecsOCRRes.getCode())) {
                    BeanUtils.copyProperties(ecsOCRRes.getData(), ecsInvoRcdDto);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 阿里云OCR识别
     *
     * @param file          文件
     * @param ecsInvoRcdDto 返回的数据
     * @throws IOException
     */
    private void alyOcr(MultipartFile file, EcsInvoRcdDto ecsInvoRcdDto) {
        try {
            String boundary = "---------------------------1234567890"; // 定义boundary
            URL url = new URL(ocrItfUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            OutputStream outputStream = connection.getOutputStream();
            PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true);

            // 添加其他参数
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"type\"").append("\r\n");
            writer.append("\r\n");
            writer.append("Invoice").append("\r\n"); // 设置参数值
            writer.flush();

            // 添加文件数据
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"")
                    .append("\r\n");
            writer.append("Content-Type: " + HttpURLConnection.guessContentTypeFromName(file.getName())).append("\r\n");
            writer.append("\r\n");
            writer.flush();

            InputStream fileInputStream = file.getInputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.flush();
            fileInputStream.close();

            writer.append("\r\n");
            writer.append("--" + boundary + "--").append("\r\n");
            writer.close();

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                response.append(line);
            }

            reader.close();
            outputStream.close();

            // 处理JSON响应
            String post = response.toString();
            log.error("当前识别的发票返回结果string-------------" + post);
            if (StringUtils.isNotEmpty(post)) {
                EcsOCRRes ecsOCRRes = JSON.parseObject(post, EcsOCRRes.class);
                if (ecsOCRRes != null) {
                    if ("200".equals(ecsOCRRes.getCode())) {
                        BeanUtils.copyProperties(ecsOCRRes.getData(), ecsInvoRcdDto);
                    }
                }
            }

            connection.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private ArrayList<EcsInvoRcdDto> distinct(List<EcsInvoRcdDto> rcds) {
        return rcds.stream().collect(
                collectingAndThen(
                        toCollection(
                                () -> new TreeSet<>(Comparator.comparing(
                                        d -> d.getInvoCode() + d.getInvoNum() + d.getInvoDate() + d.getChkCode()))),
                        ArrayList::new));
    }

    public String generateFileIdentifier(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] fileBytes = file.getBytes();

        // 将文件内容和文件名称结合在一起
        /*
         * String fileName = file.getOriginalFilename();
         * String identifier = fileName + new String(fileBytes);
         * 
         * byte[] digest = md.digest(identifier.getBytes());
         */
        byte[] digest = md.digest(fileBytes);

        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b & 0xff));
        }

        return sb.toString();
    }

    private List<EcsReimRelCoDetail> getRelCoList(EcsReimDetailDto dto, boolean insert) {
        if (CollectionUtil.isEmpty(dto.getAttFiles())) {
            return new ArrayList<>();
        }
        MultipartFile file = dto.getAttFiles().get(0);
        if (Objects.isNull(file)) {
            throw new AppException("往来单位不能为空");
        }
        List<EcsReimRelCoDetail> relCoDetails = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), EcsReimRelCoDetail.class,
                    new AnalysisEventListener<EcsReimRelCoDetail>() {
                        @Override
                        public void invoke(EcsReimRelCoDetail data, AnalysisContext analysisContext) {
                            if (!StringUtils.equals(data.getRelCoName(), "总计")) {
                                if (StringUtils.isEmpty(data.getRelCoCode())) {
                                    throw new AppException("存在往来单位代码为空的数据");
                                }
                                if (Objects.isNull(data.getAmt())) {
                                    data.setAmt(BigDecimal.ZERO);
                                }
                                data.setReimDetailId(dto.getId().intValue());
                                relCoDetails.add(data);
                            }
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                        }
                    }).sheet().headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AppException("往来单位文件解析失败");
        }
        // 上传往来单位文件
        String path = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/item/", file);
        if (insert) {
            EcsReimFileRecordDto rcd = new EcsReimFileRecordDto();
            rcd.setAtt(path);
            rcd.setAttName(file.getOriginalFilename());
            rcd.setType(EcsConst.FILE_TYPE_GENERAL);
            rcd.setAttCode(dto.getAttCode());
            ecsReimFileRecordWriteMapper.insert(rcd);
            // 新增往来单位信息
            BatchUtil.batch("insertRelCoDetail", relCoDetails, EcsReimDetailWriteMapper.class);
        }
        return relCoDetails;
    }

    /**
     * 生成分摊费用文件
     *
     * @param dto 报销信息
     */
    private void doShareFiles(EcsReimDetailDto dto) {
        // 读取分摊数据模板文件，获取用友部门数据
        ShareExcelEnum excelEnum = ShareExcelEnum.getByType(dto.getShareType());
        // 判断分摊方式，1:按人数(需计算人数), 2:按面积(以模板中面积为准)
        if (StringUtils.equals(excelEnum.getShareMode(), MedConst.TYPE_1)) {
            List<EcsReimRelCoDetail> relCoDetails = getRelCoList(dto, true);
            List<EcsReimShareEntity> yyDeptList = new ArrayList<>();
            yyDeptList = getYYdeptList(excelEnum.getSheetIdx(), excelEnum.getStartRow(), excelEnum.getEndRow());
            // 查询科室关系映射数据
            CommonResult<List<HrmOrgAgencyMapVo>> result = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());
            if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
                throw new AppException("当前未维护科室关系映射");
            }
            EmpEmployeeInfoDto employeeParam = new EmpEmployeeInfoDto();
            employeeParam.setPageNum(1);
            employeeParam.setPageSize(99999);
            employeeParam.setIsAdmin(true);
            CommonResult<Page<EmpEmployeeInfoVo>> data = empEmployeeFeignService.queryList(employeeParam);
            // 排除没有部门的人员
            List<EmpEmployeeInfoVo> employeeResult = data.getData().getRecords()
                    .stream().filter(item -> StringUtils.isNotEmpty(item.getOrgId())).collect(Collectors.toList());
            // 计算各个科室人数
            popWithDept(dto, excelEnum, yyDeptList, result.getData(), employeeResult, relCoDetails, true);
            // 填充excel
            doShareExcel(dto, excelEnum, yyDeptList, relCoDetails, true);
        } else if (StringUtils.equals(excelEnum.getShareMode(), MedConst.TYPE_2)) {

        } else if (StringUtils.equals(excelEnum.getShareMode(), MedConst.TYPE_3)) {
            // 燃气分摊，按照床位
            List<EcsReimShareGasExcelEntity> gasDepts = getGasYYdeptList(dto, excelEnum);
            // 计算科室分摊数据，按照床位数量分摊金额
            calAmount(gasDepts, dto.getShareAmt(), true);
            // 填充excel
            doGasShareExcel(dto, excelEnum, gasDepts, true);
        }
    }

    /**
     * 生成分摊结果excel
     * 
     * @param dto
     */
    @Override
    public Map<String, String> generateShareResExcel(EcsReimDetailDto dto) {
        // 读取分摊数据模板文件，获取用友部门数据
        ShareExcelEnum excelEnum = ShareExcelEnum.getByType(dto.getShareType());
        dto.setId(-1L);
        Map<String, String> res = new HashMap<>();
        if (StringUtils.equals(excelEnum.getShareMode(), MedConst.TYPE_1)) {
            List<EcsReimRelCoDetail> relCoDetails = getRelCoList(dto, false); // 只解析，不插入
            List<EcsReimShareEntity> yyDeptList = new ArrayList<>();
            yyDeptList = getYYdeptList(excelEnum.getSheetIdx(), excelEnum.getStartRow(), excelEnum.getEndRow());
            // 查询科室关系映射数据
            CommonResult<List<HrmOrgAgencyMapVo>> result = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());
            if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
                throw new AppException("当前未维护科室关系映射");
            }
            EmpEmployeeInfoDto employeeParam = new EmpEmployeeInfoDto();
            employeeParam.setPageNum(1);
            employeeParam.setPageSize(99999);
            employeeParam.setIsAdmin(true);
            CommonResult<Page<EmpEmployeeInfoVo>> data = empEmployeeFeignService.queryList(employeeParam);
            // 排除没有部门的人员
            List<EmpEmployeeInfoVo> employeeResult = data.getData().getRecords()
                    .stream().filter(item -> StringUtils.isNotEmpty(item.getOrgId())).collect(Collectors.toList());
            // 计算各个科室人数
            popWithDept(dto, excelEnum, yyDeptList, result.getData(), employeeResult, relCoDetails, false); // 只计算，不插入
            // 填充excel
            res = doShareExcel(dto, excelEnum, yyDeptList, relCoDetails, false); // 只填充上传，不插入库
        } else if (StringUtils.equals(excelEnum.getShareMode(), MedConst.TYPE_2)) {

        } else if (StringUtils.equals(excelEnum.getShareMode(), MedConst.TYPE_3)) {
            // 燃气分摊，按照床位
            List<EcsReimShareGasExcelEntity> gasDepts = getGasYYdeptList(dto, excelEnum);
            // 计算科室分摊数据，按照床位数量分摊金额
            calAmount(gasDepts, dto.getShareAmt(), false); // 只计算金额，不插入
            // 填充excel
            res = doGasShareExcel(dto, excelEnum, gasDepts, false); // 只填充，不插入库
        }
        return res;
    }

    private List<EcsReimShareEntity> getYYdeptList(int sheetIdx, int startRow, int endRow) {
        List<EcsReimShareEntity> yyDeptList = new ArrayList<>();
        try (InputStream templateIS = OSSUtil.getObject(OSSConst.BUCKET_ECS, "template/分摊费用数据模板.xlsx")) {
            // 读取分摊部门数据(用友部门)
            EasyExcel.read(templateIS, EcsReimShareEntity.class, new AnalysisEventListener<EcsReimShareEntity>() {
                @Override
                public void invoke(EcsReimShareEntity data, AnalysisContext analysisContext) {
                    int currentRow = analysisContext.readRowHolder().getRowIndex(); // 当前行号
                    if (currentRow >= startRow && currentRow <= endRow) {
                        // 将范围内数据添加到list
                        yyDeptList.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                }
            }).sheet(sheetIdx).headRowNumber(3).doRead();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AppException("分摊费用数据模板解析失败");
        }

        return yyDeptList;
    }

    /**
     * 获取燃气费需分摊的部门
     *
     * @param dto
     * @return
     */
    private List<EcsReimShareGasExcelEntity> getGasYYdeptList(EcsReimDetailDto dto, ShareExcelEnum excelEnum) {
        List<EcsReimShareGasExcelEntity> yyDeptList = new ArrayList<>();

        String abs = dto.getShareDate() + excelEnum.getName() + "支出";
        try (InputStream templateIS = OSSUtil.getObject(OSSConst.BUCKET_ECS, "template/分摊费用数据模板.xlsx")) {
            // 读取分摊部门数据(用友部门)
            EasyExcel.read(templateIS, EcsReimShareGasExcelEntity.class,
                    new AnalysisEventListener<EcsReimShareGasExcelEntity>() {
                        @Override
                        public void invoke(EcsReimShareGasExcelEntity data, AnalysisContext analysisContext) {
                            int currentRow = analysisContext.readRowHolder().getRowIndex(); // 当前行号
                            if (currentRow >= excelEnum.getStartRow() && currentRow <= excelEnum.getEndRow()) {
                                if (StringUtils.isEmpty(data.getDeptCode())) {
                                    throw new AppException("存在科室代码为空的数据");
                                }
                                // 将范围内数据添加到list
                                data.setReimDetailId(dto.getId().intValue());
                                data.setShareType(dto.getShareType());
                                data.setAbs(abs);
                                yyDeptList.add(data);
                            }
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                        }
                    }).sheet(excelEnum.getSheetIdx()).headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AppException("分摊费用数据模板解析失败");
        }

        return yyDeptList;
        /*
         * if (CollectionUtil.isEmpty(dto.getAttFiles())) {
         * throw new AppException("分摊燃气费基准不能为空");
         * }
         * MultipartFile file = dto.getAttFiles().get(0);
         * if (Objects.isNull(file)) {
         * throw new AppException("分摊燃气费基准不能为空");
         * }
         * List<EcsReimShareGasExcelEntity> yyDeptList = new ArrayList<>();
         * ShareExcelEnum excelEnum = ShareExcelEnum.getByType(dto.getShareType());
         * String abs = dto.getShareDate() + excelEnum.getName() + "支出";
         * try {
         * EasyExcel.read(file.getInputStream(), EcsReimShareGasExcelEntity.class, new
         * AnalysisEventListener<EcsReimShareGasExcelEntity>() {
         * 
         * @Override
         * public void invoke(EcsReimShareGasExcelEntity data, AnalysisContext
         * analysisContext) {
         * if (!StringUtils.equals(data.getDeptName(),"合计") &&
         * !StringUtils.equals(data.getDeptName(),"计入成本合计")) {
         * if (StringUtils.isEmpty(data.getDeptCode())) {
         * throw new AppException("存在科室代码为空的数据");
         * }
         * data.setReimDetailId(dto.getId().intValue());
         * data.setShareType(dto.getShareType());
         * data.setAbs(abs);
         * yyDeptList.add(data);
         * }
         * }
         * 
         * @Override
         * public void doAfterAllAnalysed(AnalysisContext analysisContext) {
         * 
         * }
         * }).sheet().headRowNumber(2).doRead();
         * } catch (Exception e){
         * log.error(e.getMessage(),e);
         * throw new AppException("燃气费基准分摊文件解析失败");
         * }
         * return yyDeptList;
         */
    }

    /**
     * 获取科室人数()
     *
     * @param yyDepts      用友的部门编码
     * @param deptRelation 用友-hrp-his的部门关系映射
     * @param employees    hrp的人员信息(含有部门编码)
     */
    private void popWithDept(EcsReimDetailDto dto,
            ShareExcelEnum excelEnum,
            List<EcsReimShareEntity> yyDepts,
            List<HrmOrgAgencyMapVo> deptRelation,
            List<EmpEmployeeInfoVo> employees,
            List<EcsReimRelCoDetail> relCoDetails, boolean insert) {
        Map<String, List<HrmOrgAgencyMapVo>> orgAgencyMap = deptRelation.stream()
                .collect(Collectors.groupingBy(HrmOrgAgencyMapVo::getYyOrgCode));
        Map<String, List<EmpEmployeeInfoVo>> employeeMap = employees.stream()
                .collect(Collectors.groupingBy(EmpEmployeeInfoVo::getOrgId));

        // 摘要
        String abs = dto.getShareDate() + excelEnum.getName() + "支出";
        int totalPersonNum = 0;
        for (EcsReimShareEntity item : yyDepts) {
            String yyOrgId = item.getDeptCode();
            List<HrmOrgAgencyMapVo> hrpDeptRelation = orgAgencyMap.get(yyOrgId);
            int hrpDeptNum = 0;
            if (!Objects.isNull(hrpDeptRelation)) {
                for (HrmOrgAgencyMapVo hd : hrpDeptRelation) {
                    List<EmpEmployeeInfoVo> employeeInfoVoList = employeeMap.get(hd.getHrpOrgCode());
                    if (CollectionUtil.isNotEmpty(employeeInfoVoList)) {
                        hrpDeptNum += employeeInfoVoList.size();
                    }
                }
            }
            totalPersonNum += hrpDeptNum;
            item.setBase(Double.valueOf(hrpDeptNum));
        }
        BigDecimal needShareAmt;
        if (CollectionUtil.isNotEmpty(relCoDetails)) {
            needShareAmt = relCoDetails.stream()
                    .map(EcsReimRelCoDetail::getAmt)
                    .reduce(dto.getShareAmt(), BigDecimal::subtract);
        } else {
            needShareAmt = dto.getShareAmt();
        }
        BigDecimal unit = needShareAmt.divide(new BigDecimal(totalPersonNum), 4, RoundingMode.HALF_UP);
        // 解决四舍五入后合计有误差问题
        BigDecimal roundTotal = new BigDecimal(0);
        // 获取最后一个部门人数不为0的部门，将最后的值加到这个部门上
        int lastDeptIdx = 0;
        for (int i = 0; i < yyDepts.size(); i++) {
            EcsReimShareEntity item = yyDepts.get(i);
            if (item.getBase().intValue() != 0) {
                lastDeptIdx = i;
            }
            item.setReimDetailId(dto.getId().intValue());
            item.setShareType(excelEnum.getType());
            BigDecimal curSum = unit.multiply(new BigDecimal(item.getBase())).setScale(2, BigDecimal.ROUND_HALF_UP);
            roundTotal = roundTotal.add(curSum);
            item.setAmt(curSum);
            item.setAbs(abs);
        }

        EcsReimShareEntity restItem = yyDepts.get(lastDeptIdx);
        restItem.setAmt(restItem.getAmt().add(needShareAmt.subtract(roundTotal)).setScale(2, BigDecimal.ROUND_HALF_UP));
        // 保存科室分摊数据
        if (insert) {
            BatchUtil.batch("insertShareDetail", yyDepts, EcsReimDetailWriteMapper.class);
        }
    }

    private Map<String, String> doShareExcel(EcsReimDetailDto dto, ShareExcelEnum excelEnum,
            List<EcsReimShareEntity> shareInfos, List<EcsReimRelCoDetail> relCoDetails, boolean insert) {
        Map<String, String> res = new HashMap<>();
        // 分摊金额(减去往来单位金额)
        BigDecimal needShareAmt;
        if (CollectionUtil.isNotEmpty(relCoDetails)) {
            needShareAmt = relCoDetails.stream()
                    .map(EcsReimRelCoDetail::getAmt)
                    .reduce(dto.getShareAmt(), BigDecimal::subtract);
        } else {
            needShareAmt = dto.getShareAmt();
        }
        // 填充excel信息
        Map<String, String> totalInfo = new HashMap<>();
        totalInfo.put("amtTotal", needShareAmt.toString());

        try (InputStream tempIS = OSSUtil.getObject(OSSConst.BUCKET_ECS, excelEnum.getExcelPath());
                ByteArrayOutputStream os = new ByteArrayOutputStream()) {

            // 获取模板文件流，将数据写入到临时文件流中
            ExcelWriter excelWriter = EasyExcel.write(os).inMemory(true).withTemplate(tempIS).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            // 分摊方式为人数分摊(写入摘要和人数) 为面积分摊(写入摘要)
            excelWriter.fill(new FillWrapper("data1", shareInfos), writeSheet);

            /*
             * 不写入往来单位信息到excel
             * if (CollectionUtil.isNotEmpty(relCoDetails)) {
             * //往来单位信息
             * excelWriter.fill(new FillWrapper("data2",relCoDetails),writeSheet);
             * }
             */
            excelWriter.fill(totalInfo, writeSheet);

            // 计算公式
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            workbook.getCreationHelper().createFormulaEvaluator().evaluateAll();

            excelWriter.finish();

            InputStream is = new ByteArrayInputStream(os.toByteArray());
            String path = "reim/item/" + ULIDUtil.generate() + ".xlsx";
            OSSUtil.uploadFile(OSSConst.BUCKET_ECS, path, is);
            is.close();
            res.put("att", path);
            res.put("attName", dto.getShareDate() + "各科室" + excelEnum.getName() + "分摊明细" + ".xlsx");
            // 保存当前excel文件到文件记录表中
            if (insert) {
                EcsReimFileRecordDto record = new EcsReimFileRecordDto();
                record.setAtt(path);
                record.setAttName(dto.getShareDate() + "各科室" + excelEnum.getName() + "分摊明细" + ".xlsx");
                record.setType(EcsConst.FILE_TYPE_GENERAL);
                record.setAttCode(dto.getAttCode());
                ecsReimFileRecordWriteMapper.insert(record);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AppException("生成分摊费用文件失败");
        }
        return res;
    }

    private Map<String, String> doGasShareExcel(EcsReimDetailDto dto, ShareExcelEnum excelEnum,
            List<EcsReimShareGasExcelEntity> shareInfo, boolean insert) {
        // 返回文件路径
        Map<String, String> res = new HashMap<>();
        // 填充信息
        Map<String, String> fillInfos = new HashMap<>();
        fillInfos.put("amtTotal", dto.getShareAmt().toString());
        fillInfos.put("year", StringUtils.substring(dto.getShareDate(), 0, 4));
        fillInfos.put("month", StringUtils.substring(dto.getShareDate(), 5, 7));
        try (InputStream tempIS = OSSUtil.getObject(OSSConst.BUCKET_ECS, excelEnum.getExcelPath());
                ByteArrayOutputStream os = new ByteArrayOutputStream()) {

            // 获取模板文件流，将数据写入到临时文件中
            ExcelWriter excelWriter = EasyExcel.write(os).inMemory(true).withTemplate(tempIS).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            // 写入列表
            excelWriter.fill(new FillWrapper("data", shareInfo), writeSheet);
            // 写入其他数据
            excelWriter.fill(fillInfos, writeSheet);
            // 计算公式，防止excel 本身不调用公式
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            workbook.getCreationHelper().createFormulaEvaluator().evaluateAll();

            excelWriter.finish();

            // 上传并记录
            InputStream is = new ByteArrayInputStream(os.toByteArray());
            String path = "reim/item/" + ULIDUtil.generate() + ".xlsx";
            OSSUtil.uploadFile(OSSConst.BUCKET_ECS, path, is);
            is.close();
            res.put("att", path);
            res.put("attName", dto.getShareDate() + "各科室" + excelEnum.getName() + "分摊明细" + ".xlsx");
            // 保存当前excel文件到文件记录表中
            if (insert) {
                EcsReimFileRecordDto record = new EcsReimFileRecordDto();
                record.setAtt(path);
                record.setAttName(dto.getShareDate() + "各科室" + excelEnum.getName() + "分摊明细" + ".xlsx");
                record.setType(EcsConst.FILE_TYPE_GENERAL);
                record.setAttCode(dto.getAttCode());
                ecsReimFileRecordWriteMapper.insert(record);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AppException("生成分摊费用文件失败");
        }
        return res;
    }

    private void calAmount(List<EcsReimShareGasExcelEntity> gasDept, BigDecimal totalAmt, boolean insert) {
        // 计算总床位数量
        int sum = gasDept.stream().mapToInt(EcsReimShareGasExcelEntity::getBase).sum();
        // 计算平均值
        BigDecimal unit = totalAmt.divide(new BigDecimal(sum), 4, RoundingMode.HALF_UP);
        // 解决四舍五入后合计有误差问题，只填写到倒数第二个实体类，将剩余的值直接填写到最后的实体类中
        BigDecimal roundSum = BigDecimal.ZERO;
        for (int i = 0; i < gasDept.size() - 1; i++) {
            EcsReimShareGasExcelEntity gas = gasDept.get(i);
            // 床位数 * 平均数 = 科室分摊金额
            BigDecimal curAmt = new BigDecimal(gas.getBase()).multiply(unit).setScale(2, BigDecimal.ROUND_HALF_UP);
            gas.setAmt(curAmt);
            roundSum = roundSum.add(curAmt);
        }
        gasDept.get(gasDept.size() - 1).setAmt(totalAmt.subtract(roundSum).setScale(2, BigDecimal.ROUND_HALF_UP));
        List<EcsReimShareEntity> insertRcds = gasDept.stream().map(e -> {
            EcsReimShareEntity a = new EcsReimShareEntity();
            a.setReimDetailId(e.getReimDetailId());
            a.setShareType(e.getShareType());
            a.setDeptCode(e.getDeptCode());
            a.setDeptName(e.getDeptName());
            a.setBase(e.getBase().doubleValue());
            a.setAmt(e.getAmt());
            a.setAbs(e.getAbs());
            return a;
        }).collect(Collectors.toList());

        // 保存科室分摊数据
        if (insert) {
            BatchUtil.batch("insertShareDetail", insertRcds, EcsReimDetailWriteMapper.class);
        }
    }

    @Override
    public void updateProItems(EcsReimDetailDto dto) {
        // throw new AppException("手动抛出异常");
        Long id = dto.getItemDetails().get(0).getReimDetailId();
        // 项目
        BatchUtil.batch("updateItemDetail", dto.getItemDetails(), EcsReimDetailWriteMapper.class);
        // 补助项目
        BatchUtil.batch("updateSubsItemDetail", dto.getSubsItemDetails(), EcsReimDetailWriteMapper.class);
        // 更新随行人员金额
        BatchUtil.batch("updatePsnDetail", dto.getPsnDetails(), EcsReimDetailWriteMapper.class);
        // 更新报销单的合计金额
        LambdaUpdateWrapper<EcsReimDetailDto> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(EcsReimDetailDto::getSum, dto.getSum())
                .set(EcsReimDetailDto::getCapSum, dto.getCapSum())
                .eq(EcsReimDetailDto::getId, id);
        ecsReimDetailWriteMapper.update(null, wrapper);
    }

    @Override
    public void updateContractItems(EcsReimDetailDto dto) {
        // 更新合同项目类别
        BatchUtil.batch("updateContractItems", dto.getItemDetails(), EcsReimDetailWriteMapper.class);
    }

    @Override
    public void cancelEcsReimDetailNew(EcsReimDetailDto dto) {
        // 更新申请状态为取消
        LambdaUpdateWrapper<EcsReimDetailDto> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(EcsReimDetailDto::getBusstas, EcsConst.REIM_BUSSTAS_CANCEL)
                .eq(EcsReimDetailDto::getId, dto.getId());
        ecsReimDetailWriteMapper.update(null, updateWrapper);
        // 取消对应流程实例
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                .setProcessId(dto.getProcessInstanceId());
        CommonFeignResult processIns = bpmProcessInstanceFeignApi
                .cancelRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
        if (!StringUtils.equals(processIns.get("code").toString(), "200")) {
            throw new AppException("取消流程实例异常");
        }
    }

    @Override
    public void cancelEcsReimDetailMultiTrip(EcsReimDetailDto dto) {
        //更新申请状态为取消
        LambdaUpdateWrapper<EcsReimDetailDto> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(EcsReimDetailDto::getBusstas, EcsConst.REIM_BUSSTAS_CANCEL)
                .eq(EcsReimDetailDto::getProcessInstanceId, dto.getProcessInstanceId());
        ecsReimDetailWriteMapper.update(null, updateWrapper);
        //取消对应流程实例
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                .setProcessId(dto.getProcessInstanceId());
        CommonFeignResult processIns = bpmProcessInstanceFeignApi.cancelRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
        if (!StringUtils.equals(processIns.get("code").toString(), "200")) {
            throw new AppException("取消流程实例异常");
        }
    }

    /**
     * @param dto
     * @return 返回的错误信息提示
     */
    @Override
    public List<String> ocrIdentifyNew(EcsReimDetailDto dto) {
        // 1.获取上传文件
        List<EcsInvoRcdVo> res = new ArrayList<>();
        // 错误信息
        List<String> errMsgs = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            List<EcsInvoRcdDto> resRcd = new ArrayList<>();
            List<EcsInvoRcdDto> notNullRcds = new ArrayList<>();
            List<EcsInvoRcdDto> nullRcds = new ArrayList<>();
            for (int i = 0; i < dto.getAttFiles().size(); i++) {
                MultipartFile file = dto.getAttFiles().get(i);
                String fileIdentifier = "";
                try {
                    fileIdentifier = generateFileIdentifier(file);
                    // 查询库中是否存在当前文件
                    LambdaQueryWrapper<EcsInvoRcdDto> invoWrapper = Wrappers.lambdaQuery();
                    invoWrapper.eq(EcsInvoRcdDto::getFileIdentifier, fileIdentifier);
                    EcsInvoRcdDto one = ecsInvoRcdReadMapper.selectOne(invoWrapper);
                    if (ObjectUtil.isNotNull(one)) {
                        String errMsg = String.format("发票文件: %s,已记录在库中.状态：%s,所属人工号: %s", file.getOriginalFilename(),
                                InvoStatusEnum.getMessage(one.getState()), one.getCreateUser());
                        // errMsgs.add(file.getOriginalFilename() + " 已记录在库中");
                        // 如果是已报销，或者报销中，需要判断是哪种类型的报销
                        if ((StringUtils.equals(one.getState(), MedConst.TYPE_2)
                                || StringUtils.equals(one.getState(), MedConst.TYPE_4))
                                && StringUtils.isNotEmpty(one.getInvoUsedBy())) {
                            errMsg = errMsg + ",发票被用于：" + one.getInvoUsedBy();
                        }
                        errMsgs.add(errMsg);
                        continue;
                    }
                } catch (Exception e) {
                    fileIdentifier = ULIDUtil.generate();
                }
                // 父发票
                EcsInvoRcdDto ecsInvoRcdDto = getBaseInvoRcdDto(dto.getHospitalId(), dto.getInvoFrom(),
                        dto.getSysUser().getUsername());
                // 设置文件信息
                ecsInvoRcdDto.setAttName(file.getOriginalFilename());
                ecsInvoRcdDto.setFileIdentifier(fileIdentifier);
                String url = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/invo/", file);
                ecsInvoRcdDto.setAtt(url);
                // 判断文件类型
                String originalFilename = file.getOriginalFilename();
                List<String> ocrResultStr = new ArrayList<>();
                // 处理识别结果
                List<EcsInvoRcdDto> ocrDtos = new ArrayList<>();
                if (originalFilename.toLowerCase().endsWith(".pdf")) {
                    // 判断PDF页数
                    try {
                        PDDocument document = PDDocument.load(file.getBytes());
                        int numberOfPages = document.getNumberOfPages();
                        // 一个文件多页
                        ocrResultStr = processPdf(file, MedConst.TYPE_1, numberOfPages);
                    } catch (IOException e) {
                        log.error("获取PDF文件信息失败", e);
                        throw new RuntimeException(e);
                    } catch (Exception e1) {
                        log.error("识别PDF文件失败", e1);
                        throw new RuntimeException(e1);
                    }
                } else {
                    // 直接进行OCR识别
                    String s = alyOcrNew(file, MedConst.TYPE_0, 1);
                    ocrResultStr = Arrays.asList(s);
                }
                ocrDtos = handleOcrStrToDto(dto.getHospitalId(), dto.getInvoFrom(), dto.getSysUser().getUsername(),
                        ocrResultStr);
                // 设置父发票的类型
                if (ocrDtos.size() > 1) {
                    ecsInvoRcdDto.setInvoType("INVOOTHER");
                } else {
                    ecsInvoRcdDto.setInvoType(ocrDtos.get(0).getInvoType());
                }
                ecsInvoRcdDto.setSubInvos(ocrDtos);
                resRcd.add(ecsInvoRcdDto);
            }

            // 判断每个文件的子发票是否需要进行识别，需要进行识别的发票
            ArrayList<EcsInvoRcdDto> insertRecords = new ArrayList<>();
            boolean invoFileRepeat = false;
            List<String> invoKeys = new ArrayList<>();
            for (int i = 0; i < resRcd.size(); i++) {
                EcsInvoRcdDto ecsInvoRcdDto = resRcd.get(i);
                List<EcsInvoRcdDto> subInvos = ecsInvoRcdDto.getSubInvos();

                // 判断OCR识别信息
                boolean subInvoNumNotExist = false; // 发票号码是否存在(统称，实际为发票的唯一标识，不同类型唯一标识构成不同，但都需要存在)
                boolean subInvoExist = false; // 是否库中已存在
                boolean subInvoMsgErr = false; // 是否发票信息规范
                boolean subInvoQrCodeErr = false; // 二维码区信息是否匹配

                for (int j = 0; j < subInvos.size(); j++) {
                    EcsInvoRcdDto subInvoRcdDto = subInvos.get(j);
                    // 判断OCR识别发票的invoNum是否存在
                    if (StringUtils.isEmpty(subInvoRcdDto.getInvoNum())) {
                        subInvoNumNotExist = true;
                        /*String errMsg = ecsInvoRcdDto.getAttName() + "包含发票号码为空的发票";
                        errMsgs.add(errMsg);
                        break;*/
                    }
                    // 判断OCR识别发票是否在库
                    EcsInvoRcdDto sub = checkInvoExist(subInvoRcdDto);
                    if (!Objects.isNull(sub)) {
                        subInvoExist = true;
                        // 查询发票的parent
                        LambdaQueryWrapper<EcsInvoRcdDto> parentWrapper = Wrappers.lambdaQuery();
                        parentWrapper.like(EcsInvoRcdDto::getSubInvoIds, sub.getId());
                        EcsInvoRcdDto rcdParentDto = ecsInvoRcdReadMapper.selectOne(parentWrapper);
                        String errMsg = String.format(
                                ecsInvoRcdDto.getAttName() + "包含已经上传过的发票:发票号%s,发票状态：%s,发票所属人工号:%s",
                                subInvoRcdDto.getInvoNum(), InvoStatusEnum.getMessage(subInvoRcdDto.getState()),
                                subInvoRcdDto.getCreateUser());
                        // String errMsg = ecsInvoRcdDto.getAttName() + "包含已经上传过的发票:发票号";
                        // 如果是已报销，需要判断是哪种类型的报销
                        if ((StringUtils.equals(rcdParentDto.getState(), MedConst.TYPE_2)
                                || StringUtils.equals(rcdParentDto.getState(), MedConst.TYPE_4))
                                && StringUtils.isNotEmpty(rcdParentDto.getInvoUsedBy())) {
                            errMsg = errMsg + ",发票被用于：" + rcdParentDto.getInvoUsedBy();
                        }
                        errMsgs.add(errMsg);
                        break;
                    }
                    // 判断需要校验的发票的二维码区的信息是否有且规范  （05-19） 不进行二维码的校验
                    if (false && StringUtils.equals(MedConst.TYPE_1,
                            InvoiceOcrEnum.getByTypeCode(subInvoRcdDto.getInvoType()).getNeedCheck())
                            && checkQrCodeIdentify(subInvoRcdDto)) {
                        subInvoQrCodeErr = true;
                        String errMsg = ecsInvoRcdDto.getAttName() + "包含二维码信息不匹配的发票";
                        errMsgs.add(errMsg);
                        break;
                    }
                    // 判断需要校验的发票的OCR识别发票信息是否规范 税号和抬头
                    if (StringUtils.equals(MedConst.TYPE_1,
                            InvoiceOcrEnum.getByTypeCode(subInvoRcdDto.getInvoType()).getNeedCheck())
                            && checkInvoInfoNew(subInvoRcdDto)) {
                        subInvoMsgErr = true;
                        String errMsg = ecsInvoRcdDto.getAttName() + "包含信息不规范的发票";
                        errMsgs.add(errMsg);
                        break;
                    }
                    String s = subInvoRcdDto.getInvoCode() + subInvoRcdDto.getInvoNum() + subInvoRcdDto.getInvoDate()
                            + subInvoRcdDto.getInvoCode();
                    if (invoKeys.contains(s)) {
                        invoFileRepeat = true;
                    } else {
                        invoKeys.add(s);
                    }
                }
                if (subInvoNumNotExist) {
                    // 当前文件包含的发票存在没有发票号，设定文件state状态，跳过当前文件
                    ecsInvoRcdDto.setState(EcsConst.INVO_MSG_ERROR);
//                    continue;
                }

                if (subInvoExist) {
                    // 当前文件包含的发票已存在，设定文件state状态，跳过当前文件
                    ecsInvoRcdDto.setState(EcsConst.REPEAT_INVO);
                    continue;
                }

                if (subInvoQrCodeErr) {
                    // 当前文件包含的发票二维码信息不匹配，设定文件状态，跳过当前文件
                    ecsInvoRcdDto.setState(EcsConst.INVO_MSG_ERROR);
                    continue;
                }

                if (subInvoMsgErr) {
                    // 当前文件包含的发票信息不规范，设定文件状态，跳过当前文件
                    ecsInvoRcdDto.setState(EcsConst.INVO_MSG_ERROR);
                    continue;
                }

                if (invoFileRepeat) {
                    throw new AppException("上传文件存在发票重复，请重新确认");
                }

                // 只要文件的子发票不存在于库中，当前文件即可入库，不论信息是否规范或者识别失败
                if (!subInvoExist) {
                    insertRecords.add(ecsInvoRcdDto);
                }
            }

            // 发票核验
            for (int i = 0; i < insertRecords.size(); i++) {
                EcsInvoRcdDto ecsInvoRcdDto = insertRecords.get(i);
                List<EcsInvoRcdDto> subInvos = ecsInvoRcdDto.getSubInvos();
                for (int j = 0; j < subInvos.size(); j++) {
                    boolean subInvoChkFail = false;             //发票是否核验失败
                    boolean subInvoInvalidMark =  false;        //发票是否作废
                    EcsInvoRcdDto subInvoRcdDto = subInvos.get(j);
                    // 获取当前子发票类型，判断是否需要进行发票核验
                    InvoiceOcrEnum subInvoOcr = InvoiceOcrEnum.getByTypeCode(subInvoRcdDto.getInvoType());
                    if (StringUtils.equals(subInvoOcr.getNeedCheck(), MedConst.TYPE_1)) {
                        // 需要核验
                        subInvoRcdDto.setChkTime(DateUtil.getCurrentTime(null));
                        try {
                            EcsInvoiceParam ecsInvoiceParam = new EcsInvoiceParam();
                            ecsInvoiceParam.setInvoiceCode(subInvoRcdDto.getInvoCode());
                            ecsInvoiceParam.setInvoiceNo(subInvoRcdDto.getInvoNum());
                            ecsInvoiceParam.setInvoiceDate(subInvoRcdDto.getInvoDate().replaceAll("[年月日-]", ""));
                            // 判断是否为区块链发票 区块链发票验证时传入的金额为不含税金额
                            if (StringUtils.equals(subInvoRcdDto.getInvoiceKind(), MedConst.TYPE_1)) {
                                ecsInvoiceParam.setInvoiceKind(Integer.parseInt(MedConst.TYPE_1));
                                ecsInvoiceParam.setInvoiceSum(subInvoRcdDto.getInvoiceAmountPreTax());
                            } else {
                                ecsInvoiceParam.setInvoiceSum(new BigDecimal(subInvoRcdDto.getAllValoremTax()));
                            }
                            if (StringUtils.isNotEmpty(subInvoRcdDto.getChkCode())
                                    && subInvoRcdDto.getChkCode().length() == 20) {
                                ecsInvoiceParam.setVerifyCode(
                                        subInvoRcdDto.getChkCode().substring(subInvoRcdDto.getChkCode().length() - 6));
                            } else {
                                ecsInvoiceParam.setVerifyCode(subInvoRcdDto.getChkCode());
                            }
                            log.error("----------------------发票核验参数:-------------" + JSON.toJSONString(ecsInvoiceParam));
                            String post = HttpRequestUtil.post(BeanUtil.beanToMap(ecsInvoiceParam),
                                    invoiceVerifyItfUrl);
                            log.error("----------------------发票核验结果:-------------" + post);
                            // String post =
                            // "{\"code\":\"001\",\"data\":{\"allTax\":\"5901.78\",\"allValoremTax\":\"51300.00\",\"checkCode\":\"25512000000060581415\",\"cyjgxx\":\"查验成功发票一致\",\"detailList\":[{\"allTax\":\"3959.25\",\"detailAmount\":\"30455.75\",\"detailNo\":\"1\",\"expenseItem\":\"\",\"goodsName\":\"*通用设备*反渗膜\",\"netValue\":\"6091.15044247788\",\"num\":\"5\",\"plate_no\":\"\",\"rowNo\":\"1\",\"standard\":\"8040\",\"taxClassifyCode\":\"1090130010000000000\",\"taxDetailAmount\":\"\",\"taxRate\":\"13\",\"taxUnitPrice\":\"\",\"trafficDateEnd\":\"\",\"trafficDateStart\":\"\",\"type\":\"\",\"unit\":\"支\"},{\"allTax\":\"306.48\",\"detailAmount\":\"2357.52\",\"detailNo\":\"2\",\"expenseItem\":\"\",\"goodsName\":\"*通用设备*石英砂\",\"netValue\":\"294.690265486726\",\"num\":\"8\",\"plate_no\":\"\",\"rowNo\":\"2\",\"standard\":\"细砂\",\"taxClassifyCode\":\"1090130010000000000\",\"taxDetailAmount\":\"\",\"taxRate\":\"13\",\"taxUnitPrice\":\"\",\"trafficDateEnd\":\"\",\"trafficDateStart\":\"\",\"type\":\"\",\"unit\":\"包\"},{\"allTax\":\"382.87\",\"detailAmount\":\"2945.13\",\"detailNo\":\"3\",\"expenseItem\":\"\",\"goodsName\":\"*通用设备*1000碘值活性炭\",\"netValue\":\"736.283185840708\",\"num\":\"4\",\"plate_no\":\"\",\"rowNo\":\"3\",\"standard\":\"25kg/包\",\"taxClassifyCode\":\"1090130010000000000\",\"taxDetailAmount\":\"\",\"taxRate\":\"13\",\"taxUnitPrice\":\"\",\"trafficDateEnd\":\"\",\"trafficDateStart\":\"\",\"type\":\"\",\"unit\":\"包\"},{\"allTax\":\"1195.54\",\"detailAmount\":\"9196.46\",\"detailNo\":\"4\",\"expenseItem\":\"\",\"goodsName\":\"*通用设备*阳树脂\",\"netValue\":\"766.371681415929\",\"num\":\"12\",\"plate_no\":\"\",\"rowNo\":\"4\",\"standard\":\"C100E\",\"taxClassifyCode\":\"1090130010000000000\",\"taxDetailAmount\":\"\",\"taxRate\":\"13\",\"taxUnitPrice\":\"\",\"trafficDateEnd\":\"\",\"trafficDateStart\":\"\",\"type\":\"\",\"unit\":\"包\"},{\"allTax\":\"53.96\",\"detailAmount\":\"415.04\",\"detailNo\":\"5\",\"expenseItem\":\"\",\"goodsName\":\"*通用设备*滤芯\",\"netValue\":\"59.2920353982301\",\"num\":\"7\",\"plate_no\":\"\",\"rowNo\":\"5\",\"standard\":\"30\\\"\",\"taxClassifyCode\":\"1090130010000000000\",\"taxDetailAmount\":\"\",\"taxRate\":\"13\",\"taxUnitPrice\":\"\",\"trafficDateEnd\":\"\",\"trafficDateStart\":\"\",\"type\":\"\",\"unit\":\"支\"},{\"allTax\":\"3.68\",\"detailAmount\":\"28.32\",\"detailNo\":\"6\",\"expenseItem\":\"\",\"goodsName\":\"*通用设备*滤芯\",\"netValue\":\"28.3185840707965\",\"num\":\"1\",\"plate_no\":\"\",\"rowNo\":\"6\",\"standard\":\"20\\\"\",\"taxClassifyCode\":\"1090130010000000000\",\"taxDetailAmount\":\"\",\"taxRate\":\"13\",\"taxUnitPrice\":\"\",\"trafficDateEnd\":\"\",\"trafficDateStart\":\"\",\"type\":\"\",\"unit\":\"支\"}],\"inspectionAmount\":\"5\",\"invalidMark\":\"N\",\"invoiceCode\":\"\",\"invoiceDate\":\"********\",\"invoiceMoney\":\"45398.22\",\"invoiceNumber\":\"25512000000060581415\",\"invoiceType\":\"32\",\"machineCode\":\"\",\"note\":\"销方开户银行：中国银行股份有限公司成都锦城支行
                            // 银行账号：************\",\"purchaserAddressOrPhone\":\"中江县凯江镇大北街96号\",\"purchaserBankAndNumber\":\"中国银行中江县伍城北路支行
                            // ************\",\"purchaserName\":\"中江县人民医院\",\"purchaserTaxpayerNumber\":\"12510521451164362Y\",\"receiveName\":\"\",\"salerAddressOrPhone\":\"成都高新区科园南路88号2栋4层403号
                            // 028-********\",\"salerBankAccount\":\"销方开户银行：中国银行股份有限公司成都锦城支行
                            // 银行账号：************\",\"salerBankAndNumber\":\"销方开户银行：中国银行股份有限公司成都锦城支行
                            // 银行账号：************\",\"salerName\":\"四川国药医工工程技术有限公司\",\"salerTaxpayerNumber\":\"91510100MA63B0H84Q\",\"sellerUnitOrIndividual\":\"四川国药医工工程技术有限公司\"},\"msg\":\"成功\"}";
                            if (StringUtils.isNotEmpty(post)) {
                                // post = post.substring(1, post.length() - 1).replaceAll("\\\\", "");
                                // post = post.substring(1, post.length() - 1);
                                // EcsInvoiceRes ecsInvoiceRes = JSON.parseObject(post, EcsInvoiceRes.class);
                                // EcsInvoiceRes ecsInvoiceRes = new
                                // ObjectMapper().readValue(StringEscapeUtils.unescapeJson(post).replaceAll("^\"|\"$",
                                // ""), EcsInvoiceRes.class);
                                // EcsInvoiceRes ecsInvoiceRes =
                                // JSON.parseObject(StringEscapeUtils.unescapeJson(post).replaceAll("^\"|\"$",
                                // ""), EcsInvoiceRes.class);
                                EcsInvoiceRes ecsInvoiceRes = JSON.parseObject(
                                        StringEscapeUtils.unescapeJson(post).replaceAll("^\"|\"$", ""),
                                        EcsInvoiceRes.class);
                                if (ecsInvoiceRes != null) {
                                    switch (ecsInvoiceRes.getCode()) {
                                        case "200":
                                        case "201":
                                            //判断作废标志
                                            Map map = JSON.parseObject(ecsInvoiceRes.getData(), Map.class);
                                            Map dmap = (Map<String,Object>) map.get("data");
                                            String o = (String) dmap.get("invalidMark");
                                            if (StringUtils.equals(o, EcsConst.INVOICE_INVALID_MARK_Y) || StringUtils.equals(o,EcsConst.INVOICE_INVALID_MARK_8)) {
                                                subInvoRcdDto.setChkState(EcsConst.INVO_CHECK_STATE_INVALID);
                                                subInvoRcdDto.setState(EcsConst.VERIFY_INVALID);
                                                subInvoRcdDto.setChkData(ecsInvoiceRes.getMessage());
                                                subInvoInvalidMark = true;
                                            } else {
                                                subInvoRcdDto.setChkState(EcsConst.INVO_CHECK_STATE_VALID);
                                                subInvoRcdDto.setChkData(ecsInvoiceRes.getData());
                                            }
                                            // insertRecords.add(d);
                                            break;
                                        default:
                                            // 设置核验状态
                                            subInvoRcdDto.setChkState(EcsConst.INVO_CHECK_STATE_INVALID);
                                            // 设置状态
                                            subInvoRcdDto.setState(EcsConst.VERIFY_ERROR);
                                            subInvoRcdDto.setChkData(ecsInvoiceRes.getMessage());
                                            subInvoChkFail = true;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            e.printStackTrace();
                            // 核验不通过
                            subInvoRcdDto.setChkState(EcsConst.INVO_CHECK_STATE_INVALID);
                            // 设置状态
                            subInvoRcdDto.setState(EcsConst.VERIFY_ERROR);
                            subInvoRcdDto.setChkData(e.getMessage());
                            subInvoChkFail = true;
                        }
                    }
                    if (subInvoChkFail) {
                        // 如果存在校验失败的子发票，则文件设定为失败，跳过当前文件
                        ecsInvoRcdDto.setState(EcsConst.VERIFY_ERROR);
                        ecsInvoRcdDto.setChkState(MedConst.TYPE_0);
                        break;
                    }
                    if (subInvoInvalidMark) {
                        // 如果存在作废发票，则文件设定为作废，跳过当前文件
                        ecsInvoRcdDto.setState(EcsConst.VERIFY_INVALID);
                        ecsInvoRcdDto.setChkState(MedConst.TYPE_0);
                        break;
                    }
                }
            }

            // 插入发票核验数据及核验数据明细
            List<EcsInvoChkRcdEntity> rcdEntityList = new ArrayList<>();
            List<EcsInvoChkRcdDetailEntity> rcdDetailEntityList = new ArrayList<>();
            for (int i = 0; i < insertRecords.size(); i++) {
                EcsInvoRcdDto ecsInvoRcdDto = insertRecords.get(i);
                List<EcsInvoRcdDto> subInvos = ecsInvoRcdDto.getSubInvos();
                // 子发票的id
                List<Integer> subInvoIds = new ArrayList<>();
                subInvos.stream().forEach(subInvo -> {
                    // data信息不为空，且不为区块链发票
                    if (StringUtils.equals(subInvo.getChkState(), MedConst.TYPE_1)
                            && !Objects.isNull(subInvo.getChkData())
                            && !StringUtils.equals(subInvo.getInvoiceKind(), MedConst.TYPE_1)) {
                        JSONObject jsonObject = JSON.parseObject(subInvo.getChkData());
                        EcsInvoChkRcdEntity rcdEntity = JSON.parseObject(jsonObject.getString("data"),
                                EcsInvoChkRcdEntity.class);
                        // 存储发票核验基本信息
                        subInvo.setPurchaserName(rcdEntity.getPurchaserName());
                        subInvo.setPurchaserTaxpayerNumber(rcdEntity.getPurchaserTaxpayerNumber());
                        subInvo.setInvoiceMoney(rcdEntity.getInvoiceMoney());
                        subInvo.setAllValoremTax(rcdEntity.getAllValoremTax());
                        ecsInvoRcdWriteMapper.insert(subInvo);
                        rcdEntity.setInvoRcdId(subInvo.getId().intValue());
                        subInvoIds.add(subInvo.getId().intValue());
                        rcdEntityList.add(rcdEntity);
                        rcdEntity.getDetailList().forEach(rcdDetail -> {
                            rcdDetail.setInvoRcdId(subInvo.getId().intValue());
                            rcdDetailEntityList.add(rcdDetail);
                        });
                    } else {
                        ecsInvoRcdWriteMapper.insert(subInvo);
                        subInvoIds.add(subInvo.getId().intValue());
                    }
                });
                BatchUtil.batch("insertInvoChkRcd", rcdEntityList, EcsInvoRcdWriteMapper.class);
                BatchUtil.batch("insertInvoChkRcdDetail", rcdDetailEntityList, EcsInvoRcdWriteMapper.class);

                // 父发票，入库
                String subInvoIdsStr = subInvoIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                ecsInvoRcdDto.setSubInvoIds(subInvoIdsStr);
                // 设置金额为所有子发票金额合计
                BigDecimal total = subInvos.stream().map(EcsInvoRcdDto::getAllValoremTax).map(str -> {
                    try {
                        return new BigDecimal(str);
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                }).reduce(BigDecimal.ZERO, BigDecimal::add);
                ecsInvoRcdDto.setAllValoremTax(total.toString());
                ecsInvoRcdWriteMapper.insert(ecsInvoRcdDto);
            }
        }
        return errMsgs;
    }

    /**
     * 阿里云OCR识别
     *
     * @param file   识别文件
     * @param isPDF  文件类型
     * @param pageNo 页数(只针对与PDF格式文件)
     */
    private String alyOcrNew(MultipartFile file, String isPDF, Integer pageNo) {
        try {
            String boundary = "---------------------------1234567890"; // 定义boundary
            URL url = new URL(ocrItfUrlNew);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            OutputStream outputStream = connection.getOutputStream();
            PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true);

            // 添加其他参数
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"type\"").append("\r\n");
            writer.append("\r\n");
            writer.append("MixedInvoice").append("\r\n"); // 设置参数值

            // 添加 pageNo 参数
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"pageNo\"").append("\r\n");
            writer.append("\r\n");
            writer.append(String.valueOf(pageNo)).append("\r\n"); // 这里的 "1" 是 pageNo 的值，可以根据需要修改

            // 添加文件数据
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"")
                    .append("\r\n");
            writer.append("Content-Type: " + HttpURLConnection.guessContentTypeFromName(file.getName())).append("\r\n");
            writer.append("\r\n");
            writer.flush();

            InputStream fileInputStream = file.getInputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.flush();
            fileInputStream.close();

            writer.append("\r\n");
            writer.append("--" + boundary + "--").append("\r\n");
            writer.close();

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                response.append(line);
            }

            reader.close();
            outputStream.close();

            // 处理JSON响应
            String post = response.toString();
            log.error("当前识别的发票返回结果string-------------" + post);
            if (StringUtils.isNotEmpty(post)) {
                // EcsOCRRes ecsOCRRes = JSON.parseObject(post, EcsOCRRes.class);
                JSONObject jsonObject = JSON.parseObject(post);
                if (jsonObject != null) {
                    if ("200".equals(jsonObject.getString("code"))) {
                        return jsonObject.getJSONArray("data").toJSONString();
                        // BeanUtils.copyProperties(ecsOCRRes.getData(), ecsInvoRcdDto);
                    }
                }
            }
            /*
             * if (StringUtils.isNotEmpty(post)) {
             * EcsOCRRes ecsOCRRes = JSON.parseObject(post, EcsOCRRes.class);
             * if(ecsOCRRes != null){
             * if ("200".equals(ecsOCRRes.getCode())) {
             * BeanUtils.copyProperties(ecsOCRRes.getData(), ecsInvoRcdDto);
             * }
             * }
             * }
             */

            connection.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 处理 PDF 的每一页，并调用第三方 OCR 接口
     */
    public CompletableFuture<String> processPage(MultipartFile file, String isPDF, int pageSize) {
        return CompletableFuture.supplyAsync(() -> alyOcrNew(file, isPDF, pageSize), executorService);
    }

    /**
     * 处理整个 PDF 文件
     */
    public List<String> processPdf(MultipartFile file, String isPDF, int totalPages) throws Exception {
        // 创建 CompletableFuture 列表，每个任务处理一页
        List<CompletableFuture<String>> futureList = IntStream.rangeClosed(1, totalPages) // 页码从 1 开始
                .mapToObj(page -> processPage(file, isPDF, page)) // 异步处理每一页
                .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futureList.toArray(new CompletableFuture[0]));

        // 在所有任务完成后，收集结果
        CompletableFuture<List<String>> allResults = allFutures.thenApply(v -> futureList.stream()
                .map(CompletableFuture::join) // 获取每个任务的结果
                .collect(Collectors.toList()));

        // 返回所有结果
        return allResults.get();
    }

    private EcsInvoRcdDto getBaseInvoRcdDto(String hospitalId, String invoFrom, String crter) {
        EcsInvoRcdDto ecsInvoRcdDto = new EcsInvoRcdDto();
        // 设置基本发票记录数据
        // ecsInvoRcdDto.setAttName(file.getOriginalFilename());
        ecsInvoRcdDto.setCreateTime(DateUtil.getCurrentTime(null));
        ecsInvoRcdDto.setHospitalId(hospitalId);
        // 默认核验成功
        ecsInvoRcdDto.setChkState(MedConst.TYPE_1);
        ecsInvoRcdDto.setState(EcsConst.CAN_REIM);
        // ecsInvoRcdDto.setFileIdentifier(fileIdentifier);
        ecsInvoRcdDto.setRcdIdentifier(ULIDUtil.generate());
        // 设置发票来源
        ecsInvoRcdDto.setInvoFrom(invoFrom);
        // 设置创建人
        ecsInvoRcdDto.setCreateUser(crter);
        // String url = OSSUtil.uploadFile(OSSConst.BUCKET_ECS, "reim/invo/", file);
        // ecsInvoRcdDto.setAtt(url);
        return ecsInvoRcdDto;
    }

    private List<EcsInvoRcdDto> handleOcrStrToDto(String hospitalId, String invoFrom, String crter,
            List<String> ocrResultStr) {
        List<EcsInvoRcdDto> ocrDtos = new ArrayList<>();
        List<String> repeatedInvo = new ArrayList<>();
        try {
            for (int j = 0; j < ocrResultStr.size(); j++) {
                String ocrStr = ocrResultStr.get(j);
                if (StringUtils.isEmpty(ocrStr)) {
                    throw new AppException("识别结果为空");
                }
                // 生成发票记录识别，一页可能识别多张票据
                JSONArray subImages = JSONArray.parseArray(ocrStr);
                for (int k = 0; k < subImages.size(); k++) {
                    JSONObject jsonObject = subImages.getJSONObject(k);
                    // 获取类型
                    String type = jsonObject.getString("type");
                    // JSONObject kvInfo = jsonObject.getJSONObject("kvInfo");
                    String kvInfoStr = jsonObject.getString("kvInfo");
                    // 二维码信息
                    JSONArray qrCodeArr = jsonObject.getJSONArray("qrCodeInfo");
                    EcsInvoRcdDto rcdDto = OcrDataToInvoRcdUtil.dataToInvoRcd(hospitalId, invoFrom, crter, type,
                            kvInfoStr);
                    if (!Objects.isNull(qrCodeArr) && !qrCodeArr.isEmpty()) {
                        rcdDto.setQrCodeData(qrCodeArr.getJSONObject(0).getString("data"));
                    } else {
                        rcdDto.setQrCodeData("");
                    }
                    // 一个文件中，多页，只添加不重复发票
                    String s = rcdDto.getInvoCode() + rcdDto.getInvoNum() + rcdDto.getInvoDate() + rcdDto.getChkCode();
                    if (!repeatedInvo.contains(s)) {
                        repeatedInvo.add(s);
                        ocrDtos.add(rcdDto);
                    }
                }
            }
        } catch (Exception e) {
            log.error("转换识别结果失败", e);
            throw new AppException(e.getMessage());
        }
        return ocrDtos;
    }

    private EcsInvoRcdDto checkInvoExist(EcsInvoRcdDto ecsInvoRcdDto) {
        InvoiceOcrEnum byTypeCode = InvoiceOcrEnum.getByTypeCode(ecsInvoRcdDto.getInvoType());
        // 判断识别的发票是否存在于库，不同类型的发票查询条件不同
        LambdaQueryWrapper<EcsInvoRcdDto> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EcsInvoRcdDto::getInvoNum, ecsInvoRcdDto.getInvoNum());
        // 以下几类发票需要添加日期查询
        switch (byTypeCode) {
            case INVOICE:
            case CARINVOICE:
            case ROLLTICKET:
            case NONTAXINVOICE:
            case AIRITINERARY:
            case TOLLINVOICE:
            case COMMONPRINTEDINVOICE:
                wrapper.eq(EcsInvoRcdDto::getInvoDate, ecsInvoRcdDto.getInvoDate());
                break;
            // case QUOTAINVOICE:
            // case TRAINTICKET:
            // case BANKACCEPTANCE:
            // case BUSSHIPTICKET:

            default:
                break;
        }

        return ecsInvoRcdReadMapper.selectOne(wrapper);
    }

    /**
     * 向合同系统传递报销详细信息
     * 
     * @param dto       报销明细DTO
     * @param paymentId 付款条件ID
     */
    private void updateContractPaymentInfo(EcsReimDetailDto dto, Integer paymentId) {
        try {
            // 获取报销人信息
            HrmUser hrmUser = dto.getSysUser().getHrmUser();
            String reimbursePersonName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName()
                    : dto.getSysUser().getNickname();
            String reimburseDeptName = StringUtils.isNotEmpty(hrmUser.getHrmOrgName()) ? hrmUser.getHrmOrgName() : "";
            String reimburseTime = DateUtil.getCurrentTime(null);
            String reimburseNo = dto.getAuditBchno(); // 审核批次号作为报销单号

            // 调用合并后的接口，一次性更新所有信息
            FeignExecuteUtil.execute(cmsFeignService.updatePaymentTermsReimId(
                    paymentId,
                    Integer.valueOf(dto.getId().intValue()),
                    reimbursePersonName,
                    reimburseDeptName,
                    reimburseTime,
                    reimburseNo));
        } catch (Exception e) {
            log.error("更新合同付款条件报销信息失败", e);
            // 这里只记录日志不抛出异常，避免影响主流程
        }
    }

    private void updateContractPaymentInfo(EcsReimContractTask entity, Integer reimId) {
        if (entity != null && entity.getPaymentId() != null) {
            cmsFeignService.updatePaymentTermsReimId(
                    entity.getPaymentId(),
                    reimId,
                    null,
                    null,
                    null,
                    null);
        }
    }

    @Override
    public void purcUploadReceipt(EcsReimDetailDto dto) {
        //报销id不能为空
        if (CollectionUtil.isEmpty(dto.getIds())){
            throw new AppException("报销id不能为空");
        }

        //更新报销状态为已付款，
        LambdaUpdateWrapper<EcsReimDetailDto> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(EcsReimDetailDto::getBusstas, EcsConst.REIM_BUSSTAS_PAID)
                .set(EcsReimDetailDto::getPayMethod,dto.getPayMethod())
                .in(EcsReimDetailDto::getId,dto.getIds());

        //如果是非现金支付
        if (StringUtils.equals(dto.getPayMethod(),MedConst.TYPE_0)) {
            if (CollectionUtil.isEmpty(dto.getPayRcptFiles())) {
                throw new AppException("报销付款证明文件不能为空");
            }
            //保存付款证明文件
            String attCode = ULIDUtil.generate();
            List<List<String>> ossPaths = OSSUtil.getOSSPaths(dto.getPayRcptFiles(),OSSConst.BUCKET_ECS,"reim/item/");
            List<EcsReimFileRecordDto> rcds = new ArrayList<>();
            for (int i = 0; i < ossPaths.get(0).size(); i++) {
                EcsReimFileRecordDto rcd = new EcsReimFileRecordDto();
                rcd.setAttCode(attCode);
                rcd.setAtt(ossPaths.get(0).get(i));
                rcd.setAttName(ossPaths.get(1).get(i));
                rcd.setType(EcsConst.FILE_TYPE_PAY);
                rcds.add(rcd);
            }
            BatchUtil.batch("insertFileRecord",rcds, EcsReimFileRecordWriteMapper.class);

            //生成上传付款文件映射表
            EcsDrugPayDetailDto payDto = new EcsDrugPayDetailDto();
            payDto.setAttCode(attCode);
            payDto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
            payDto.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            ecsDrugPayDetailWriteMapper.insert(payDto);

            //设置payid
            updateWrapper.set(EcsReimDetailDto::getPayRcptId,payDto.getId());
        }

        ecsReimDetailWriteMapper.update(null, updateWrapper);
    }

    @Override
    public List<EcsReimDetailVo> queryLoanReim(EcsReimDetailDto dto) {
        List<EcsReimDetailVo> res = new ArrayList<>();
        //查询当前员工的所有已生成凭证的借款记录  (暂时不筛选已经生成凭证的借款记录，防止重复关联。在凭证生成处再进行判断)
        LambdaQueryWrapper<EcsReimDetailDto> queryWrapper = Wrappers.lambdaQuery(EcsReimDetailDto.class);
        queryWrapper.eq(EcsReimDetailDto::getType,MedConst.TYPE_13)
                .eq(EcsReimDetailDto::getBusstas,MedConst.TYPE_1)
                .eq(EcsReimDetailDto::getHasPz,MedConst.TYPE_1)
                .eq(EcsReimDetailDto::getAppyer,dto.getAppyer());
//        List<EcsReimDetailDto> loanReims = ecsReimDetailReadMapper.selectList(queryWrapper);
        List<EcsReimDetailVo> loanReims = ecsReimDetailReadMapper.queryListNew(dto);
        //获取借款报销的id
        List<Long> collect = loanReims.stream().map(item -> item.getId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            //查询已经对借款进行过冲抵的报销
            LambdaQueryWrapper<EcsReimDetailDto> loanQueryWrapper = Wrappers.lambdaQuery(EcsReimDetailDto.class);
            loanQueryWrapper.eq(EcsReimDetailDto::getIsLoan,MedConst.TYPE_1)
                    .in(EcsReimDetailDto::getLoanReimId,collect);
            List<EcsReimDetailDto> ecsReimDetailDtos = ecsReimDetailReadMapper.selectList(loanQueryWrapper);
            loanReims.stream().forEach(item -> {
                //查询冲抵过当前借款的报销的总金额
                BigDecimal sum = ecsReimDetailDtos.stream().filter(item1 -> item1.getLoanReimId().intValue() == item.getId().intValue())
                        .map(item1 -> item1.getLoanAmt()).reduce(BigDecimal.ZERO, BigDecimal::add);
                //未被冲抵金额
                BigDecimal noLoanAmt = item.getSum().subtract(sum);
                if (noLoanAmt.compareTo(BigDecimal.ZERO) > 0) {
                    EcsReimDetailVo vo = new EcsReimDetailVo();
                    BeanUtils.copyProperties(item,vo);
                    vo.setNoLoanAmt(noLoanAmt);
                    res.add(vo);
                }
            });
        }
        return res;
    }

    @Autowired
    private EcsActigCfgReadService ecsActigCfgReadService;
    @Override
    public List<EcsReimItemDetail> uploadReimDetailFile(MultipartFile file) {
        ExcelReader reader = null;
        try {
            reader = ExcelUtil.getReader(file.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        reader.addHeaderAlias("报销摘要", "reimAbst");
        reader.addHeaderAlias("报销科室", "deptName");
        reader.addHeaderAlias("类型", "typeName");
        reader.addHeaderAlias("会计科目", "actigName");
        reader.addHeaderAlias("往来单位", "relCoName");
        reader.addHeaderAlias("金额", "amt");
        return reader.readAll(EcsReimItemDetail.class);
    }
}
